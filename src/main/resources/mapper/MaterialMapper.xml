<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.material.dao.MaterialMapper">



    <select id="getExportMaterial"
            resultType="com.fotile.exportcenter.cmscenter.material.pojo.MaterialExportDto">
        SELECT t.id as materialId,
        t.title,
        t.type,
        t.node_id,
        t.sort,
        t.status,
        t.share_count,
        t.favorite_count,
        t.helpful_count,
        t.unhelpful_count,
        t.created_date,
        t.modified_date,
        t.modified_username
        FROM cmscenter.t_material t
        inner JOIN cmscenter.t_material_content tc ON t.id = tc.material_id and tc.is_deleted =0
        <if test="material.basicMaterialContentId != null ">
            AND tc.basic_material_id =  #{material.basicMaterialContentId}
        </if>
        <if test="material.content != null and material.content != ''">
            inner JOIN cmscenter.t_basic_material_content tbc on tc.material_content_id = tbc.id and tbc.is_deleted =0
            AND tbc.content like CONCAT('%',#{material.content},'%')
        </if>
        <if test="material.productList != null and material.productList.size() > 0">
            inner join cmscenter.t_basic_material_product tbmp
            on tc.basic_material_id = tbmp.material_id and tbmp.is_deleted = 0
            and tbmp.type = 1
            and tbmp.ref_id in
            <foreach collection="material.productList" item="product" separator="," open="(" close=")">
                #{product}
            </foreach>
        </if>
        <if test="material.modelList != null and material.modelList.size() > 0">
            inner join cmscenter.t_basic_material_product tbmp2
            on tc.basic_material_id = tbmp2.material_id and tbmp2.is_deleted = 0
            and tbmp2.type = 2
            and tbmp2.ref_id in
            <foreach collection="material.modelList" item="model" separator="," open="(" close=")">
                #{model}
            </foreach>
        </if>
        WHERE t.is_deleted = 0
        <if test="materialIdList!=null and materialIdList.size() > 0">
            and t.id in
            <foreach collection="materialIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and EXISTS(
            select tn.id from (
                select tn1.id
                from
                cmscenter.t_material_category_node tn1
                where tn1.is_deleted = 0 and tn1.tree_id = #{material.treeId}
                <if test="material.categoryNodeId!=null">
                    and tn1.id = #{material.categoryNodeId}
                </if>
                UNION
                select tn2.id
                from
                cmscenter.t_material_category_node tn2
                where tn2.is_deleted = 0 and tn2.tree_id = #{material.treeId}
                <if test="material.categoryNodeId!=null">
                    and tn2.parent_node_id = #{material.categoryNodeId}
                </if>
                UNION
                select tn3.id
                from
                cmscenter.t_material_category_node tn2
                INNER JOIN cmscenter.t_material_category_node tn3 on tn2.id = tn3.parent_node_id and tn3.tree_id = #{material.treeId} and tn3.is_deleted = 0
                where tn2.is_deleted = 0 and tn2.tree_id = #{material.treeId}
                <if test="material.categoryNodeId!=null">
                    and tn2.parent_node_id = #{material.categoryNodeId}
                </if>
                union
                select tn4.id
                from cmscenter.t_material_category_node tn4
                INNER JOIN cmscenter.t_material_category_node tn3 on tn3.id = tn4.parent_node_id and tn3.is_deleted = 0 and tn3.tree_id = #{material.treeId}
                INNER JOIN cmscenter.t_material_category_node tn2 on tn2.id = tn3.parent_node_id and tn2.is_deleted = 0 and tn2.tree_id = #{material.treeId}
                <if test="material.categoryNodeId!=null">
                    and tn2.parent_node_id = #{material.categoryNodeId}
                </if>
                where tn4.is_deleted = 0 and tn4.tree_id =  #{material.treeId}
            ) tn where tn.id = t.node_id
        )
        <if test="material.title != null and material.title!=''">
            AND t.title like CONCAT('%',#{material.title},'%')
        </if>
        <if test="material.notMaterialIdList != null and material.notMaterialIdList.size()>0">
            and t.id not in
            <foreach collection="material.notMaterialIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="material.type != null ">
            AND t.type = #{material.type}
        </if>
        <if test="material.materialId != null ">
            AND t.id = #{material.materialId}
        </if>
        <if test="material.status != null ">
            AND t.status = #{material.status}
        </if>
        <if test="material.recommendFlag != null ">
            AND t.recommend_flag =  #{material.recommendFlag}
        </if>
        group by t.id
        order by
        <choose>
            <when test="material.sortType == 1">
                t.sort
                <choose>
                    <when test="material.conditionsType == 'DESC'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </when>
            <when test="material.sortType == 2">
                t.modified_date
                <choose>
                    <when test="material.conditionsType == 'DESC'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </when>
            <when test="material.sortType == 3">
                t.created_date
                <choose>
                    <when test="material.conditionsType == 'DESC'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                t.modified_date desc
            </otherwise>
        </choose>
        limit #{material.page},#{material.size}
    </select>
    <select id="materialTreeName" resultType="java.lang.String">
        select title
        from cmscenter.t_material_category_tree
        where is_deleted = 0 and id = #{treeId} and status = 1
    </select>
    <select id="findBasicMaterial"
            resultType="com.fotile.exportcenter.cmscenter.material.pojo.BasicMaterialDto">
        select tmc.material_id,
                tmc.basic_material_id,
               tbm.source_desc_names,
               tbmc.type,
               tbmc.material_cover_url,
               tmc.material_url,
               tmc.link_address,
               tbmc.content,
               tmc.sort,
               tmc.landing_page_title,
               tmc.display_picture_url,
               tmc.link_description,
               tbm.source_desc_names
        from cmscenter.t_material_content tmc
        inner join cmscenter.t_basic_material tbm on tmc.basic_material_id = tbm.id and tbm.is_deleted =0
        inner join cmscenter.t_basic_material_content tbmc on tmc.material_content_id = tbmc.id and tbmc.is_deleted =0
        where tmc.is_deleted = 0 and tmc.material_id in
        <foreach collection="materialIdList" item="materialId" close=")" open="(" separator=",">
            #{materialId}
        </foreach>
        order by tmc.sort
    </select>
    <select id="materialNodeName" resultType="com.fotile.exportcenter.cmscenter.material.pojo.MaterialNodeDto">
        select id,full_path_name nodeName
        from cmscenter.t_material_category_node
        where is_deleted = 0 and id in <foreach collection="NodeIdList" separator="," open="(" close=")" item="nodeId">
                    #{nodeId}
                </foreach>
    </select>
    <select id="findBasicMaterialProduct"
            resultType="com.fotile.exportcenter.cmscenter.material.pojo.BasicMaterialProductDto">
        select tmc.material_id,tbmp.type,tbmp.ref_id,tbmp.ref_code,tbmp.ref_name
        from cmscenter.t_basic_material_product tbmp
        inner join cmscenter.t_material_content tmc on tmc.basic_material_id = tbmp.material_id and tmc.is_deleted = 0
        where tbmp.is_deleted =0
        and tmc.material_id in
        <foreach collection="materialIdList" item="materialId" close=")" open="(" separator=",">
            #{materialId}
        </foreach>
    </select>
    <select id="findBasicMaterialTag"
            resultType="com.fotile.exportcenter.cmscenter.material.pojo.BasicMaterialTagDto">
        select tmc.material_id,tbmt.tag_id
        from cmscenter.t_basic_material_tag tbmt
        inner join cmscenter.t_material_content tmc on tmc.basic_material_id = tbmt.material_id and tmc.is_deleted = 0
        where tbmt.is_deleted =0 and tbmt.tag_type = 1
        and tmc.material_id in
        <foreach collection="materialIdList" item="materialId" close=")" open="(" separator=",">
            #{materialId}
        </foreach>
    </select>


</mapper>