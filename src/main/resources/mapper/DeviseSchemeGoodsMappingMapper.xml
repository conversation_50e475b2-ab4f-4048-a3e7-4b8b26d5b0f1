<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeGoodsMappingMapper">
    <resultMap id="BaseResultMap" type="com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeGoodsMapping">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="scheme_id" jdbcType="BIGINT" property="schemeId"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id ,
        is_deleted,
        created_by,
        created_date,
        modified_by,
        modified_date,
        scheme_id,
        goods_id,
        goods_code,
        goods_name

    </sql>

    <select id="selectListBySchemeIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cmscenter.t_devise_scheme_goods_mapping
        where is_deleted = 0 and scheme_id
        <foreach collection="list" item="item" separator="," close=")" open="in (">
            #{item}
        </foreach>
    </select>

</mapper>