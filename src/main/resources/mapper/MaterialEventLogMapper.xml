<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.MaterialEventLogMapper">


    <select id="getSchemeMaterialStatistical"
            resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.SchemeMaterialStatisticalDTO">
        SELECT
        log.relation_parent_id relationParentId,
        log.event_type eventType,
        count(1) eventCount
        FROM
        cmscenter.t_material_event_log log
        WHERE
        log.is_deleted = 0
        AND log.event_type in(10,20,30,50)
        <if test="relationParentIds != null and relationParentIds.size() != 0">
            AND log.relation_parent_id IN
            <foreach collection="relationParentIds" open="(" close=")" item="relationParentId" separator=",">
                #{relationParentId}
            </foreach>
        </if>
        AND log.relation_source_type = #{relationSourceType}
        group by log.relation_parent_id,log.event_type
    </select>

</mapper>