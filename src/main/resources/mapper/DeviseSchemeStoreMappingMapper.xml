<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeStoreMappingMapper">
    <resultMap id="BaseResultMap" type="com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeStoreMapping">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="scheme_id" jdbcType="BIGINT" property="schemeId"/>
        <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId"/>
        <result column="store_code" jdbcType="VARCHAR" property="storeCode"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="abbreviation" jdbcType="VARCHAR" property="abbreviation"/>
    </resultMap>

    <sql id="Base_Column_List">
        id ,
        is_deleted,
        created_by,
        created_date,
        modified_by,
        modified_date,
        scheme_id,
        store_org_id,
        store_code,
        store_name,
        abbreviation
    </sql>


    <select id="selectListBySchemeIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cmscenter.t_devise_scheme_store_mapping
        where is_deleted = 0 and scheme_id
        <foreach collection="list" item="item" separator="," close=")" open="in (">
            #{item}
        </foreach>
    </select>


</mapper>