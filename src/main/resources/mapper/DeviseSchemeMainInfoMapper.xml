<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeMainInfoMapper">

    <select id="findSchemeList" resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeVO">
        SELECT t.* from (
        select
        tmi.*
        from cmscenter.t_devise_scheme_main_info tmi
        where tmi.is_deleted = 0
        and (type = 1 or tmi.excellent_flag = 1)
        UNION
        select
        tmi.*
        from cmscenter.t_devise_scheme_main_info tmi
        where tmi.is_deleted = 0
        and type in (2,3)
        and tmi.excellent_flag = 0
        <if test="authCompanyIds != null and authCompanyIds.size() != 0">
            and company_id
            <foreach collection="authCompanyIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>
        <if test="authStoreOrgIds != null and authStoreOrgIds.size() != 0">
            and
            tmi.id in (
            SELECT scheme_id FROM cmscenter.`t_devise_scheme_store_mapping` tsm where tsm.is_deleted = 0 and
            tsm.store_org_id
            <foreach collection="authStoreOrgIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
            )
        </if>
        ) t
        <if test="createdStationList != null and createdStationList.size() != 0">
            left join orgcenter.t_salesman ts on ts.is_deleted = 0 and ts.id = t.created_charge_user_id
        </if>
        where t.is_deleted =0
        <if test="projectCode != null and projectCode != '' ">
            and t.project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        <if test="projectName != null and projectName !='' ">
            and t.project_name like CONCAT('%',#{projectName},'%')
        </if>
        <if test="type != null">
            and t.type = #{type,jdbcType=TINYINT}
        </if>
        <if test="ids != null and ids.size() != 0">
            and t.id
            <foreach collection="ids" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>
        <if test="companyArea != null and companyArea.size() != 0">
            and t.company_id
            <foreach collection="companyArea" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size() != 0">
            and t.company_id
            <foreach collection="companyIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>
        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and t.id in (
            SELECT scheme_id FROM cmscenter.`t_devise_scheme_store_mapping` tsm where tsm.is_deleted = 0 and
            tsm.store_org_id
            <foreach collection="storeOrgIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
            )
        </if>

        <if test="provinceId != null">
            and t.province_id = #{provinceId,jdbcType=BIGINT}
        </if>
        <if test="cityId != null">
            and t.city_id = #{cityId,jdbcType=BIGINT}
        </if>
        <if test="countyId != null">
            and t.county_id = #{countyId,jdbcType=BIGINT}
        </if>
        <if test="villageName != null and villageName !=''">
            and t.village_name like CONCAT('%',#{villageName},'%')
        </if>
        <if test="cluesId != null">
            and t.clues_id = #{cluesId,jdbcType=BIGINT}
        </if>
        <if test="status != null">
            and t.status = #{status,jdbcType=TINYINT}
        </if>
        <if test="createdName != null and createdName != ''">
            and (AES_DECRYPT(UNHEX(t.created_name), #{aesKey}) like concat('%', #{createdName}, '%')
            or t.created_charge_code like concat('%', #{createdName}, '%')
            )
        </if>
        <if test="createdStationList != null and createdStationList.size() != 0">
            and ts.station in
            <foreach collection="createdStationList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="createdChargeUserIdList != null and createdChargeUserIdList.size() != 0">
            and t.created_charge_user_id in
            <foreach collection="createdChargeUserIdList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="createdStartDate != null">
            and t.created_date <![CDATA[>=]]> #{createdStartDate}
        </if>
        <if test="createdEndDate != null">
            and t.created_date <![CDATA[<=]]> #{createdEndDate}
        </if>

        <if test="amountStart != null">
            and t.deal_order_amount <![CDATA[>=]]> #{amountStart}
        </if>
        <if test="amountEnd != null">
            and t.deal_order_amount <![CDATA[<=]]> #{amountEnd}
        </if>

        <if test="groupedMap != null">
            <foreach collection="groupedMap.entrySet()" index="key" item="value" >
                AND t.id IN (
                SELECT
                DISTINCT(scheme_id)
                FROM
                cmscenter.`t_devise_scheme_label_mapping` tlm
                WHERE
                tlm.is_deleted = 0
                and tlm.field_id = #{key}
                AND tlm.property_parameter_id
                <foreach collection="value" item="item" separator="," close=")" open="in (">
                    #{item.propertyParameterId}
                </foreach>
                )
            </foreach>
        </if>
        <if test="(goodsCategoryIdList != null and goodsCategoryIdList.size() != 0) or (goodsIdList != null and goodsIdList.size() != 0)">
            and t.id in
            (
            SELECT scheme_id
            FROM cmscenter.`t_devise_scheme_goods_mapping` tgm where tgm.is_deleted = 0
            <if test="goodsCategoryIdList != null and goodsCategoryIdList.size() != 0">
                and tgm.goods_category_id
                <foreach collection="goodsCategoryIdList" item="item" separator="," close=")" open="in (">
                    #{item}
                </foreach>
            </if>
            <if test="goodsIdList != null and goodsIdList.size() != 0">
                and tgm.goods_id
                <foreach collection="goodsIdList" item="item" separator="," close=")" open="in (">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        <if test="excellentFlag != null">
            and t.excellent_flag = #{excellentFlag}
        </if>
        <if test="planName != null and planName != ''">
            and t.id in
            (SELECT scheme_id FROM cmscenter.`t_devise_scheme_kujiale_mapping` tkm where tkm.is_deleted = 0
            and tkm.plan_name like CONCAT('%', #{planName}, '%'))
        </if>
        <if test="(storeChannelCodeList!=null and storeChannelCodeList.size()>0) or
        (storeChannelSubdivisionCodeList!=null and storeChannelSubdivisionCodeList.size()>0) or (storeChannelSubDivisionCodeFlag!=null)">
            and exists (
            select tsm.id
            from orgcenter.t_store ts
            inner join orgcenter.t_salesman tsm on tsm.store_id = ts.org_id and tsm.is_deleted = 0
            inner join usercenter.user_entity_extend u on tsm.id = u.salesman_id  and u.is_deleted =0
            where ts.is_deleted = 0 and u.user_entity_id = t.created_by
            <if test="storeChannelCodeList!=null and storeChannelCodeList.size()>0">
                and ts.store_channel_code in
                <foreach collection="storeChannelCodeList" item="code" close=")" open="(" separator=",">
                    #{code}
                </foreach>

            </if>
            <if test="storeChannelSubDivisionCodeFlag != null and storeChannelSubDivisionCodeFlag == 1">
                and (ts.store_sub_channel_code is null
                <if test="storeChannelSubdivisionCodeList != null and storeChannelSubdivisionCodeList.size() > 0">
                    or ts.store_sub_channel_code in
                    <foreach collection="storeChannelSubdivisionCodeList" item="code" close=")" open="(" separator=",">
                        #{code}
                    </foreach>
                </if>
                )
            </if>
            <if test="storeChannelSubDivisionCodeFlag == null and storeChannelSubdivisionCodeList != null and storeChannelSubdivisionCodeList.size() > 0">
                and ts.store_sub_channel_code in
                <foreach collection="storeChannelSubdivisionCodeList" item="code" close=")" open="(" separator=",">
                    #{code}
                </foreach>
            </if>
            group by t.created_by
            )
        </if>
        order by t.created_date desc
        limit #{offSize},#{pageSize}
    </select>
    <select id="getStoreInfo" resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO">
        select
        tsm.created_by,
        tsm.store_org_id,
        cc.name as storeChannelName,
        cc.code storeChannelCode,
        s.store_sub_channel_code as storeSubChannelCode
        from cmscenter.t_devise_scheme_main_info tsm
        inner join usercenter.user_entity_extend u on u.user_entity_id = tsm.created_by  and u.is_deleted =0
        inner join orgcenter.t_salesman ts on ts.id = u.salesman_id and ts.is_deleted = 0
        inner join orgcenter.t_store s on ts.store_id = s.org_id and s.is_deleted = 0
        left join orgcenter.channel_category cc ON s.store_type = cc.id and cc.is_deleted = 0
        where tsm.is_deleted = 0 and tsm.id in
        <foreach collection="ids" separator="," open="(" close=")" item="id" >
            #{id}
        </foreach>
        group by tsm.created_by
    </select>

    <select id="findSalesmanStation" resultType="com.fotile.exportcenter.common.KvLs">
        select s.id         as 'key',
               d.value_name as value
        from orgcenter.t_salesman s
            left join systemcenter.dic d on d.type_code = 'gw' and d.id = s.station
            and s.id in
        <foreach collection="salesmanIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="querySalesmanLabel" resultType="com.fotile.exportcenter.client.pojo.org.SalesmanLabel">
        select tsl.id,
        tsl.salesman_id,
        tsl.type,
        tsl.label_value,
        dic.value_name as labelName
        from orgcenter.t_salesman_label tsl
        left join systemcenter.dic dic on dic.is_deleted = 0 and dic.value_code = tsl.label_value and dic.type_code = 'capability_label'
        where tsl.is_deleted = 0
        and tsl.type = 1
        and tsl.salesman_id in
        <foreach collection="salesmanIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
        order by tsl.id asc
    </select>

    <select id="querySalesmanIdByLabelValue" resultType="java.lang.Long">
        select distinct salesman_id
        from orgcenter.t_salesman_label where is_deleted = 0
        and type = 1
        <if test="labelValueList != null and labelValueList.size() != 0">
            and label_value in
            <foreach item="labelValue" collection="labelValueList" separator="," open="(" close=")">
                #{labelValue}
            </foreach>
        </if>
    </select>
</mapper>