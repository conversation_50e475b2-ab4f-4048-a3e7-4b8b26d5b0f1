<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.TAttributeLevelConfigMapper">
  <resultMap id="BaseResultMap" type="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig">
    <!--@mbg.generated-->
    <!--@Table `marketingcenter`.`t_attribute_level_config`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_id" jdbcType="VARCHAR" property="fieldId" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="attribute_value" jdbcType="VARCHAR" property="attributeValue" />
    <result column="attribute_sort" jdbcType="BIGINT" property="attributeSort" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="parent_attribute_id" jdbcType="VARCHAR" property="parentAttributeId" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `field_id`, `attribute_id`, `attribute_value`, `attribute_sort`, `level`, `parent_attribute_id`, 
    `category_code`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `marketingcenter`.`t_attribute_level_config`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`field_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.fieldId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`attribute_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.attributeId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`attribute_value` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.attributeValue,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`attribute_sort` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.attributeSort,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`level` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.level,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`parent_attribute_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.parentAttributeId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`category_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.categoryCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_attribute_level_config`
    (`field_id`, `attribute_id`, `attribute_value`, `attribute_sort`, `level`, `parent_attribute_id`, 
      `category_code`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fieldId,jdbcType=VARCHAR}, #{item.attributeId,jdbcType=VARCHAR}, #{item.attributeValue,jdbcType=VARCHAR}, 
        #{item.attributeSort,jdbcType=BIGINT}, #{item.level,jdbcType=TINYINT}, #{item.parentAttributeId,jdbcType=VARCHAR}, 
        #{item.categoryCode,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, 
        #{item.createdDate,jdbcType=TIMESTAMP}, #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_attribute_level_config`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `field_id`,
      `attribute_id`,
      `attribute_value`,
      `attribute_sort`,
      `level`,
      `parent_attribute_id`,
      `category_code`,
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{fieldId,jdbcType=VARCHAR},
      #{attributeId,jdbcType=VARCHAR},
      #{attributeValue,jdbcType=VARCHAR},
      #{attributeSort,jdbcType=BIGINT},
      #{level,jdbcType=TINYINT},
      #{parentAttributeId,jdbcType=VARCHAR},
      #{categoryCode,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `field_id` = #{fieldId,jdbcType=VARCHAR},
      `attribute_id` = #{attributeId,jdbcType=VARCHAR},
      `attribute_value` = #{attributeValue,jdbcType=VARCHAR},
      `attribute_sort` = #{attributeSort,jdbcType=BIGINT},
      `level` = #{level,jdbcType=TINYINT},
      `parent_attribute_id` = #{parentAttributeId,jdbcType=VARCHAR},
      `category_code` = #{categoryCode,jdbcType=VARCHAR},
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_attribute_level_config`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="fieldId != null and fieldId != ''">
        `field_id`,
      </if>
      <if test="attributeId != null and attributeId != ''">
        `attribute_id`,
      </if>
      <if test="attributeValue != null and attributeValue != ''">
        `attribute_value`,
      </if>
      <if test="attributeSort != null">
        `attribute_sort`,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="parentAttributeId != null and parentAttributeId != ''">
        `parent_attribute_id`,
      </if>
      <if test="categoryCode != null and categoryCode != ''">
        `category_code`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fieldId != null and fieldId != ''">
        #{fieldId,jdbcType=VARCHAR},
      </if>
      <if test="attributeId != null and attributeId != ''">
        #{attributeId,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null and attributeValue != ''">
        #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="attributeSort != null">
        #{attributeSort,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="parentAttributeId != null and parentAttributeId != ''">
        #{parentAttributeId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null and categoryCode != ''">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="fieldId != null and fieldId != ''">
        `field_id` = #{fieldId,jdbcType=VARCHAR},
      </if>
      <if test="attributeId != null and attributeId != ''">
        `attribute_id` = #{attributeId,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null and attributeValue != ''">
        `attribute_value` = #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="attributeSort != null">
        `attribute_sort` = #{attributeSort,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="parentAttributeId != null and parentAttributeId != ''">
        `parent_attribute_id` = #{parentAttributeId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null and categoryCode != ''">
        `category_code` = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

    <select id="findByFiledIds" resultType="com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO">
        select
        id,
        field_id,
        attribute_id,
        attribute_value,
        attribute_sort,
      `level`,
        parent_attribute_id,
        category_code
        from t_attribute_level_config
        where is_deleted = 0 and field_id in (
        <foreach collection="fieldIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>


    <select id="selectListByFieldIdAndLevel" resultType="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig">
        select
        id,
        field_id,
        attribute_id,
        attribute_value,
        attribute_sort
        from t_attribute_level_config
        where is_deleted = 0 and field_id  = #{fieldId}
      <if test="level != null">
        and `level` =#{level}
      </if>
        <if test="parentAttributeId != null and parentAttributeId != ''">
          and parent_attribute_id = #{parentAttributeId}
        </if>
    </select>

    <select id="selectListByFieldIdAndAttributeAndLevel" resultType="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig">
        select
            id,
            field_id,
            attribute_id,
            attribute_value,
            attribute_sort,
            parent_attribute_id
        from t_attribute_level_config
        where is_deleted = 0 and field_id  = #{fieldId} and attribute_id =#{attributeId}
      <if test="level != null">
        and `level` =#{level}
      </if>
        limit 1;
    </select>

  <select id="selectAllByFiledIdAndAttributeId" resultType="com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO">
       select
        id,
        field_id,
        attribute_id,
        attribute_value,
        attribute_sort,
        parent_attribute_id,
        category_code
        from t_attribute_level_config
        where is_deleted = 0 and field_id  = #{fieldId}
        <if test="attributeId != null and attributeId != ''">
          and attribute_id =#{attributeId}
        </if>
  </select>

  <select id="selectOneTweConfigByFiledIdAndAttributeId" resultType="com.fotile.exportcenter.marketing.pojo.dto.SelectOneTweOutDto">
    SELECT  t.attribute_value tweValue,a.attribute_value oneValue
    FROM `t_attribute_level_config` `t`
    INNER JOIN `t_attribute_level_config` `a` ON `t`.`parent_attribute_id`=`a`.`attribute_id`
    WHERE t.attribute_id = #{attributeId} and  t.field_id=#{fieldId}
  </select>


  <select id="getFollowServerList" resultType="com.fotile.exportcenter.marketing.pojo.dto.FollowServerDTO">
    SELECT
      tlc.field_id,
      tlc.attribute_id,
      tlc.attribute_value,
      tlc.parent_attribute_id,
      tt.parent_attribute_value,
      tlc.category_code,
      tlc.attribute_sort
    FROM
      `marketingcenter`.`t_attribute_level_config` tlc,(
      SELECT
        attribute_id parent_attribute_id,
        attribute_value parent_attribute_value
      FROM
        `marketingcenter`.`t_attribute_level_config` tc
      WHERE
        tc.is_deleted = 0
        AND tc.field_id = 'follow_server'
        AND tc.`level` = 1
    ) tt
    WHERE
      tlc.is_deleted = 0
      AND tlc.field_id = 'follow_server'
      AND tlc.`level` = 2
      AND tlc.parent_attribute_id = tt.parent_attribute_id
    ORDER BY
      tlc.category_code,
      tlc.parent_attribute_id,
      tlc.attribute_sort
  </select>

  <select id="selectAllByFiledIdAndAttributeIds" resultType="com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO">
    select
    id,
    field_id,
    attribute_id,
    attribute_value,
    attribute_sort,
    parent_attribute_id,
    category_code
    from t_attribute_level_config
    where is_deleted = 0
    <if test="attributeIds != null and attributeIds.size > 0 ">
      and attribute_id in
          <foreach collection="attributeIds" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
    </if>
  </select>


  <select id="selectListByFieldIdAndLevelAndParentIds" resultType="com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig">
    select
    id,
    field_id,
    attribute_id,
    attribute_value,
    attribute_sort
    from t_attribute_level_config
    where is_deleted = 0 and field_id  = #{fieldId}
    <if test="level != null">
      and `level` =#{level}
    </if>
    <if test="parentAttributeIds != null and parentAttributeIds.size > 0 ">
      and parent_attribute_id in
      <foreach collection="parentAttributeIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>



</mapper>