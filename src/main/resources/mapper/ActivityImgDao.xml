<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.ActivityImgDao">


    <!-- 根据id，类型查询URL-->
    <select id="selectUrlByIds" resultType="com.fotile.exportcenter.marketing.pojo.dto.LogPictureDto">
        select
        pmm.source_id sourceId,
        pm.cover_url coverUrl,
        pmm.picture_type pictureType
        from marketingcenter.picture_marketing pm
        left join marketingcenter.picture_marketing_mapping pmm on pm.id = pmm.picture_marketing_id and pmm.is_deleted = 0
        <where>
            pm.is_deleted = 0
            <if test="ids != null and ids.size > 0">
                and pmm.source_id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and pmm.source_table_name = #{sourceTableName}
            <if  test = "  pictureType != null  and pictureType != ''  ">
                and pmm.picture_type = #{pictureType}
            </if>
        </where>
    </select>
</mapper>