<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.TQywxCustomerCluesRelationDao">

    <resultMap type="com.fotile.exportcenter.marketing.pojo.entity.TQywxCustomerCluesRelation"
               id="TQywxCustomerCluesRelationMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="externalUserid" column="external_userid" jdbcType="VARCHAR"/>
        <result property="unionId" column="union_id" jdbcType="VARCHAR"/>
        <result property="cluesId" column="clues_id" jdbcType="INTEGER"/>
        <result property="customerPhone" column="customer_phone" jdbcType="VARCHAR" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="OTHER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="getCustomerCluesBycluesIds" resultMap="TQywxCustomerCluesRelationMap">
        select
        id, account_id, external_userid, union_id, clues_id, customer_phone, user_id, is_deleted, created_by, created_date, modified_by,
        modified_date
        from marketingcenter.t_qywx_customer_clues_relation
        <where>
            is_deleted = 0
            <if test="cluesIds != null">
                and clues_id in
                <foreach collection="cluesIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryQywxCustomerRelationByexternalUserid"
            resultType="com.fotile.exportcenter.marketing.pojo.vo.QywxCustomerRelationVo">
        select external_userid,name from customercenter.t_qywx_cutomer_user_relation
        where is_deleted = 0 and name is not null and name != '' and external_userid in
        <foreach collection="externalUseridList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by external_userid
    </select>

</mapper>