<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.UserCluesAssistMappingMapper">
  <resultMap id="BaseResultMap" type="com.fotile.exportcenter.marketing.pojo.entity.UserCluesAssistMapping">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="clues_id" jdbcType="BIGINT" property="cluesId" />
    <result column="assist_user_id" jdbcType="BIGINT" property="assistUserId" />
    <result column="assist_user_name" jdbcType="VARCHAR" property="assistUserName" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
    <result column="assist_user_code" jdbcType="VARCHAR" property="assistUserCode" />
    <result column="assist_user_phone" jdbcType="VARCHAR" property="assistUserPhone" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  
	  	  	      	id,
       	  	      	is_deleted,
       	  	      	created_by,
       	  	      	created_date,
       	  	      	modified_by,
       	  	      	modified_date,
       	  	      	clues_id,
       	  	      	assist_user_id,
       	  	      	assist_user_name,
       	  	      	assist_user_code,
       	  	      	assist_user_phone,
       	  	    	  	source_type
       	  	
  </sql>
  

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_clues_assist_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectListByCluesId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_clues_assist_mapping
    where is_deleted = 0 and clues_id = #{cluesId}
  </select>

  <select id="selectListByCluesIds"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_clues_assist_mapping
    where is_deleted = 0
    <if test="cluesIds != null and cluesIds.size > 0 ">
      and clues_id in (
      <foreach collection="cluesIds" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
  </select>


  <select id="selectOneByCluesIdAndUserId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_clues_assist_mapping
    where is_deleted = 0 and clues_id = #{cluesId} and assist_user_id = #{userId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_clues_assist_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>

                                                                
</mapper>