<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.TCluesDesignInfoDao">

    <resultMap type="com.fotile.exportcenter.marketing.pojo.entity.TCluesDesignInfo" id="TCluesDesignInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="cluesId" column="clues_id" jdbcType="INTEGER"/>
        <result property="appointmentTime" column="appointment_time" jdbcType="TIMESTAMP"/>
        <result property="appointmentQuantum" column="appointment_quantum" jdbcType="VARCHAR"/>
        <result property="measureTime" column="measure_time" jdbcType="TIMESTAMP"/>
        <result property="designTime" column="design_time" jdbcType="TIMESTAMP"/>
        <result property="caseType" column="case_type" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="reviseName" column="revise_name" jdbcType="VARCHAR"/>
        <result property="reviseTime" column="revise_time" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>



    <!-- 通过线索ids查询多条数据 -->
    <select id="queryByCluesIds" resultMap="TCluesDesignInfoMap">
        select
            id, clues_id, appointment_time, appointment_quantum, measure_time, design_time, case_type, remark, revise_name, revise_time, created_by, created_date, modified_by, modified_date, is_deleted
        from t_clues_design_info
        where is_deleted = 0
        <if test="cluesIds != null and cluesIds.size > 0">
            and clues_id in
            <foreach collection="cluesIds" item="cluesId" open="(" separator="," close=")">
                #{cluesId}
            </foreach>
        </if>
    </select>


</mapper>

