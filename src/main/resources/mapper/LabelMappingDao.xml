<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.LabelMappingDao">
    <!-- 修改状态为失效 -->
    <delete id="delLabelMapping">
        delete from marketingcenter.label_marketing_mapping
        where source_table_name = #{sourceTableName} and source_id = #{sourceId}
    </delete>
    <resultMap id="FindLabelByNameOutDto" type="com.fotile.exportcenter.marketing.pojo.dto.FindLabelBySourceIdOutDto">
        <result property="id" column="id"/>
        <result property="sourceTableName" column="sourceTableName"/>
        <result property="sourceId" column="sourceId"/>
        <result property="labelSort" column="labelSort"/>
        <result property="labelId" column="labelId"/>
        <result property="name" column="name"/>
    </resultMap>
    <select id="findLabelBySourceId" resultMap="FindLabelByNameOutDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,slm.label_sort labelSort,slm.label_id labelId,sl.name name
        from marketingcenter.label_marketing_mapping slm
        left join marketingcenter.label_marketing sl on sl.id =slm.label_id
        where slm.source_table_name = #{sourceTableName} and
        slm.source_id = #{sourceId} and slm.is_deleted = 0
    </select>

<!--    <insert id = "batchInsertLabelMapping" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into marketingcenter.label_marketing_mapping  (is_deleted,source_id,label_id,source_table_name)-->
<!--        values-->
<!--        <foreach collection="list" item="labelMapping" index="index" separator=",">-->
<!--            (-->
<!--            0,#{labelMapping.sourceId} ,#{labelMapping.labelId},#{labelMapping.sourceTableName}-->
<!--            )-->
<!--        </foreach>-->
<!--    </insert>-->

    <select id="findLabelNameById" resultMap="FindLabelByNameOutDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,
        slm.label_sort labelSort,
        slm.label_id labelId,
        sl.name name
        from marketingcenter.label_marketing_mapping slm
        left join marketingcenter.label_marketing sl on sl.id =slm.label_id
        <where>
            slm.source_table_name = #{sourceTableName}
            and slm.is_deleted = 0
            <if test=" labelIds != null">
                and slm.source_id in(
                <foreach collection="labelIds" index="id" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>
    <select id="findLabelNameByIds" resultMap="FindLabelByNameOutDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,
        slm.label_sort labelSort,
        slm.label_id labelId,
        sl.name name
        from marketingcenter.label_marketing_mapping slm
        left join marketingcenter.label_marketing sl on sl.id =slm.label_id
        <where>
            slm.source_table_name = #{sourceTableName}
            and slm.is_deleted = 0
            <if test=" ids != null ">
                and slm.source_id in(
                <foreach collection="ids" index="id" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>