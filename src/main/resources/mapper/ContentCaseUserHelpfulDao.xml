<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.ContentCaseUserHelpfulDao">

    <select id="getSchemeCaseHelpful"
            resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.SchemeCaseStatisticalDTO">
        SELECT
        content_id as `caseId`,
        content_type as `caseType`,
        count(1) as `caseCount`
        FROM
        cmscenter.content_case_user_helpful
        WHERE
        is_helpful = 1 AND is_deleted = 0
        and content_type in (5,6)
        <if test="caseIds != null and caseIds.size > 0">
            AND content_id IN
            <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
        </if>
        GROUP BY
        content_id,content_type
    </select>

</mapper>