<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.marketing.dao.TCluesLabelMappingMapper">
  <resultMap id="BaseResultMap" type="com.fotile.exportcenter.marketing.pojo.entity.TCluesLabelMappingEntity">
    <!--@mbg.generated-->
    <!--@Table `marketingcenter`.`t_clues_label_mapping`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="clues_id" jdbcType="BIGINT" property="cluesId" />
    <result column="label_category" jdbcType="TINYINT" property="labelCategory" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="label_value" jdbcType="VARCHAR" property="labelValue" />
    <result column="label_code" jdbcType="VARCHAR" property="labelCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, 
    `clues_id`, `label_category`, `label_id`, `label_value`, `label_code`
  </sql>



  <select id="selectListByCluesId" resultMap="BaseResultMap">

    select * from `marketingcenter`.`t_clues_label_mapping` where is_deleted = 0 and clues_id = #{cluesId}

  </select>

  <select id="selectListByCluesIds" resultMap="BaseResultMap">
    select * from `marketingcenter`.`t_clues_label_mapping`
    <where>
      is_deleted = 0
      and clues_id in
          <foreach collection="cluesIds" item="item" open="(" separator="," close=")">
             #{item}
          </foreach>
    </where>

  </select>

  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `marketingcenter`.`t_clues_label_mapping`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`clues_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.cluesId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`label_category` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelCategory,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`label_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`label_value` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelValue,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`label_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `marketingcenter`.`t_clues_label_mapping`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`clues_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.cluesId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.cluesId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`label_category` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelCategory != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelCategory,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`label_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`label_value` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelValue != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelValue,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`label_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelCode != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.labelCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_clues_label_mapping`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `clues_id`, 
      `label_category`, `label_id`, `label_value`, `label_code`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.cluesId,jdbcType=BIGINT}, 
        #{item.labelCategory,jdbcType=TINYINT}, #{item.labelId,jdbcType=BIGINT}, #{item.labelValue,jdbcType=VARCHAR}, 
        #{item.labelCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.exportcenter.marketing.pojo.entity.TCluesLabelMappingEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_clues_label_mapping`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `clues_id`,
      `label_category`,
      `label_id`,
      `label_value`,
      `label_code`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{cluesId,jdbcType=BIGINT},
      #{labelCategory,jdbcType=TINYINT},
      #{labelId,jdbcType=BIGINT},
      #{labelValue,jdbcType=VARCHAR},
      #{labelCode,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `clues_id` = #{cluesId,jdbcType=BIGINT},
      `label_category` = #{labelCategory,jdbcType=TINYINT},
      `label_id` = #{labelId,jdbcType=BIGINT},
      `label_value` = #{labelValue,jdbcType=VARCHAR},
      `label_code` = #{labelCode,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.exportcenter.marketing.pojo.entity.TCluesLabelMappingEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `marketingcenter`.`t_clues_label_mapping`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="cluesId != null">
        `clues_id`,
      </if>
      <if test="labelCategory != null">
        `label_category`,
      </if>
      <if test="labelId != null">
        `label_id`,
      </if>
      <if test="labelValue != null and labelValue != ''">
        `label_value`,
      </if>
      <if test="labelCode != null and labelCode != ''">
        `label_code`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cluesId != null">
        #{cluesId,jdbcType=BIGINT},
      </if>
      <if test="labelCategory != null">
        #{labelCategory,jdbcType=TINYINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
      <if test="labelValue != null and labelValue != ''">
        #{labelValue,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null and labelCode != ''">
        #{labelCode,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cluesId != null">
        `clues_id` = #{cluesId,jdbcType=BIGINT},
      </if>
      <if test="labelCategory != null">
        `label_category` = #{labelCategory,jdbcType=TINYINT},
      </if>
      <if test="labelId != null">
        `label_id` = #{labelId,jdbcType=BIGINT},
      </if>
      <if test="labelValue != null and labelValue != ''">
        `label_value` = #{labelValue,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null and labelCode != ''">
        `label_code` = #{labelCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>