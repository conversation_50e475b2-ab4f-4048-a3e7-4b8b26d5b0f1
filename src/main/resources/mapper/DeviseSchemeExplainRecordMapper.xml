<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeExplainRecordMapper">
    <resultMap id="BaseResultMap" type="com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeExplainRecord">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="scheme_id" jdbcType="BIGINT" property="schemeId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId"/>
        <result column="store_code" jdbcType="VARCHAR" property="storeCode"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="abbreviation" jdbcType="VARCHAR" property="abbreviation"/>
        <result column="charge_user_id" jdbcType="BIGINT" property="chargeUserId"/>
        <result column="charge_user_name" jdbcType="VARCHAR" property="chargeUserName"
                typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
        <result column="charge_code" jdbcType="VARCHAR" property="chargeCode"/>
        <result column="charge_phone" jdbcType="VARCHAR" property="chargePhone"
                typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="duration_time" jdbcType="INTEGER" property="durationTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_name" jdbcType="VARCHAR" property="createdName"
                typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="revise_time" jdbcType="TIMESTAMP" property="reviseTime"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="explain_clues_id" jdbcType="BIGINT" property="explainCluesId"/>
        <result column="need_clues_flag" jdbcType="INTEGER" property="needCluesFlag"/>
        <result column="abnormal_end_type" jdbcType="INTEGER" property="abnormalEndType"/>
        <result column="pause_time" jdbcType="TIMESTAMP" property="pauseTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id ,
        is_deleted,
        created_by,
        created_date,
        modified_by,
        modified_date,
        scheme_id,
        company_id,
        company_name,
        store_org_id,
        store_code,
        store_name,
        abbreviation,
        charge_user_id,
        charge_user_name,
        charge_code,
        charge_phone,
        start_time,
        end_time,
        duration_time,
        remark,
        created_name,
        status,
        revise_time,
        source,
        explain_clues_id,
        need_clues_flag,
        pause_time,
        abnormal_end_type

    </sql>

    <select id="selectListBySchemeIds" resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.ExplainRecordDTO">
        select
        scheme_id schemeId,
        count(1) as 'count',
        sum(duration_time) as durationTime,
        GROUP_CONCAT(explain_clues_id) as explain_clues_ids
        from cmscenter.t_devise_scheme_explain_record ter
        where ter.is_deleted =0
        and ter.scheme_id
        <foreach collection="list" item="item" separator="," close=")" open="in (">
            #{item}
        </foreach>
        and ter.status = 2
        GROUP BY ter.scheme_id
    </select>


    <select id="selectDealCluesCountBySchemeIds"
            resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.DealCluesCountDTO">
        select
        scheme_id schemeId,
        count(DISTINCT explain_clues_id ) as 'dealCluesCount'
        from cmscenter.t_devise_scheme_explain_record ter
        where ter.is_deleted =0
        and ter.scheme_id
        <foreach collection="list" item="item" separator="," close=")" open="in (">
            #{item}
        </foreach>
        and ter.status = 2
        and ter.detail_order_amount > 0
        GROUP BY ter.scheme_id
    </select>

    <select id="findRecordListForWeb" resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeExplainRecordVO">
        SELECT ser.id,
        t.project_code,
        t.project_name,
        t.type,
        t.province_id,
        t.province_name,
        t.city_id,
        t.city_name,
        t.county_id,
        t.county_name,
        t.village_id,
        t.village_name,
        t.created_date,
        t.company_name createdCompanyName,
        t.created_name createdName,
        t.created_charge_code createdCode,
        t.clues_id,
        t.status,
        t.excellent_flag,
        ser.company_id,
        ser.company_name,
        ser.store_org_id,
        ser.store_code,
        ser.store_name,
        ser.abbreviation,
        ser.charge_user_id,
        ser.charge_user_name,
        ser.charge_code,
        ser.charge_phone,
        ser.start_time,
        ser.end_time,
        ser.created_name createdName1,
        ser.scheme_id,
        ser.source,
        ser.explain_clues_id,
        ser.detail_order_amount,
        ser.related_clues_time,
        ser.abnormal_end_type,
        ser.need_clues_flag,
        ser.no_clues_type,
        ser.duration_time
        FROM cmscenter.`t_devise_scheme_explain_record` ser
        LEFT JOIN
        (select
        tmi.*
        from cmscenter.t_devise_scheme_main_info tmi
        where tmi.is_deleted = 0
        and (type = 1 or tmi.excellent_flag = 1)
        UNION
        select
        tmi.*
        from cmscenter.t_devise_scheme_main_info tmi
        where tmi.is_deleted = 0
        and type in (2,3)
        and tmi.excellent_flag = 0
        <if test="authCompanyIds != null and authCompanyIds.size() != 0">
            and company_id
            <foreach collection="authCompanyIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>
        <if test="authStoreOrgIds != null and authStoreOrgIds.size() != 0">
            and
            tmi.id in (
            SELECT scheme_id FROM cmscenter.`t_devise_scheme_store_mapping` tsm
            where tsm.is_deleted = 0 and
            tsm.store_org_id
            <foreach collection="authStoreOrgIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
            )
        </if>
        ) t
        on ser.scheme_id = t.id
        <if test="chargeUserStationList != null and chargeUserStationList.size() != 0">
            left join orgcenter.t_salesman ts on ts.is_deleted = 0 and ts.id = ser.charge_user_id
        </if>
        where t.is_deleted =0
        and ser.status = 2
        <if test="authCompanyIds != null and authCompanyIds.size() != 0">
            and ser.company_id
            <foreach collection="authCompanyIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="authStoreOrgIds != null and authStoreOrgIds.size() != 0">
            and ser.store_org_id
            <foreach collection="authStoreOrgIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="ids != null and ids.size() != 0">
            and ser.id
            <foreach collection="ids" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="projectCode != null and projectCode != '' ">
            and t.project_code = #{projectCode,jdbcType=VARCHAR}
        </if>

        <if test="projectName != null and projectName !='' ">
            and t.project_name like CONCAT('%',#{projectName},'%')
        </if>

        <if test="type != null">
            and t.type = #{type,jdbcType=TINYINT}
        </if>

        <if test="excellentFlag != null">
            and t.excellent_flag = #{excellentFlag}
        </if>

        <if test="chargeUserIds != null and chargeUserIds.size() != 0">
            and ser.charge_user_id
            <foreach collection="chargeUserIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="chargeUserStationList != null and chargeUserStationList.size() != 0">
            and ts.station in
            <foreach collection="chargeUserStationList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="companyArea != null and companyArea.size() != 0">
            and ser.company_id
            <foreach collection="companyArea" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="companyIds != null and companyIds.size() != 0">
            and ser.company_id
            <foreach collection="companyIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and ser.store_org_id
            <foreach collection="storeOrgIds" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="storeByConditionsList != null and storeByConditionsList.size() != 0">
            and ser.store_org_id
            <foreach collection="storeByConditionsList" item="item" separator="," close=")" open="in (">
                #{item}
            </foreach>
        </if>

        <if test="relationCluesFlag != null">
            <choose>
                <when test="relationCluesFlag == 1">
                    and explain_clues_id is not null
                </when>
                <otherwise>
                    and explain_clues_id is null
                </otherwise>
            </choose>
        </if>

        <if test="source != null">
            and ser.source = #{source}
        </if>

        <if test="explainCluesId != null">
            and ser.explain_clues_id = #{explainCluesId}
        </if>

        <if test="explainTimesStart != null">
            and ser.duration_time <![CDATA[>=]]> #{explainTimesStart}
        </if>
        <if test="explainTimesEnd != null">
            and ser.duration_time <![CDATA[<=]]> #{explainTimesEnd}
        </if>

        <if test="explainStartDate != null">
            and ser.start_time <![CDATA[>=]]> #{explainStartDate}
        </if>
        <if test="explainEndDate != null">
            and ser.start_time <![CDATA[<=]]> #{explainEndDate}
        </if>
        <if test="(storeChannelCodeList!=null and storeChannelCodeList.size()>0) or
        (storeChannelSubdivisionCodeList!=null and storeChannelSubdivisionCodeList.size()>0) or (storeChannelSubDivisionCodeFlag!=null)">
            and exists (
            select ts.org_id
            from orgcenter.t_store ts
            where ts.is_deleted = 0 and ts.org_id = ser.store_org_id
            <if test="storeChannelCodeList!=null and storeChannelCodeList.size()>0">
                and ts.store_channel_code in
                <foreach collection="storeChannelCodeList" item="code" close=")" open="(" separator=",">
                    #{code}
                </foreach>

            </if>
            <if test="storeChannelSubDivisionCodeFlag != null and storeChannelSubDivisionCodeFlag == 1">
                and (ts.store_sub_channel_code is null
                <if test="storeChannelSubdivisionCodeList != null and storeChannelSubdivisionCodeList.size() > 0">
                    or ts.store_sub_channel_code in
                    <foreach collection="storeChannelSubdivisionCodeList" item="code" close=")" open="(" separator=",">
                        #{code}
                    </foreach>
                </if>
                )
            </if>
            <if test="storeChannelSubDivisionCodeFlag == null and storeChannelSubdivisionCodeList != null and storeChannelSubdivisionCodeList.size() > 0">
                and ts.store_sub_channel_code in
                <foreach collection="storeChannelSubdivisionCodeList" item="code" close=")" open="(" separator=",">
                    #{code}
                </foreach>
            </if>
            )
        </if>
        order by ser.created_date desc
        limit #{offSize},#{pageSize}
    </select>
    <select id="getStoreInfo" resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO">
        select
        s.org_id,
        cc.name as storeChannelName,
        cc.code storeChannelCode,
        s.store_sub_channel_code as storeSubChannelCode
        from orgcenter.t_store s
        LEFT JOIN orgcenter.channel_category cc ON s.store_type = cc.id
        where s.is_deleted = 0 and s.org_id in
        <foreach collection="storeOrgIdList" separator="," open="(" close=")" item="storeId" >
            #{storeId}
        </foreach>
        group by s.org_id
    </select>

    <select id="querySalesmanStation" resultType="com.fotile.exportcenter.common.KvLs">
        select s.id         as 'key',
        d.value_name as value
        from orgcenter.t_salesman s
        left join systemcenter.dic d on d.type_code = 'gw' and d.id = s.station
        and s.id in
        <foreach collection="salesmanIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="querySalesmanLabel" resultType="com.fotile.exportcenter.client.pojo.org.SalesmanLabel">
        select tsl.id,
        tsl.salesman_id,
        tsl.type,
        tsl.label_value,
        dic.value_name as labelName
        from orgcenter.t_salesman_label tsl
        left join systemcenter.dic dic on dic.is_deleted = 0 and dic.value_code = tsl.label_value and dic.type_code = 'capability_label'
        where tsl.is_deleted = 0
        and tsl.type = 1
        and tsl.salesman_id in
        <foreach collection="salesmanIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
        order by tsl.id asc
    </select>

    <select id="querySalesmanIdByLabelValue" resultType="java.lang.Long">
        select distinct salesman_id
        from orgcenter.t_salesman_label where is_deleted = 0
        and type = 1
        <if test="labelValueList != null and labelValueList.size() != 0">
            and label_value in
            <foreach item="labelValue" collection="labelValueList" separator="," open="(" close=")">
                #{labelValue}
            </foreach>
        </if>
    </select>
</mapper>