<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cluesExaminationDetail.dao.TCluesExaminationDetailDao">

    <select id="findttributeByCompanyIds"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.FindAttributeValueOutDto">
        SELECT *
        FROM marketingcenter.attribute
        WHERE  is_deleted=0 and crux_flag = 0
        <if test="companyIds != null and companyIds.size >0">

            and org_id in (
            <foreach collection="companyIds" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ORDER BY created_date desc
    </select>

    <!--查询服务报告列表-->
    <select id="queryExaminationExportListUseSalesman" resultType="com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto">
        SELECT
            d.id,
            d.type typeId,
            CASE d.type when 1 then '厨房健康报告' when 2 then '清洗服务报告' when 3 then '厨房健康报告'   else '厨电选购指南' end as typeName,
            d.created_by createdBy,
            c.charge_code chargeCode,
            c.charge_user_name chargeUserName,
            IF(cs.id is null, '(空)', CONCAT(cs.`code`, '/', cast(AES_DECRYPT_MY(UNHEX(cs.`name`), #{inDto.secretKey}) as char))) as createdPerson,
            d.salesman_id belongSalesmanId,
            IF(bs.id is null, '(空)', CONCAT(bs.`code`, '/', cast(AES_DECRYPT_MY(UNHEX(bs.`name`), #{inDto.secretKey}) as char))) as belongPerson,
            d.clues_id cluesId,
            c.customer_phone as cluesCustomerPhone,
            dic.value_code as districtCode,
            dic.value_name as districtValue,
            gw.value_name as createdPersonStationName,
            oc.id companyId,
            oc.name companyName,
            os.id stroeId,
            os.`code` stroeCode,
            os.name stroeName,
            ts.abbreviation as storeAbbreName,
            IFNULL(ts.abbreviation,os.name) as storeCombName,
            CONCAT(os.`code`,'/',IFNULL(ts.abbreviation,os.name)) stroeFullName,
            IF(YEAR(ts.terminal_check_date) = YEAR(CURDATE()),'是','否') as isNewStoreName,
<!--            c.customer_phone customerPhone,-->
<!--            c.customer_name customerName,-->
            CASE d.type when 1 then r.phone when 2 then c.customer_phone when 3 then r.phone  else c.customer_phone end as customerPhone,
            CASE d.type when 1 then r.nick_name when 2 then c.customer_name when 3 then r.nick_name  else c.customer_name end as customerName,
            c.gender gender,
            CASE c.gender WHEN 1 THEN '男' WHEN 2 THEN '女' ELSE '未知' END AS genderName,
            c.house_type houseType,
            c.address_id addressId,
            c.clues_source cluesSource,
            c.clues_source_value cluesSourceValue,
            c.audit_status cluesAuditStatus,
            CASE c.audit_status when '40' then '未提交' when '1' then '待审核' when '2' then '审核通过' when '3' then '审核不通过' else '空' end as cluesAuditStatusName,
            CASE d.check_count WHEN 0 THEN '/' ELSE d.check_count END AS checkCount,
            d.recheck_flag recheckFlag,
            CASE d.recheck_flag WHEN 1 THEN '是' ELSE '否' END AS recheckFlagName,
            d.created_date createdDate,
            d.view_count viewCount,
            d.share_count shareCount,
            CASE d.type when 1 then r.province_name when 3 then r.province_name  else c.province end as province,
            CASE d.type when 1 then r.city_name when 3 then r.city_name  else c.city end as city,
            CASE d.type when 1 then r.area_name when 3 then r.area_name  else c.area end as area,
            CASE d.type when 1 then IF(r.community_id &lt; 1, null, r.community_id) when 3 then IF(r.community_id &lt; 1, null, r.community_id)  else IF(c.village_id &lt; 1, null, c.village_id) end as villageId,
            CASE d.type when 1 then r.community_name when 3 then r.community_name  else c.village_name end as villageName,
            tsoi.id as serviceOrderId,
            CAST(AES_DECRYPT_MY(UNHEX(r.qywx_external_username), #{inDto.secretKey}) AS CHAR ) as customerNickname,
            khr.id  AS kitchenHealthReportId,
            case when d.type = 1 then d.range_hood_wind_speed when d.type = 3 then khr.yyj_fs else null end as hoodBlowingRate,
            case when d.type = 1 then d.tds_value when d.type = 3 then khr.tds else null end as tds,

            case when d.type = 1 and d.cookers_blow_flag = 1 then '是'
            when d.type = 1 and d.cookers_blow_flag = 0 then '否'
            when d.type = 3 then khr.zj_lq else null end as gasBlow,

            case when d.type = 1 and d.hood_out_period_flag = 1 then '是'
            when d.type = 1 and d.hood_out_period_flag = 0 then '否'
            when d.type = 3 then khr.zj_cl else null end as gasOutPeriod,

            case when d.type = 1 and d.heater_blow_flag = 1 then '是'
            when d.type = 1 and d.heater_blow_flag = 0 then '否'
            when d.type = 3 then khr.rsq_lq else null end as heaterBlow,

            case when d.type = 3 then khr.rsq_cl else null end as heaterOutPeriod,

            case when cmi.customer_id is null then '否' else '是' end as isVip,
            case when dii.clues_id is null then '否' else '是' end as isDeal,
            dii.audit_date as auditDate,
            case when cdbl.id is null then '否' else '是' end as isEquity,
            cc3.name as storeChannelName,
            IF(cc2.id is null,'', concat(cc1.name,'/',cc2.name)) as distributorChannelCombName,
            td.name as distributorName,
            cc1.name as distributorChannelCategoryName
        FROM marketingcenter.t_clues_examination_detail d

                <!--inner join (
                select d1.id,
                       s.id                                                                                               as createdSalesmanId,
                       IF(s.id is null, '(空)', CONCAT(s.`code`, '/',
                                                      cast(AES_DECRYPT_MY(UNHEX(s.`name`), #{inDto.secretKey}) as char))) as createdPerson
                FROM marketingcenter.t_clues_examination_detail d1
                         left join orgcenter.t_salesman s
                                   on s.is_deleted = 0 and s.id = d1.created_by
                WHERE d1.is_deleted = 0
                  AND d1.type = 4

                UNION ALL

                select d2.id,
                       s.id               as createdSalesmanId,
                       CASE
                           WHEN s.id is not null THEN CONCAT(s.`code`, '/',
                                                             cast(AES_DECRYPT_MY(UNHEX(s.`name`), #{inDto.secretKey}) as char))
                           WHEN uee.id is not null THEN cast(AES_DECRYPT_MY(UNHEX(uee.first_name),
                                                                            #{inDto.secretKey}) as char)
                           else '(空)' end as createdPerson
                FROM marketingcenter.t_clues_examination_detail d2
                         left join usercenter.user_entity_extend uee
                                   on uee.is_deleted = 0 and uee.user_entity_id = d2.created_by
                         left join orgcenter.t_salesman s
                                   on s.is_deleted = 0 and s.id = uee.salesman_id
                WHERE d2.is_deleted = 0
                  AND d2.type in (1, 2, 3)
                <if test="inDto.createdByAccountIds != null and inDto.createdByAccountIds.size() > 0">
                    and uee.user_entity_id in
                    <foreach collection="inDto.createdByAccountIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) dd on dd.id = d.id-->

        <if test=" inDto.calcStartDate != null or inDto.calcEndDate != null">
            inner join (
                select distinct
                    bc.examination_detail_id
                from marketingcenter.t_clues_examination_detail_bjt_count bc
                where
                    bc.is_deleted = 0
                    <if test=" inDto.calcStartDate != null">
                        and bc.calc_date &gt;= #{inDto.calcStartDate}
                    </if>
                    <if test=" inDto.calcEndDate != null">
                        and bc.calc_date &lt;= #{inDto.calcEndDate}
                    </if>
            ) tt on tt.examination_detail_id = d.id
        </if>
        left JOIN marketingcenter.user_clues c on c.id = d.clues_id <!--and c.is_deleted = 0-->
        <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
            and c.id in
            <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join orgcenter.t_salesman bs on bs.is_deleted = 0 and bs.id = d.salesman_id
        left join orgcenter.t_salesman cs on cs.is_deleted = 0 and cs.id = d.created_salesman_id
        left join orgcenter.t_org os on os.is_deleted = 0 and os.type = 3 and os.id = cs.store_id
        left join orgcenter.t_org oc on oc.is_deleted = 0 and oc.type = 1 and oc.id = cs.company_id
        left join orgcenter.t_store ts on ts.is_deleted = 0 and ts.org_id = os.id
        left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = oc.id
        left join systemcenter.dic dic on dic.is_deleted = 0 and dic.type_code = '所属大区' and dic.id = tc.area
        left join systemcenter.dic gw on gw.is_deleted = 0 and gw.type_code = 'gw' and gw.id = cs.station
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id = ts.store_type
        LEFT JOIN orgcenter.t_distributor_mapping tdm
                  ON tdm.is_deleted = 0 AND ts.org_id = tdm.org_id AND tdm.type = 3
        LEFT JOIN orgcenter.t_distributor td ON td.is_deleted = 0 AND tdm.distributor_id = td.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id = td.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id = td.channel_subdivide
        LEFT JOIN marketingcenter.kitchen_health_report r on r.is_deleted = 0 and r.medical_report_id = d.id
        LEFT JOIN omscenter.t_service_order_info tsoi on tsoi.is_deleted = 0 and tsoi.examination_id = d.id

        left join	(
						SELECT
							r.id,
							r.medical_report_id,
							GROUP_CONCAT( IF ( qm.question_id = 13, qm.user_input, NULL ) SEPARATOR '' ) as yyj_fs,
							GROUP_CONCAT( IF ( qm.question_id = 18, qm.user_input, NULL ) SEPARATOR '' ) as tds,
                            if(GROUP_CONCAT( IF ( qm.question_id = 2, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as zj_lq,-- 灶具是否存在燃气泄漏隐患
                            if(GROUP_CONCAT( IF ( qm.question_id = 3, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as zj_cl,-- 灶具是否超龄使用
                            if(GROUP_CONCAT( IF ( qm.question_id = 5, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as rsq_lq,-- 热水器是否存在燃气泄漏隐患
                            if(GROUP_CONCAT( IF ( qm.question_id = 6, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as rsq_cl -- 热水器是否超龄使用
						FROM
							marketingcenter.kitchen_health_report r
							INNER JOIN marketingcenter.kitchen_health_report_question_mapping qm ON qm.is_deleted = 0
							AND qm.report_id = r.id
						WHERE
							r.is_deleted = 0
							AND qm.question_id IN ( 13, 18, 2, 3, 5, 6 )
						GROUP BY
							r.id,
						r.medical_report_id
			) khr ON khr.medical_report_id = d.id
        left join (
			SELECT
				di.clues_id,
				max(di.audit_date ) as audit_date
			FROM
				marketingcenter.t_clues_examination_detail dd
				INNER JOIN omscenter.declaration_info di ON  di.clues_id = dd.clues_id and di.is_deleted = 0
				AND di.stage = 2
				AND di.order_stage !=4
                <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
                    and di.clues_id in
                    <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

			WHERE
				dd.is_deleted = 0 and
                TIMESTAMPDIFF(SECOND,dd.created_date,di.order_create_time)>=-48*3600
				GROUP BY
				di.clues_id
			) dii on dii.clues_id = d.clues_id
            left join customercenter.customer_info ci on ci.is_deleted = 0 and ci.type =1 and ci.phone = r.phone
            left join (select DISTINCT cmiTemp.customer_id from customercenter.t_company_member_info cmiTemp where cmiTemp.is_deleted = 0) cmi on  cmi.customer_id = ci.id
            LEFT JOIN (select distinct cdbl_temp.source_id as  id from promotioncenter.exchange_card_detail_bjt_log cdbl_temp where cdbl_temp.is_deleted = 0
            AND cdbl_temp.biz_type IN ( 3, 4 ) ) cdbl on cdbl.id = d.id
        <where>
            d.is_deleted = 0
            <if test="inDto.ids != null and inDto.ids.size > 0">
                and d.id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.districtCode != null and inDto.districtCode != ''">
                and dic.value_code = #{inDto.districtCode}
            </if>
            <if test="inDto.companyId != null">
                and cs.company_id = #{inDto.companyId}
            </if>
            <if test="inDto.storeOrgId != null">
                and cs.store_id = #{inDto.storeOrgId}
            </if>
            <if test="inDto.chargeId != null and inDto.chargeId != ''">
                and cs.id = #{inDto.chargeId}
            </if>
            <if test="inDto.belongSalesmanId != null">
                and d.salesman_id = #{inDto.belongSalesmanId}
            </if>
            <if test="inDto.type != null">
                <choose>
                    <when test="inDto.type == 1">
                        and d.type in (1,3)
                    </when>
                    <otherwise>
                        and d.type = #{inDto.type}
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.phone != null and inDto.phone != ''">
                and
                    (
                        (d.type in (1, 3)
                            and r.phone =
                                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                        (d.type in (2, 4)
                            and c.customer_phone =
                                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                        )
            </if>
            <if test="inDto.name != null and inDto.name != ''">
                and
                    (
                        (d.type in (1, 3)
                            and r.nick_name =
                                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                        (d.type in (2, 4)
                            and c.customer_name =
                                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                        )
            </if>
            <if test="inDto.startTime != null and inDto.endTime != null">
                and d.created_date between #{inDto.startTime} and #{inDto.endTime}
            </if>
            <if test="inDto.authorCompanyIds != null and inDto.authorCompanyIds.size > 0">
                and cs.company_id in
                <foreach collection="inDto.authorCompanyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="inDto.cluesId != null">
                <if test="inDto.cluesId == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesId != '-1'">
                    and d.clues_id = #{inDto.cluesId}
                </if>
            </if>
            <if test="inDto.cluesAuditStatus != null and inDto.cluesAuditStatus != ''">
                <if test="inDto.cluesAuditStatus == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesAuditStatus != '-1'">
                    and c.audit_status = #{inDto.cluesAuditStatus}
                </if>
            </if>
            <if test="inDto.isNewStore != null">
                <if test="inDto.isNewStore == 1">
                    and YEAR(ts.terminal_check_date) = YEAR(CURDATE())
                </if>
                <if test="inDto.isNewStore == 2">
                    and (ts.terminal_check_date is null or YEAR(ts.terminal_check_date) != YEAR(CURDATE()))
                </if>
            </if>
            <!--门店渠道支持多选-->
            <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="inDto.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != '' and inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <if test="inDto.isAssociateServiceOrder != null">
                <if test="inDto.isAssociateServiceOrder == 0">
                    and tsoi.id is null
                </if>
                <if test="inDto.isAssociateServiceOrder == 1">
                    and tsoi.id is not null
                </if>
            </if>


            <if test="inDto.isAssociateCustomer != null">
                <if test="inDto.isAssociateCustomer == 0">
                    <!--   and tsoi.qywx_external_username is null-->
                    and( r.external_userid IS NULL
                    OR r.external_userid ='' )
                </if>
                <if test="inDto.isAssociateCustomer == 1">
                    <!-- and tsoi.qywx_external_username is not null-->
                    and r.external_userid IS NOT NULL
                    AND r.external_userid!=''
                </if>
            </if>

        </where>
        ORDER BY d.created_date desc
        limit #{pageInfo.offset}, #{pageInfo.size}
    </select>

    <select id="queryExaminationListCountUseSalesman" resultType="java.lang.Long">
        SELECT
        count(d.id)
        FROM marketingcenter.t_clues_examination_detail d
        <if test=" inDto.calcStartDate != null or inDto.calcEndDate != null">
            inner join (
            select distinct
            bc.examination_detail_id
            from marketingcenter.t_clues_examination_detail_bjt_count bc
            where
            bc.is_deleted = 0
            <if test=" inDto.calcStartDate != null">
                and bc.calc_date &gt;= #{inDto.calcStartDate}
            </if>
            <if test=" inDto.calcEndDate != null">
                and bc.calc_date &lt;= #{inDto.calcEndDate}
            </if>
            ) tt on tt.examination_detail_id = d.id
        </if>
        left JOIN marketingcenter.user_clues c on c.id = d.clues_id <!--and c.is_deleted = 0-->
        <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
            and c.id in
            <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join orgcenter.t_salesman cs on cs.is_deleted = 0 and cs.id = d.created_salesman_id
        left join orgcenter.t_salesman bs on bs.is_deleted = 0 and bs.id = d.salesman_id
        left join orgcenter.t_store ts on ts.is_deleted = 0 and ts.org_id = cs.store_id
        <if test="inDto.districtCode != null and inDto.districtCode != ''">
            left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = cs.company_id
            left join systemcenter.dic dic on dic.is_deleted = 0 and dic.type_code = '所属大区' and dic.id = tc.area
        </if>

        <if test="(inDto.phone != null and inDto.phone != '') or (inDto.name != null and inDto.name != '')  or (inDto.isAssociateCustomer !=null)">
            LEFT JOIN marketingcenter.kitchen_health_report r on r.is_deleted = 0 and r.medical_report_id = d.id
        </if>

        <if test="inDto.isAssociateServiceOrder != null or inDto.isAssociateCustomer != null">
            LEFT JOIN omscenter.t_service_order_info tsoi on tsoi.is_deleted = 0 and tsoi.examination_id = d.id
        </if>

        <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
            LEFT JOIN orgcenter.channel_category cc3 on cc3.id = ts.store_type
        </if>

        <if test="(inDto.channelCategoryIds != null and inDto.channelCategoryIds != '')
        or (inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != '')">
            LEFT JOIN orgcenter.t_distributor_mapping tdm
                      ON tdm.is_deleted = 0 AND ts.org_id = tdm.org_id AND tdm.type = 3
            LEFT JOIN orgcenter.t_distributor td ON td.is_deleted = 0 AND tdm.distributor_id = td.id
            LEFT JOIN orgcenter.channel_category cc1 on cc1.id = td.channel_category
            LEFT JOIN orgcenter.channel_category cc2 on cc2.id = td.channel_subdivide
        </if>
        <where>
            d.is_deleted = 0
            <if test="inDto.ids != null and inDto.ids.size > 0">
                and d.id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.districtCode != null and inDto.districtCode != ''">
                and dic.value_code = #{inDto.districtCode}
            </if>
            <if test="inDto.companyId != null">
                and cs.company_id = #{inDto.companyId}
            </if>
            <if test="inDto.storeOrgId != null">
                and cs.store_id = #{inDto.storeOrgId}
            </if>
            <if test="inDto.chargeId != null and inDto.chargeId != ''">
                and cs.id = #{inDto.chargeId}
            </if>
            <if test="inDto.belongSalesmanId != null">
                and d.salesman_id = #{inDto.belongSalesmanId}
            </if>
            <if test="inDto.type != null">
                <choose>
                    <when test="inDto.type == 1">
                        and d.type in (1,3)
                    </when>
                    <otherwise>
                        and d.type = #{inDto.type}
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.izKitchenDetailExport != null and inDto.izKitchenDetailExport == 1">
                and d.type = 3
            </if>
            <if test="inDto.phone != null and inDto.phone != ''">
                and
                (
                (d.type in (1, 3)
                and r.phone =
                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                or
                (d.type in (2, 4)
                and c.customer_phone =
                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                )
            </if>
            <if test="inDto.name != null and inDto.name != ''">
                and
                (
                (d.type in (1, 3)
                and r.nick_name =
                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                or
                (d.type in (2, 4)
                and c.customer_name =
                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                )
            </if>
            <if test="inDto.startTime != null and inDto.endTime != null">
                and d.created_date between #{inDto.startTime} and #{inDto.endTime}
            </if>
            <if test="inDto.authorCompanyIds != null and inDto.authorCompanyIds.size > 0">
                and cs.company_id in
                <foreach collection="inDto.authorCompanyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="inDto.cluesId != null">
                <if test="inDto.cluesId == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesId != '-1'">
                    and d.clues_id = #{inDto.cluesId}
                </if>
            </if>
            <if test="inDto.cluesAuditStatus != null and inDto.cluesAuditStatus != ''">
                <if test="inDto.cluesAuditStatus == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesAuditStatus != '-1'">
                    and c.audit_status = #{inDto.cluesAuditStatus}
                </if>
            </if>
            <if test="inDto.isNewStore != null">
                <if test="inDto.isNewStore == 1">
                    and YEAR(ts.terminal_check_date) = YEAR(CURDATE())
                </if>
                <if test="inDto.isNewStore == 2">
                    and (ts.terminal_check_date is null or YEAR(ts.terminal_check_date) != YEAR(CURDATE()))
                </if>
            </if>
            <!--门店渠道支持多选-->
            <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="inDto.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != '' and inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <if test="inDto.isAssociateServiceOrder != null">
                <if test="inDto.isAssociateServiceOrder == 0">
                    and tsoi.id is null
                </if>
                <if test="inDto.isAssociateServiceOrder == 1">
                    and tsoi.id is not null
                </if>
            </if>


            <if test="inDto.isAssociateCustomer != null">
                <if test="inDto.isAssociateCustomer == 0">
                    <!--   and tsoi.qywx_external_username is null-->
                    and( r.external_userid IS NULL
                    OR r.external_userid ='' )
                </if>
                <if test="inDto.isAssociateCustomer == 1">
                    <!-- and tsoi.qywx_external_username is not null-->
                    and r.external_userid IS NOT NULL
                    AND r.external_userid!=''
                </if>
            </if>

        </where>

    </select>

    <select id="querySimpleExaminationListUseSalesman"
            resultType="com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QuerySimpleExaminationListOutDto">
        SELECT  d.id,
                d.created_by AS createdByAccountId,
                d.salesman_id AS createdBySalesmanId,
                d.clues_id as cluesId
        FROM marketingcenter.t_clues_examination_detail d
        where d.is_deleted = 0
        <if test="inDto.ids != null and inDto.ids.size() > 0">
            and d.id in
            <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inDto.type != null">
            <choose>
                <when test="inDto.type == 1">
                    and d.type in (1, 3)
                </when>
                <otherwise>
                    and d.type = #{inDto.type}
                </otherwise>
            </choose>
        </if>
        <if test="inDto.izKitchenDetailExport != null and inDto.izKitchenDetailExport == 1">
            and d.type = 3
        </if>
        <if test="inDto.startTime != null and inDto.endTime != null">
            and d.created_date between #{inDto.startTime} and #{inDto.endTime}
        </if>
        <if test="inDto.cluesId != null">
            <if test="inDto.cluesId == '-1'">
                and d.clues_id is null
            </if>
            <if test="inDto.cluesId != '-1'">
                and d.clues_id = #{inDto.cluesId}
            </if>
        </if>
    </select>

    <select id="querySimpleExaminationCountUseSalesman" resultType="java.lang.Integer">
        SELECT count(1)
        FROM marketingcenter.t_clues_examination_detail d
        where d.is_deleted = 0
        <if test="inDto.ids != null and inDto.ids.size() > 0">
            and d.id in
            <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inDto.type != null">
            <choose>
                <when test="inDto.type == 1">
                    and d.type in (1, 3)
                </when>
                <otherwise>
                    and d.type = #{inDto.type}
                </otherwise>
            </choose>
        </if>
        <if test="inDto.izKitchenDetailExport != null and inDto.izKitchenDetailExport == 1">
            and d.type = 3
        </if>
        <if test="inDto.startTime != null and inDto.endTime != null">
            and d.created_date between #{inDto.startTime} and #{inDto.endTime}
        </if>
        <if test="inDto.cluesId != null">
            <if test="inDto.cluesId == '-1'">
                and d.clues_id is null
            </if>
            <if test="inDto.cluesId != '-1'">
                and d.clues_id = #{inDto.cluesId}
            </if>
        </if>
    </select>

    <select id="queryExaminationKitchenDetailExportUseSalesman" resultType="com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationKitchenDetailOutDTO">
        select
            t.reportId,
            t.problemNum,
            t.problemList,
            t.nickName,
            t.phone,
            t.areaComb,
            t.communityComb,
            t.createdDate,
            -- 1、火盖状态是否腐蚀、变形、火孔有堵塞？
            if(t.q1 = 1,'是', '否') as fq1,
            -- 2、火焰状态是否有火孔未出火、是否红黄火？
            if(t.q2 = 1,'是', '否') as fq2,
            -- 3、燃气检测仪器是否报警？
            if(t.q3 = 1,'是', '否') as fq3,
            -- 4、灶具使用年限
            t.q4 as fq4,
            -- 5、灶具与燃气管接口处是否泄漏？
            if(t.q5 = 1,'是', '否') as fq5,
            -- 6、灶具炉头处是否泄露？
            if(t.q6 = 1,'是', '否') as fq6,
            -- 7、热水器使用年限
            t.q7 as fq7,
            -- 8、热水器与燃气管接口处是否泄漏？
            if(t.q8 = 1,'是', '否') as fq8,
            -- 9、热水器是否点火不顺畅？
            if(t.q9 = 1,'是', '否') as fq9,
            -- 10、厨房插座是否存在电路问题？
            if(t.q10 = 1,'是', '否') as fq10,
            -- 11、烟管排烟是否顺畅？
            if(t.q11 = 1,'是', '否') as fq11,
            -- 12、油烟机使用年限
            t.q12 as fq12,
            -- 13、吸烟效果检测
            t.q13 as fq13,

            -- 14、油烟机机型
            CASE t.q14
                WHEN '1' THEN "欧式&新欧式"
                WHEN '2' THEN '侧吸式'
                WHEN '3' THEN '中式'
                WHEN '4' THEN '低吸式（含部分升降）'
                WHEN '5' THEN '卜字型'
                WHEN '6' THEN '其他'
                ELSE t.q14
                END AS fq14,

            -- 油烟机形状
            CASE
                WHEN t.q14 = '1' THEN
                    CASE t.q101
                        WHEN '1' THEN '无挡烟板-网状&栅格状'
                        WHEN '2' THEN '无挡烟板-环吸板'
                        WHEN '3' THEN '有挡烟板-栅格状'
                        ELSE t.q101
                        END
                WHEN t.q14 = '2' THEN
                    CASE t.q101
                        WHEN '1' THEN '网状'
                        WHEN '2' THEN '栅格状'
                        WHEN '3' THEN '环吸板'
                        ELSE t.q101
                        END
                WHEN t.q14 = '3' THEN
                    CASE t.q101
                        WHEN '1' THEN '网状'
                        ELSE t.q101
                        END
                WHEN t.q14 = '4' THEN
                    CASE t.q101
                        WHEN '1' THEN '无油网'
                        ELSE t.q101
                        END
                WHEN t.q14 = '5' THEN
                    CASE t.q101
                        WHEN '1' THEN '网状'
                        ELSE t.q101
                        END
                ELSE t.q101
                END AS fq101,

            -- 挂机高度
            CASE
                WHEN t.q14 = '1' AND t.q101 = '1' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 700mm'
                        WHEN t.q102 = '2' THEN '700mm ≤ H ≤ 800mm'
                        WHEN t.q102 = '3' THEN 'H > 800mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '1' AND t.q101 = '2' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 675mm'
                        WHEN t.q102 = '2' THEN '675mm ≤ H ≤ 775mm'
                        WHEN t.q102 = '3' THEN 'H > 775mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '1' AND t.q101 = '3' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 700mm'
                        WHEN t.q102 = '2' THEN '700mm ≤ H ≤ 800mm'
                        WHEN t.q102 = '3' THEN 'H > 800mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '2' AND t.q101 = '1' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 750mm'
                        WHEN t.q102 = '2' THEN '750mm ≤ H ≤ 850mm'
                        WHEN t.q102 = '3' THEN 'H > 850mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '2' AND t.q101 = '2' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 700mm'
                        WHEN t.q102 = '2' THEN '700mm ≤ H ≤ 800mm'
                        WHEN t.q102 = '3' THEN 'H > 800mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '2' AND t.q101 = '3' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 725mm'
                        WHEN t.q102 = '2' THEN '725mm ≤ H ≤ 825mm'
                        WHEN t.q102 = '3' THEN 'H > 825mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '3' AND t.q101 = '1' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 975mm'
                        WHEN t.q102 = '2' THEN '975mm ≤ H ≤ 1075mm'
                        WHEN t.q102 = '3' THEN 'H > 1075mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '4' AND t.q101 = '1' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 725mm'
                        WHEN t.q102 = '2' THEN '725mm ≤ H ≤ 825mm'
                        WHEN t.q102 = '3' THEN 'H > 825mm'
                        ELSE t.q102
                        END
                WHEN t.q14 = '5' AND t.q101 = '1' THEN
                    CASE
                        WHEN t.q102 = '1' THEN 'H < 775mm'
                        WHEN t.q102 = '2' THEN '775mm ≤ H ≤ 875mm'
                        WHEN t.q102 = '3' THEN 'H > 875mm'
                        ELSE t.q102
                        END
                ELSE t.q102
                END AS fq102,

            -- 油烟机风速检测数值（m/s）
            t.q103 as fq103,
            -- 15、内部脏污程度检测
            t.q15 as fq15,
            -- 16、异音检测
            t.q16 as fq16,
            -- 17、是否安装净水器？
            if(t.q17 = 1,'是', '否') as fq17,
            -- 18、净水TDS检测数值（mg/l)
            t.q18 as fq18,
            -- 自来水TDS检测数值（mg/l)
            t.q201 as fq201,
            -- 19、净水铜离子检测结果？
            t.q19 as fq19,
            -- 自来水铜离子检测结果？
            t.q301 as fq301,
            -- 20、净水氯离子检测结果？
            t.q20 as fq20,
            -- 自来水氯离子检测结果？
            t.q401 as fq401,
            -- 21、冰箱是否存在明显异味？
            if(t.q21 = 1,'是', '否') as fq21,
            -- 22、冰箱是否存在积水、结冰等健康隐患？
            if(t.q22 = 1,'是', '否') as fq22,
            -- 其他安全隐患
            t.other_security_risks as otherSecurityRisks,
            -- 综合诊断建议
            t.comprehensive_diagnostic_advice as comprehensiveDiagnosticAdvice,

            -- 创建业务员大区
            t.createdAreaName,
            -- 创建业务员分公司
            t.createdCompanyName,
            -- 创建业务员门店渠道
            t.createdStoreChannelName,
            -- 创建业务员门店渠道细分
            t.createdStoreSubChannelCode,
            -- 创建业务员客户渠道
            t.createdChannelSubdivideName,
            -- 创建业务员客户
            t.createdDistributorName,
            -- 创建业务员门店编码
            t.createdStoreCode,
            -- 创建业务员门店
            t.createdStoreName,
            -- 创建业务员编码
            t.createdCode,
            -- 创建业务员姓名
            t.createdName,
            -- 创建人手机号（脱敏）
            t.createdPhone,
            -- 创建人岗位
            t.createdPosition,
            -- 所属业务员编码
            t.belongCode,
            -- 所属业务员姓名
            t.belongName,
            -- 所属业务员手机号（脱敏）
            t.belongPhone
        FROM (
                 SELECT
                     khr.id,
                     khr.medical_report_id as reportId,
                     ifnull(problemTable.problemNum,0) as problemNum,
                     problemListTable.problemList,
                     cast(AES_DECRYPT_MY(UNHEX(khr.nick_name),#{inDto.secretKey}) as char) AS nickName,
                     cast(AES_DECRYPT_MY(UNHEX(khr.phone),#{inDto.secretKey}) as char) AS phone,
                     CONCAT(khr.province_name,'-',khr.city_name,'-',khr.area_name) as areaComb,
                     CONCAT(khr.community_id,'/',khr.community_name) as communityComb,
                     khr.created_date as createdDate,
                     -- 1、火盖状态是否腐蚀、变形、火孔有堵塞？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 30, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q1,
                     -- 2、火焰状态是否有火孔未出火、是否红黄火？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 4, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q2,
                     -- 3、燃气检测仪器是否报警？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 57, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q3,
                     -- 4、灶具使用年限
                     IFNULL( GROUP_CONCAT( IF ( q.id = 3, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q4,
                     -- 5、灶具与燃气管接口处是否泄漏？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 2, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q5,
                     -- 6、灶具炉头处是否泄露？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 25, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q6,
                     -- 7、热水器使用年限
                     IFNULL( GROUP_CONCAT( IF ( q.id = 6, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q7,
                     -- 8、热水器与燃气管接口处是否泄漏？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 5, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q8,
                     -- 9、热水器是否点火不顺畅？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 7, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q9,
                     -- 10、厨房插座是否存在电路问题？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 9, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q10,
                     -- 11、烟管排烟是否顺畅？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 16, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q11,
                     -- 12、油烟机使用年限
                     IFNULL( GROUP_CONCAT( IF ( q.id = 14, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q12,
                     -- 13、吸烟效果检测
                     IFNULL( GROUP_CONCAT( IF ( q.id = 51, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q13,
                     -- 14、油烟机机型
                     IFNULL( GROUP_CONCAT( IF ( q.id = 52, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q14,
                     -- 油烟机形状
                     IFNULL( GROUP_CONCAT( IF ( q.id = 53, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q101,
                     -- 挂机高度
                     IFNULL( GROUP_CONCAT( IF ( q.id = 54, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q102,
                     -- 油烟机风速检测数值（m/s）
                     IFNULL( GROUP_CONCAT( IF ( q.id = 13, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q103,
                     -- 15、内部脏污程度检测
                     IFNULL( GROUP_CONCAT( IF ( q.id = 55, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q15,
                     -- 16、异音检测
                     IFNULL( GROUP_CONCAT( IF ( q.id = 56, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q16,
                     -- 17、是否安装净水器？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 27, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q17,
                     -- 18、净水TDS检测数值（mg/l)
                     IFNULL( GROUP_CONCAT( IF ( q.id = 28, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q18,
                     -- 自来水TDS检测数值（mg/l)
                     IFNULL( GROUP_CONCAT( IF ( q.id = 18, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q201,
                     -- 19、净水铜离子检测结果？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 59, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q19,
                     -- 自来水铜离子检测结果？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 58, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q301,
                     -- 20、净水氯离子检测结果？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 63, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q20,
                     -- 自来水氯离子检测结果？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 62, qm.user_input, NULL ) SEPARATOR '' ), '' )  as q401,
                     -- 21、冰箱是否存在明显异味？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 22, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q21,
                     -- 22、冰箱是否存在积水、结冰等健康隐患？
                     IFNULL( GROUP_CONCAT( IF ( q.id = 23, qm.user_choose, NULL ) SEPARATOR '' ), '' )  as q22,
                     -- 其他安全隐患
                     khr.other_security_risks,
                     -- 综合诊断建议
                    khr.comprehensive_diagnostic_advice,
                    khr_dq.value_name as createdAreaName,
                    khr_company.`name` as createdCompanyName,
                    khr_st.`name` as createdStoreChannelName,
                    khr_ts.store_sub_channel_code as createdStoreSubChannelCode,
                    khr_cs.`name` as createdChannelSubdivideName,
                    khr_td.`name` as createdDistributorName,
                    khr_store.`code` as createdStoreCode,
                    khr_store.`name` as createdStoreName,
                    khr_s.`code` as createdCode,
                    cast(AES_DECRYPT_MY(UNHEX(khr_s.`name`),#{inDto.secretKey}) as char) as createdName,
                    cast(AES_DECRYPT_MY(UNHEX(khr_s.phone),#{inDto.secretKey}) as char) AS createdPhone,
                    khr_gw.value_name as createdPosition,
                    khr_bs.`code` as belongCode,
                    cast(AES_DECRYPT_MY(UNHEX(khr_bs.`name`),#{inDto.secretKey}) as char) as belongName,
                    cast(AES_DECRYPT_MY(UNHEX(khr_bs.phone),#{inDto.secretKey}) as char) AS belongPhone
                 FROM
                    marketingcenter.kitchen_health_report khr
                    INNER JOIN marketingcenter.t_clues_examination_detail d ON d.is_deleted = 0 and d.id = khr.medical_report_id
                    left JOIN marketingcenter.user_clues c on c.id = d.clues_id <!--and c.is_deleted = 0-->
                    <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
                        and c.id in
                        <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    left join orgcenter.t_salesman cs on cs.is_deleted = 0 and cs.id = d.created_salesman_id
                    left join orgcenter.t_salesman bs on bs.is_deleted = 0 and bs.id = d.salesman_id
                    left join orgcenter.t_store ts on ts.is_deleted = 0 and ts.org_id = cs.store_id
                    <if test="inDto.districtCode != null and inDto.districtCode != ''">
                        left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = cs.company_id
                        left join systemcenter.dic dic on dic.is_deleted = 0 and dic.type_code = '所属大区' and dic.id = tc.area
                    </if>

                    <if test="(inDto.phone != null and inDto.phone != '') or (inDto.name != null and inDto.name != '')  or (inDto.isAssociateCustomer !=null)">
                        LEFT JOIN marketingcenter.kitchen_health_report r on r.is_deleted = 0 and r.medical_report_id = d.id
                    </if>

                    <if test="inDto.isAssociateServiceOrder != null or inDto.isAssociateCustomer != null">
                        LEFT JOIN omscenter.t_service_order_info tsoi on tsoi.is_deleted = 0 and tsoi.examination_id = d.id
                    </if>

                    <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                        LEFT JOIN orgcenter.channel_category cc3 on cc3.id = ts.store_type
                    </if>

                    <if test="(inDto.channelCategoryIds != null and inDto.channelCategoryIds != '')
                    or (inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != '')">
                        LEFT JOIN orgcenter.t_distributor_mapping tdm
                                  ON tdm.is_deleted = 0 AND ts.org_id = tdm.org_id AND tdm.type = 3
                        LEFT JOIN orgcenter.t_distributor td ON td.is_deleted = 0 AND tdm.distributor_id = td.id
                        LEFT JOIN orgcenter.channel_category cc1 on cc1.id = td.channel_category
                        LEFT JOIN orgcenter.channel_category cc2 on cc2.id = td.channel_subdivide
                    </if>
                     left JOIN (
                         SELECT
                             khrqm.report_id,
                             count(*) as problemNum
                         FROM
                             marketingcenter.kitchen_health_report_question_mapping khrqm
                         WHERE
                             khrqm.is_deleted = 0
                             AND khrqm.is_problem = 1
                         GROUP BY
                             khrqm.report_id
                     ) problemTable ON problemTable.report_id = khr.id

                     left join (
                         SELECT
                             khrqm.report_id,
                             IFNULL( GROUP_CONCAT( khrq.question_title SEPARATOR ';' ), '' ) AS problemList
                         FROM
                             marketingcenter.kitchen_health_report_question_mapping khrqm
                             left join marketingcenter.kitchen_health_report_question khrq ON khrq.id = khrqm.question_id
                         WHERE
                             khrqm.is_deleted = 0
                             AND khrqm.is_problem = 1
                         GROUP BY
                             khrqm.report_id
                     ) problemListTable on problemListTable.report_id = khr.id
                    left join marketingcenter.kitchen_health_report_question_mapping qm ON qm.is_deleted = 0 and qm.report_id = khr.id
                    left join  marketingcenter.kitchen_health_report_question q ON q.id = qm.question_id
                    left join orgcenter.t_salesman khr_s on khr_s.id = d.created_salesman_id
                    left join systemcenter.dic khr_gw on khr_gw.type_code = 'gw' and khr_gw.id = khr_s.station
                    left join orgcenter.t_org khr_company on khr_company.is_deleted = 0 and khr_s.company_id = khr_company.id and khr_company.type = 1
                    left join orgcenter.t_company khr_tc on khr_tc.is_deleted = 0 and khr_s.company_id = khr_tc.org_id
                    left join systemcenter.dic khr_dq on khr_dq.type_code = '所属大区' and khr_dq.id = khr_tc.area
                    left join orgcenter.t_org khr_store on khr_store.is_deleted = 0 and khr_s.store_id = khr_store.id and khr_store.type = 3
                    left join orgcenter.t_store khr_ts on khr_ts.is_deleted = 0 and khr_s.store_id = khr_ts.org_id
                    left join orgcenter.channel_category khr_st on khr_st.is_deleted = 0 and khr_st.type_code = 'store_type' and khr_st.id = khr_ts.store_type
                    left join orgcenter.t_distributor_mapping khr_tdm on khr_tdm.is_deleted = 0 and khr_tdm.type = 3 and khr_tdm.org_id = khr_store.id
                    left join orgcenter.t_distributor khr_td on khr_td.is_deleted = 0 and khr_td.id = khr_tdm.distributor_id
                    left join orgcenter.channel_category khr_cs on khr_cs.is_deleted = 0 and khr_cs.type_code = 'channel_subdivide' and khr_cs.id = khr_td.channel_subdivide
                    left join orgcenter.channel_category khr_cc on khr_cc.is_deleted = 0 and khr_cc.type_code = 'channel_category' and khr_cc.id = khr_td.channel_category
                    left join orgcenter.t_salesman khr_bs on khr_bs.id = d.salesman_id
                 WHERE
                     khr.is_deleted = 0
                     and khr.is_draft = 0
                    <if test="inDto.ids != null and inDto.ids.size > 0">
                            and d.id in
                            <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                                #{id}
                            </foreach>
                        </if>
                        <if test="inDto.districtCode != null and inDto.districtCode != ''">
                            and dic.value_code = #{inDto.districtCode}
                        </if>
                        <if test="inDto.companyId != null">
                            and cs.company_id = #{inDto.companyId}
                        </if>
                        <if test="inDto.storeOrgId != null">
                            and cs.store_id = #{inDto.storeOrgId}
                        </if>
                        <if test="inDto.chargeId != null and inDto.chargeId != ''">
                            and cs.id = #{inDto.chargeId}
                        </if>
                        <if test="inDto.belongSalesmanId != null">
                            and d.salesman_id = #{inDto.belongSalesmanId}
                        </if>
                        <if test="inDto.type != null">
                            <choose>
                                <when test="inDto.type == 1">
                                    and d.type in (1,3)
                                </when>
                                <otherwise>
                                    and d.type = #{inDto.type}
                                </otherwise>
                            </choose>
                        </if>
                        <if test="inDto.izKitchenDetailExport != null and inDto.izKitchenDetailExport == 1">
                            and d.type = 3
                        </if>
                        <if test="inDto.phone != null and inDto.phone != ''">
                            and
                            (
                            (d.type in (1, 3)
                            and r.phone =
                            #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                            (d.type in (2, 4)
                            and c.customer_phone =
                            #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            )
                        </if>
                        <if test="inDto.name != null and inDto.name != ''">
                            and
                            (
                            (d.type in (1, 3)
                            and r.nick_name =
                            #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                            (d.type in (2, 4)
                            and c.customer_name =
                            #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            )
                        </if>
                        <if test="inDto.startTime != null and inDto.endTime != null">
                            and d.created_date between #{inDto.startTime} and #{inDto.endTime}
                        </if>
                        <if test="inDto.authorCompanyIds != null and inDto.authorCompanyIds.size > 0">
                            and cs.company_id in
                            <foreach collection="inDto.authorCompanyIds" item="companyId" open="(" separator="," close=")">
                                #{companyId}
                            </foreach>
                        </if>
                        <if test="inDto.cluesId != null">
                            <if test="inDto.cluesId == '-1'">
                                and d.clues_id is null
                            </if>
                            <if test="inDto.cluesId != '-1'">
                                and d.clues_id = #{inDto.cluesId}
                            </if>
                        </if>
                        <if test="inDto.cluesAuditStatus != null and inDto.cluesAuditStatus != ''">
                            <if test="inDto.cluesAuditStatus == '-1'">
                                and d.clues_id is null
                            </if>
                            <if test="inDto.cluesAuditStatus != '-1'">
                                and c.audit_status = #{inDto.cluesAuditStatus}
                            </if>
                        </if>
                        <if test="inDto.isNewStore != null">
                            <if test="inDto.isNewStore == 1">
                                and YEAR(ts.terminal_check_date) = YEAR(CURDATE())
                            </if>
                            <if test="inDto.isNewStore == 2">
                                and (ts.terminal_check_date is null or YEAR(ts.terminal_check_date) != YEAR(CURDATE()))
                            </if>
                        </if>
                        <!--门店渠道支持多选-->
                        <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                            AND cc3.code in
                            <foreach collection="inDto.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                                #{scc}
                            </foreach>
                        </if>

                        <!--客户渠道支持多选-->
                        <choose>
                            <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != '' and inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                                and (
                                cc1.id in
                                <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                                         close=")">
                                    #{cid}
                                </foreach>
                                or cc2.id in
                                <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                                         close=")">
                                    #{cid}
                                </foreach>
                                )
                            </when>
                            <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != ''">
                                AND cc1.id in
                                <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                                         close=")">
                                    #{cid}
                                </foreach>
                            </when>
                            <when test="inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                                AND cc2.id in
                                <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                                         close=")">
                                    #{cid}
                                </foreach>
                            </when>
                        </choose>

                        <if test="inDto.isAssociateServiceOrder != null">
                            <if test="inDto.isAssociateServiceOrder == 0">
                                and tsoi.id is null
                            </if>
                            <if test="inDto.isAssociateServiceOrder == 1">
                                and tsoi.id is not null
                            </if>
                        </if>

                        <if test="inDto.isAssociateCustomer != null">
                            <if test="inDto.isAssociateCustomer == 0">
                                <!--   and tsoi.qywx_external_username is null-->
                                and( r.external_userid IS NULL
                                OR r.external_userid ='' )
                            </if>
                            <if test="inDto.isAssociateCustomer == 1">
                                <!-- and tsoi.qywx_external_username is not null-->
                                and r.external_userid IS NOT NULL
                                AND r.external_userid!=''
                            </if>
                        </if>
                 GROUP BY
                     khr.id
                 ORDER BY
                     khr.id desc
                 limit #{pageInfo.offset}, #{pageInfo.size}
        ) t
    </select>
</mapper>