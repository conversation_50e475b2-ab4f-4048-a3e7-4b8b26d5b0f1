<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.ContentCaseUserCollectDao">
    <resultMap id="BaseResultMap" type="com.fotile.exportcenter.cmscenter.scheme.pojo.entity.ContentCaseUserCollect">
        <!--@mbg.generated-->
        <!--@Table content_case_user_collect-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="type" jdbcType="BIGINT" property="type"/>
        <result column="content_id" jdbcType="BIGINT" property="contentId"/>
        <result column="is_collect" jdbcType="INTEGER" property="isCollect"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
    </resultMap>

    <select id="getSchemeCaseCollect"
            resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.SchemeCaseStatisticalDTO">
        SELECT
        content_id as `caseId`,
        content_type as `caseType`,
        count(1) as `caseCount`
        FROM
        cmscenter.content_case_user_collect
        WHERE
        is_collect = 1 AND is_deleted = 0
        and content_type in (5,6)
        <if test="caseIds != null and caseIds.size > 0">
            AND content_id IN
            <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
        </if>
        GROUP BY
        content_id,content_type
    </select>

</mapper>