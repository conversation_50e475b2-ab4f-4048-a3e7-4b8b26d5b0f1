<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.fotile.exportcenter.marketing.dao.UserCluesDao">



    <!--根据条件查询个数返回 -->
    <select id="querySalesLeadsListForExportCounts" resultType="java.lang.Long">
        select count(sl.id)
        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on a.id = sl.activity_id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                <when test="scores.contains(0)">
                    and ( ucs.score in
                    <foreach collection="scores" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or ucs.score is null
                    )
                </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                           )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
               and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or  sl.store_sub_channel_code = '' or sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null

                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )

            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>

            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>



    <!-- 线索导出/脱敏导出 -->
    <select id="newQuerySalesLeadsListForExport"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto">
        SELECT
        sl.id,
        sl.channel_code channelCode,
        sl.radio_code radioCode,
        sl.activity_id activityId,
        a.title activityName,
        case a.type
        WHEN 1 THEN
        '报名'
        WHEN 3 THEN
        '菜谱晒作品'
        WHEN 4 THEN
        '留资'
        else ''
        end as activityType,
        sl.customer_name customerName,
        sl.funding_time fundingTime,
        CASE
        sl.gender
        WHEN 1 THEN
        '男'
        WHEN 2 THEN
        '女' ELSE '未知'
        END AS gender,
        sl.customer_phone customerPhone,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.stroe_name stroeName,
        sl.stroe_id storeId,
        sl.charge_user_id chargeUserId,
        sl.charge_user_name salesman,
        sl.charge_code chargeCode,
        sl.others others,
        sl.visit_time visitTime,
        sl.remark remark,
        sl.utm_source utmSource,
        CASE
        sl.status
        WHEN '1' THEN
        '未处理'
        WHEN '2' THEN
        '已匹配'
        WHEN '3' THEN
        '已分配'
        WHEN '4' THEN
        '已跟进'
        WHEN '5' THEN
        '已成交'
        WHEN '6' THEN
        '已取消' ELSE ''
        END AS status,
        CASE
        sl.is_into_stores
        WHEN '0' THEN
        '否' ELSE '是'
        END AS isIntoStores,
        CASE
        sl.bury_pipe
        WHEN '0' THEN
        '否' ELSE '是'
        END AS buryPipe,
        sl.decorate_progres decorateProgres,
        sl.intention_product intentionProduct,
        sl.service_process serviceProcess,
        CASE
        sl.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        sl.total_score totalScore,
        CASE
        sl.audit_status
        WHEN '1' THEN
        '待审核'
        WHEN '2' THEN
        '审核通过'
        WHEN '3' THEN
        '审核不通过'
        WHEN '40' THEN
        '未提交'
        ELSE ''
        END AS auditStatus,
        sl.province provinceName,
        sl.city cityName,
        sl.area countyName,
        sl.address address,
        sl.address_id addressId,
        sl.village_id villageId,
        sl.clues_type cluesType,
        sl.clues_source cluesSource,
        sl.clues_level cluesLevel,
        sl.house_type houseType,
        sl.house_area houseArea,
        sl.kitchen_type kitchenType,
        sl.kitchen_area kitchenArea,
        CASE
        sl.regular_subscriber
        WHEN '1' THEN
        '是' ELSE '否'
        END AS regularSubscriber,
        sl.store_type_name storeTypeName,
        sl.review_status reviewStatus,
        sl.community_member_json communityMemberJson,
        sl.decorate_designer_json decorateDesignerJson,
        sl.make_bargain_product makeBargainProduct,
        sl.make_bargain_system makeBargainSystem,
        sl.is_make_bargain isMakeBargain,
        sl.pay_status payStatus,
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id-->
        <!--        and cfu.type in ('1','2','3','4','5'))-->
        <!--        followUpCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '1' ) teleConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '2' ) weChatConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '3' ) visitConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '5' ) intoShopConnectionCount,-->
        case when sl.revise_time is null then 0 else TIMESTAMPDIFF(day,DATE_FORMAT(sl.revise_time,'%Y-%m-%d'),DATE_FORMAT(NOW(),'%Y-%m-%d'))  end   followUpNoDate,
        sl.create_user_name createUserName,
        sl.lost_order_value lostOrderValue,
        sl.lost_order_code lostOrderCOde,
        sl.lost_order_sub_code lostOrderSubCode,
        sl.lost_order_sub_value lostOrderSubValue,
        sl.self_lack_review_status_code selfLackReviewStatusCode,
        sl.self_lack_review_status_value selfLackReviewStatusValue,
        sl.revise_time reviseTime,
        sl.stroe_code stroeCode,
        case
        sl.is_houserholds_visit
        WHEN '0' THEN
        '否' ELSE '是'
        END AS
        isHouserholdsVisit,
        DATE_FORMAT(make_room_time,'%Y-%m-%d') makeRoomTime,
        decorate_type decorateType
        ,case WHEN sl.follow_up_status > '1' THEN '是' else '否' END AS isFollowUp,
        case WHEN sl.order_status = '1' THEN '是' else '否' END AS isDeal,
        case WHEN sl.follow_up_status = '5' THEN '是' else '否' END AS isLostOrder,
        sl.distributor_name distributorName,sl.first_follow_time firstFollowTime,sl.budget_accounting  budgetAccounting,
        sl.last_follow_time lastFollowTime,
        case WHEN sl.audit_status = '2' then sl.audit_time  else null end as auditTime,
        sl.is_come_devise AS isComeDevise,
        sl.come_devise_status comeDeviseStatus,
        sl.modified_charge_user_date modifiedChargeUserDate,
        uce.with_single_old_user_id,uce.with_single_old_user_name,
        uce.whether_preference,uce.preference_note,
        case when uce.whether_preference = '1' THEN '是' else '否' END  as preferenceStatusName,
        sl.lost_order_sub_value lostOrderSubValue,
        sl.lost_order_sub_code lostOrderSubCode,
        UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) followEfficiencyTime,

        <!--20243:线索列表-导出字段新增 added by zhongdian-->
        sl.assist_user_id,
        sl.assist_user_name,
        (case when uce.flag_installation=1 then '是' else '否' end) as flag_installation,
        (case when uce.flag_move_in=1 then '是' else '否' end) as flag_move_in,
        uce.urgency_follow_up,
        uce.urgency_purchase,
        uce.movein_date,
        uce.install_date,
        uce.in_store_type,
        uce.in_store_time,
        uce.introducer_name,
        uce.introducer_phone,
        uce.retention_method,
        sl.deal_order_time dealOrderTime,
        sl.visit_status visitStatus,
        case WHEN sl.order_earnest_flag = 1 THEN '是' else '否' END AS orderEarnestFlagName,
        sl.frequency_open_seas frequencyOpenSeas,
        sl.customer_bring_type customerBringType,
        sl.card_no cardNo

        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="serverCodes != null and serverCodes.size != 0">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join marketingcenter.user_clues_extend uce on sl.id=uce.user_clues_id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id

        <where>
            sl.is_deleted = 0
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList != ''">
                <choose>
                    <when test="decorateCompanyTypeList == 1"><!--家装-->
                        AND sl.decorate_company_category IN (1,2,3) AND IFNULL(decorate_company_code,'')!=''
                        <if test="drainageChannel !=null and drainageChannel != ''">
                            and sl.decorate_company_code = #{drainageChannel}
                        </if>
                    </when>
                    <when test="decorateCompanyTypeList == 2 "><!--设计师-->
                        AND sl.decorate_company_category IN (1,2,3) AND IFNULL(designer_code,'')!=''
                        <if test="drainageChannel !=null and drainageChannel != ''">
                            and sl.designer_code = #{drainageChannel}
                        </if>
                    </when>
                    <otherwise><!--异业三工-->
                        AND sl.decorate_company_category = #{decorateCompanyTypeList}
                        <if test="drainageChannel !=null and drainageChannel != ''">
                            and sl.decorate_company_code = #{drainageChannel}
                        </if>

                    </otherwise>
                </choose>
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
        </where>

        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>


        limit #{start},#{size}
    </select>


    <select id="newCluesActivityForExport" resultType="com.fotile.exportcenter.marketing.pojo.dto.CluesActivityOutDto">
        select
        sl.id,
        sl.customer_name customerName,
        sl.customer_phone customerPhone,
        sl.district_value areaName,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.distributor_name distributorName,
        sl.stroe_id storeId,
        sl.stroe_code stroeCode,
        sl.stroe_name stroeName,
        sl.create_user_name createUserName,
        sl.charge_user_name chargeUserName,
        sl.charge_code chargeCode,
        ucar.activity_id activityId,
        ucar.activity_name activityName,
        ucar.funding_time fundingTime,
        CASE ucar.record_type
        WHEN 1 THEN '新增'
        WHEN 2 THEN '报名'
        ELSE '' END
        recordType
        from marketingcenter.user_clues_activity_record ucar
        inner join marketingcenter.user_clues sl on sl.id = ucar.clues_id and sl.is_deleted = 0
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            ucar.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and   (sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null  or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">
                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>

        </where>

        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>


        limit #{start},#{size}
    </select>



    <sql id="cluesServiceActionTable">
        (
        select distinct sa.clue_related_id
        from marketingcenter.clues_follow_up sa
        where sa.is_deleted=0
          <if test="serverCodes != null and serverCodes.size != 0">
              and
              <foreach collection="serverCodes" item="sc" separator="or" open="(" close=")">
                  concat(',', sa.service_action,',') like concat( '%,',#{sc},',%')
              </foreach>
          </if>
        <if test="serverTimeStart != null">
            and sa.follow_date >= #{serverTimeStart}
        </if>
        <if test="serverTimeEnd != null">
            and sa.follow_date &lt;= #{serverTimeEnd}
        </if>
        <if test="followUpEvaluate!=null">
            and sa.evaluate_status = #{followUpEvaluate}
        </if>
        ) follow
    </sql>



    <select id="exportCluesActivityCount" resultType="java.lang.Long">
        select count(1)
        from
        (
        select sl.id
        from marketingcenter.user_clues_activity_record ucar
        inner join marketingcenter.user_clues sl on sl.id = ucar.clues_id and sl.is_deleted = 0
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            ucar.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and (  sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <!--<if test="address != null and address != ''">
                and AES_DECRYPT(UNHEX(sl.address), #{aesKey}) like concat('%',#{address},'%')
            </if>-->
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">
                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                            and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>

            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>

        </where>
        ) a
    </select>


    <select id="selectCluesFollowUpByCluesIds"
            resultType="com.fotile.exportcenter.marketing.pojo.vo.SelectCluesFollowUpByCluesIdsVO">
        select clue_related_id clueRelatedId,
        count(1) followUpCount
        , sum(case type when 1 then 1 else 0 end) teleConnectionCount
        , sum(case type when 2 then 1 else 0 end) weChatConnectionCount
        , sum(case type when 3 then 1 else 0 end) visitConnectionCount
        , sum(case type when 5 then 1 else 0 end)  intoShopConnectionCount
        from marketingcenter.clues_follow_up
        where
        is_deleted = 0
        <if test="cluesIds != null and cluesIds.size != 0">
        and clue_related_id in
        <foreach collection="cluesIds" item="item" separator="," open="(" close=")">
             #{item}
        </foreach>
        </if>
        and type in ('1','2','3','4','5')
        and audit_status = 20
        group by clue_related_id
    </select>



    <!-- 线索评分部分字段导出/脱敏导出 -->
    <select id="cluesScorePortionExport"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto">
        SELECT
        sl.id,
        sl.channel_code channelCode,
        sl.radio_code radioCode,
        sl.activity_id activityId,
        a.title activityName,
        case a.type
        WHEN 1 THEN
        '报名'
        WHEN 3 THEN
        '菜谱晒作品'
        WHEN 4 THEN
        '留资'
        else ''
        end as activityType,
        sl.customer_name customerName,
        sl.funding_time fundingTime,
        CASE
        sl.gender
        WHEN 1 THEN
        '男'
        WHEN 2 THEN
        '女' ELSE '未知'
        END AS gender,
        sl.customer_phone customerPhone,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.stroe_name stroeName,
        sl.charge_user_id chargeUserId,
        sl.charge_user_name salesman,
        sl.others others,
        CASE
        sl.visit_status
        WHEN '101' THEN
        '上门设计'
        WHEN '102' THEN
        '有购买需求'
        WHEN '103' THEN
        '未接听'
        WHEN '104' THEN
        '沟通后无意向'
        WHEN '105' THEN
        '未报名'
        WHEN '106' THEN
        '其他需求' ELSE ''
        END AS visitStatus,
        sl.visit_time visitTime,
        sl.remark remark,
        sl.utm_source utmSource,
        CASE
        sl.status
        WHEN '1' THEN
        '未处理'
        WHEN '2' THEN
        '已匹配'
        WHEN '3' THEN
        '已分配'
        WHEN '4' THEN
        '已跟进'
        WHEN '5' THEN
        '已成交'
        WHEN '6' THEN
        '已取消' ELSE ''
        END AS status,
        CASE
        sl.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        sl.clues_type cluesType,
        sl.clues_source cluesSource,
        sl.clues_level cluesLevel,
        sl.store_type_name storeTypeName,
        sl.review_status reviewStatus,
        sl.create_user_name createUserName,
        sl.lost_order_value lostOrderValue,
        sl.revise_time reviseTime,
        sl.province provinceName,
        sl.city cityName,
        sl.area countyName,
        sl.address,
        sl.village_name village,
        b.tag_all tag,
        b.score tagScore

        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join marketingcenter.user_clues_score b on sl.id = b.id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( b.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or b.score is null
                        )
                    </when>
                    <otherwise>
                        and b.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!-- <if test="customerName != null and customerName != ''">
                 and sl.customer_name =
                 #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
             </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>

        </where>

        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>


        limit #{start},#{size}
    </select>


    <!-- 线索部分字段导出/脱敏导出 -->
    <select id="cluesPortionExport"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto">
        SELECT
        sl.id,
        sl.channel_code channelCode,
        sl.radio_code radioCode,
        sl.activity_id activityId,
        a.title activityName,
        case a.type
        WHEN 1 THEN
        '报名'
        WHEN 3 THEN
        '菜谱晒作品'
        WHEN 4 THEN
        '留资'
        else ''
        end as activityType,
        sl.customer_name customerName,
        sl.funding_time fundingTime,
        CASE
        sl.gender
        WHEN 1 THEN
        '男'
        WHEN 2 THEN
        '女' ELSE '未知'
        END AS gender,
        sl.customer_phone customerPhone,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.stroe_name stroeName,
        sl.charge_user_id chargeUserId,
        sl.charge_user_name salesman,
        sl.others others,
        sl.visit_time visitTime,
        sl.remark remark,
        sl.utm_source utmSource,
        CASE
        sl.status
        WHEN '1' THEN
        '未处理'
        WHEN '2' THEN
        '已匹配'
        WHEN '3' THEN
        '已分配'
        WHEN '4' THEN
        '已跟进'
        WHEN '5' THEN
        '已成交'
        WHEN '6' THEN
        '已取消' ELSE ''
        END AS status,
        CASE
        sl.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        sl.clues_type cluesType,
        sl.clues_source cluesSource,
        sl.clues_level cluesLevel,
        sl.store_type_name storeTypeName,
        sl.review_status reviewStatus,
        sl.create_user_name createUserName,
        sl.lost_order_value lostOrderValue,
        sl.revise_time reviseTime,
        sl.visit_status visitStatus,
        sl.lost_order_sub_code lostOrderSubCode,
        sl.lost_order_sub_value lostOrderSubValue,
        sl.self_lack_review_status_code selfLackReviewStatusCode,
        sl.self_lack_review_status_value selfLackReviewStatusValue

        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>

        </where>

        group by sl.id
        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>


        limit #{start},#{size}
    </select>

    <select id="findLogCluesExport"
            resultType="java.lang.Long">
        SELECT
        sl.id

        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join
            <include refid="cluesServiceActionTable"/>
            on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and sl.address_id is not null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                            #{item.followEfficeStartDate}
                            and UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                            #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                            #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                            #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where
                        tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr
                        where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where
                ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>
        </where>

        group by sl.id
        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>

        limit #{start},#{size}
    </select>

    <select id="newQuerySalesLeadsIdListForExport" resultType="java.lang.Long">
        SELECT
        sl.id
        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=sl.id
        </if>
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join marketingcenter.user_clues_extend uce on sl.id=uce.user_clues_id
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is  null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>)
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and  sl.address_id is not  null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                        and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and  sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores  = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status  = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status  > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status  = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status  = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and  sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null  )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status  in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in  (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in  (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>

        </where>
        group by sl.id
        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>


        limit #{start},#{size}
    </select>

    <select id="newQuerySalesLeadsListForExportByIds"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto">
        SELECT
        sl.id,
        sl.channel_code channelCode,
        sl.radio_code radioCode,
        sl.activity_id activityId,
        a.title activityName,
        case a.type
        WHEN 1 THEN
        '报名'
        WHEN 3 THEN
        '菜谱晒作品'
        WHEN 4 THEN
        '留资'
        else ''
        end as activityType,
        sl.customer_name customerName,
        sl.funding_time fundingTime,
        CASE
        sl.gender
        WHEN 1 THEN
        '男'
        WHEN 2 THEN
        '女' ELSE '未知'
        END AS gender,
        sl.customer_phone customerPhone,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.stroe_name stroeName,
        sl.stroe_id storeId,
        sl.charge_user_id chargeUserId,
        sl.charge_user_name salesman,
        sl.charge_code chargeCode,
        sl.others others,
        sl.visit_time visitTime,
        sl.remark remark,
        sl.utm_source utmSource,
        CASE
        sl.status
        WHEN '1' THEN
        '未处理'
        WHEN '2' THEN
        '已匹配'
        WHEN '3' THEN
        '已分配'
        WHEN '4' THEN
        '已跟进'
        WHEN '5' THEN
        '已成交'
        WHEN '6' THEN
        '已取消' ELSE ''
        END AS status,
        CASE
        sl.is_into_stores
        WHEN '0' THEN
        '否' ELSE '是'
        END AS isIntoStores,
        CASE
        sl.bury_pipe
        WHEN '0' THEN
        '否' ELSE '是'
        END AS buryPipe,
        sl.decorate_progres decorateProgres,
        sl.intention_product intentionProduct,
        sl.service_process serviceProcess,
        CASE
        sl.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        sl.total_score totalScore,
        CASE
        sl.audit_status
        WHEN '1' THEN
        '待审核'
        WHEN '2' THEN
        '审核通过'
        WHEN '3' THEN
        '审核不通过'
        WHEN '40' THEN
        '未提交'
        ELSE ''
        END AS auditStatus,
        sl.province provinceName,
        sl.city cityName,
        sl.area countyName,
        sl.address address,
        sl.address_id addressId,
        sl.village_id villageId,
        sl.clues_type cluesType,
        sl.clues_source cluesSource,
        sl.clues_level cluesLevel,
        sl.house_type houseType,
        sl.house_area houseArea,
        sl.kitchen_type kitchenType,
        sl.kitchen_area kitchenArea,
        CASE
        sl.regular_subscriber
        WHEN '1' THEN
        '是' ELSE '否'
        END AS regularSubscriber,
        sl.store_type_name storeTypeName,

        sl.review_status reviewStatus,
        sl.community_member_json communityMemberJson,
        sl.decorate_designer_json decorateDesignerJson,
        sl.make_bargain_product makeBargainProduct,
        sl.make_bargain_system makeBargainSystem,
        sl.is_make_bargain isMakeBargain,
        sl.pay_status payStatus,
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id-->
        <!--        and cfu.type in ('1','2','3','4','5'))-->
        <!--        followUpCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '1' ) teleConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '2' ) weChatConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '3' ) visitConnectionCount,-->
        <!--        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND-->
        <!--        cfu.type = '5' ) intoShopConnectionCount,-->
        case when sl.revise_time is null then 0 else TIMESTAMPDIFF(day,DATE_FORMAT(sl.revise_time,'%Y-%m-%d'),DATE_FORMAT(NOW(),'%Y-%m-%d'))  end   followUpNoDate,
        sl.create_user_name createUserName,
        sl.lost_order_value lostOrderValue,
        sl.lost_order_code lostOrderCOde,
        sl.lost_order_sub_code lostOrderSubCode,
        sl.lost_order_sub_value lostOrderSubValue,
        sl.self_lack_review_status_code selfLackReviewStatusCode,
        sl.self_lack_review_status_value selfLackReviewStatusValue,
        sl.revise_time reviseTime,
        sl.stroe_code stroeCode,
        case
        sl.is_houserholds_visit
        WHEN '0' THEN
        '否' ELSE '是'
        END AS
        isHouserholdsVisit,
        DATE_FORMAT(make_room_time,'%Y-%m-%d') makeRoomTime,
        decorate_type decorateType
        ,case WHEN sl.follow_up_status > '1' THEN '是' else '否' END AS isFollowUp,
        case WHEN sl.order_status = '1' THEN '是' else '否' END AS isDeal,
        case WHEN sl.follow_up_status = '5' THEN '是' else '否' END AS isLostOrder,
        sl.distributor_name distributorName,sl.first_follow_time firstFollowTime,sl.budget_accounting  budgetAccounting,
        sl.last_follow_time lastFollowTime,
        case WHEN sl.audit_status = '2' then sl.audit_time  else null end as auditTime,
        sl.is_come_devise AS isComeDevise,
        sl.come_devise_status comeDeviseStatus,
        sl.modified_charge_user_date modifiedChargeUserDate,
        uce.with_single_old_user_id,uce.with_single_old_user_name,
        uce.whether_preference,uce.preference_note,
        case when uce.whether_preference = '1' THEN '是' else '否' END  as preferenceStatusName,
        sl.lost_order_sub_value lostOrderSubValue,
        sl.lost_order_sub_code lostOrderSubCode,
        sl.is_come_devise_time isComeDeviseTime,
        sl.is_scene_interaction_time isSceneInteractionTime,
        UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) followEfficiencyTime,

        <!--20243:线索列表-导出字段新增 added by zhongdian-->
        sl.assist_user_id,
        sl.assist_user_name,
        (case when uce.flag_installation=1 then '是' else '否' end) as flag_installation,
        (case when uce.flag_move_in=1 then '是' else '否' end) as flag_move_in,
        uce.urgency_follow_up,
        uce.urgency_purchase,
        uce.movein_date,
        uce.install_date,
        uce.in_store_type,
        uce.in_store_time,
        uce.introducer_name,
        uce.introducer_phone,
        uce.retention_method,
        sl.deal_order_time dealOrderTime,
        sl.visit_status visitStatus,
        case WHEN sl.order_earnest_flag = 1 THEN '是' else '否' END AS orderEarnestFlagName,
        sl.frequency_open_seas frequencyOpenSeas,
        sl.customer_bring_type customerBringType,
        sl.card_no cardNo,
        d.integrate_title integrateTitle,
        d.integrate_code integrateCode,
        sl.salesman frontSalesman,
        CASE
        sl.created_by
        WHEN 'anonymousUser' THEN '顾客主动留资'
        ELSE '导购手动录入'
        END AS creatCluesMethod,
        f.template_code,
        sl.freehand_sketching_flag,
        sl.kitchen_design_flag,
        sl.costume_change_report_flag,
        sl.is_scene_interaction,
<!--        门店类型，1：专卖店；2：KA店；3：社区店-->
        ts.store_level storeCategoryName,
        sl.designer_club_member_grade designerClubMemberGrade,
        sl.designer_club_member_id designerClubMemberId,
        sl.designer_industry_level designerIndustryLevel,
        uce.create_source_platform createSourcePlatform,
        sl.store_sub_channel_code storeSubChannelCode,
        ts.store_channel_code storeChannelCode,
        uce.house_renovation_type houseRenovationType,
        ts.develop_salesman_code developSalesmanCode,
        ts.develop_salesman_name developSalesmanName
        from marketingcenter.user_clues sl
        left join orgcenter.t_store ts on cast(sl.stroe_id as binary) = ts.org_id
        left join marketingcenter.activity a on sl.activity_id = a.id
        left join marketingcenter.user_clues_extend uce on sl.id=uce.user_clues_id
        left join marketingcenter.im_integrate_marketing d on d.id = uce.integrate_id
        left join marketingcenter.im_ref_content_template e on e.is_deleted = 0 and d.id = e.integrate_id
        left  join cmscenter.im_content_template f on f.is_deleted = 0 and f.id = e.content_template_id
        <where>
            and sl.is_deleted = 0
            and sl.id in
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
        GROUP BY sl.id
    </select>


    <select id="newCluesFollowServiceExport" resultType="com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo">
        SELECT
        sl.id,
        sl.customer_name customerName,
        sl.district_value districtValue,
        sl.company_id companyId,
        sl.company_name companyName,
        sl.store_type_name storeTypeName,
        sl.stroe_id stroeId,
        sl.stroe_code stroeCode,
        sl.stroe_name stroeName,
        sl.charge_code chargeCode,
        sl.charge_user_name salesman,
        sl.distributor_name,
        sl.channel_category_name,
        cfu.id followId,
        cfu.type,
        CASE WHEN cfu.`type` = 4 THEN '历史日志' ELSE '新增跟进' END sourceType,
        cfu.service_action serviceAction,
        cfu.content_text contentText,
        cfu.decorate_progres decorateProgres,
        cfu.charge_user_name chargeUserName,
        cfu.follow_date followDate,
        cfu.address,
        cfu.score,
        cfu.picture_url1 pictureUrl1,
        cfu.picture_url2 pictureUrl2,
        cfu.picture_url3 pictureUrl3,
        am.next_follow_up_time nextFollowUpTime,
        cfu.evaluate_status evaluateStatus,
        cfu.evaluate_user_name evaluateUserName,
        cfu.evaluate_time evaluateTime,
        cfu.evaluate_result_desc evaluateResultDesc
        from marketingcenter.user_clues sl
        inner join marketingcenter.clues_follow_up cfu on cfu.clue_related_id=sl.id and cfu.is_deleted = 0
        left join marketingcenter.t_audit_mapping am on am.source_id = cfu.id and am.source_table_name = 'clues_follow_up' and am.is_deleted = 0
        <where>
            cfu.is_deleted = 0
            <foreach open="and cfu.clue_related_id in (" collection="list" item="item" separator="," close=")">
                #{item}
            </foreach>
        </where>
        group by cfu.id
    </select>

    <select id="newCluesFollowServiceExport2" resultType="java.lang.Long">
        select sl.id
        from marketingcenter.user_clues sl
        <!-- 服务动作&服务时间过滤 added by zd -->
        inner join
        <include refid="cluesServiceActionTable2"/>
        on follow.clue_related_id=sl.id
        <if test="activityName != null and activityName != ''">
            left join marketingcenter.activity a on sl.activity_id = a.id
        </if>
        left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
        <if test="distributorId != null">
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        </if>

            left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id

        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
            sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="clueLevels != null and clueLevels.size != 0">
                <choose>
                    <when test="clueLevels.contains('-1')">

                        and (sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.clues_level is null)
                    </when>
                    <otherwise>
                        and sl.clues_level in
                        <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="scores != null and scores.size > 0">
                <choose>
                    <when test="scores.contains(0)">
                        and ( ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or ucs.score is null
                        )
                    </when>
                    <otherwise>
                        and ucs.score in
                        <foreach collection="scores" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="startUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
            </if>
            <if test="endUnfollowTime != null">
                and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
            </if>
            <if test="imIds != null and imIds.size != 0">
                and uce.integrate_id
                <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                and sl.designer_club_member_grade = #{designerClubMemberGrade}
            </if>
            <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                <choose>
                    <when test="designerClubMemberGradeList.contains('-1')">
                        and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                <choose>
                    <when test="designerClubMemberIdList.contains(-1L)">
                        and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test = "kitchenDesignFlag != null">
                and sl.kitchen_design_flag = #{kitchenDesignFlag}
            </if>
            <if test = "costumeChangeReportFlag != null">
                and sl.costume_change_report_flag = #{costumeChangeReportFlag}
            </if>
            <if test = "freehandSketchingFlag != null">
                and sl.freehand_sketching_flag = #{freehandSketchingFlag}
            </if>
            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and sl.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and sl.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="id != null">
                and sl.id=#{id}
            </if>
            <choose>
                <when test="visitStatusFlag">
                    and (sl.visit_status is null or sl.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                    )

                </when>
                <otherwise>
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        and
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <choose>
                <when test="villageFlag">
                    and (sl.village_id is null or sl.address_id is null
                    <if test="villageIds != null  and villageIds.size() != 0">
                        or sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    <if test="villageIds != null  and villageIds.size() != 0">
                        and sl.address_id is not null and sl.village_id in
                        <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                and sl.store_type_code in
                <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and sl.id in (
                <foreach collection="ids" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesType != null and cluesType.size > 0 ">

                and sl.clues_type in (
                <foreach collection="cluesType" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="activityId != null and activityId.size > 0">
                and sl.activity_id in (
                <foreach collection="activityId" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <choose>
                <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                            and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                    and (
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        or sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                    <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        and sl.channel_subdivide_code in (
                        <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                    <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        and sl.channel_category_code in (
                        <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="isIntoStores != null">
                and sl.is_into_stores = #{isIntoStores}
            </if>
            <if test="isFollowUp != null">
                <if test="isFollowUp == 0">
                    and sl.follow_up_status = 1
                </if>
                <if test="isFollowUp == 1">
                    and sl.follow_up_status > 1
                </if>
            </if>
            <if test="isLostOrder != null and isLostOrder == 1">
                and sl.follow_up_status = 5
            </if>
            <if test="isLostOrder != null and isLostOrder == 0">
                and sl.follow_up_status  <![CDATA[<]]>  5
            </if>
            <if test="orderStatus != null">
                and sl.order_status = #{orderStatus}
            </if>
            <if test="cluesSource != null and cluesSource.size > 0">
                and sl.clues_source in (
                <foreach collection="cluesSource" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="state != null and state.size > 0">
                and sl.status in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followUpStatus != null and followUpStatus.size > 0 ">
                and sl.follow_up_status in (
                <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="cluesIds != null  and cluesIds.size > 0 ">
                and sl.id in (
                <foreach collection="cluesIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyId != null and companyId.size > 0 ">
                <choose>
                    <when test="companyOrgId != null and companyOrgId == -11L">
                        and (sl.company_id is null or sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>)
                    </when>
                    <otherwise>
                        and sl.company_id in
                        <foreach collection="companyId" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="companyIds != null and companyIds.size > 0">
                and sl.company_id in (
                <foreach collection="companyIds" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="storeIds != null and storeIds.size() > 0">
                and ( sl.stroe_id in
                (
                <foreach collection="storeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
                or sl.stroe_id is null
                or sl.stroe_id = ''
                )
            </if>
            <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                and sl.stroe_id in
                <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                and sl.stroe_id in
                <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companys != null and companys.size > 0">
                and sl.company_id in (
                <foreach collection="companys" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channels != null and channels.size > 0">
                and sl.channel_code in (
                <foreach collection="channels" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="channelId != null and channelId.size > 0">
                and
                sl.channel_code in (
                <foreach collection="channelId" item="item" index="index"
                         separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="radioCodeIds != null and radioCodeIds.size > 0">
                and sl.radio_code in
                <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeId != null and stroeId != ''">
                <choose>
                    <when test="stroeId == '-1' ">
                        and (sl.stroe_id is null or sl.stroe_id = '')
                    </when>
                    <otherwise>
                        and sl.stroe_id = #{stroeId}
                    </otherwise>
                </choose>
            </if>
            <if test="customServiceCode != null and customServiceCode != ''">
                and sl.custom_service_code = #{customServiceCode}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and sl.audit_status=#{auditStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                and a.title like CONCAT(
                '%',#{activityName},'%')
            </if>
            <!--<if test="customerName != null and customerName != ''">
                and sl.customer_name =
                #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>-->
            <if test="customerPhone != null and customerPhone != ''">
                and sl.customer_phone =
                #{customerPhone}
            </if>
            <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                and sl.charge_user_id = #{salesmanId}
            </if>
            <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
            </if>
            <if test="companyName != null and companyName != ''">
                and sl.company_name like CONCAT( '%', #{companyName},'%')
            </if>
            <if test="visitStartTime != null and visitStartTime != ''">
                and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
            </if>
            <if test="startTime != null">
                and
                sl.funding_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and
                sl.funding_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="visitEndTime != null and visitEndTime != ''">
                and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
            </if>
            <if test="utmSource != null and utmSource != ''">
                and sl.utm_source like '%${utmSource}%'
            </if>
            <if test="withinType == 1 and durationStartTime != null">
                and sl.created_date <![CDATA[>=]]> #{durationStartTime}
            </if>
            <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                and sl.created_date <![CDATA[>]]> #{durationEndTime}
                and sl.created_date <![CDATA[<=]]> #{durationStartTime}
            </if>
            <if test="withinType == 3 and durationEndTime != null">
                and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
            </if>
            <if test="flag != null and flag == 0 and dueTime != null">
                and sl.created_date <![CDATA[>=]]> #{dueTime}
            </if>
            <if test="provinceCode != null">
                and sl.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                and sl.city_code = #{cityCode}
            </if>
            <if test="areaCode != null">
                and sl.area_code = #{areaCode}
            </if>
            <if test="reviseTimeStart != null">
                and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
            </if>
            <if test="reviseTimeEnd != null">
                and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
            </if>
            <if test="followUpTimeStart != null">
                and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
            </if>
            <if test="followUpTimeEnd != null">
                and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
            </if>
            <if test="isComeDevise != null and isComeDevise != ''">
                and sl.is_come_devise = #{isComeDevise}
            </if>
            <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                and sl.come_devise_status in
                <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                and sl.decorate_type in
                <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                and sl.stroe_id in
                <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 是否公海  0否 1是 -->
            <if test="isOpenSea != null">
                <choose>
                    <when test="isOpenSea == 1">
                        and sl.audit_status = '2'
                        and (sl.company_id is not null )
                        and (sl.stroe_id is not null and sl.stroe_id != '')
                        and (sl.charge_user_id is null or sl.charge_user_id = '')
                        and sl.status in ('3','4','5')
                    </when>
                    <otherwise>
                        and (sl.audit_status != '2'
                        or sl.stroe_id is null or sl.stroe_id = ''
                        or (sl.charge_user_id is not null and sl.charge_user_id != '')
                        or sl.company_id is null
                        or sl.status in ('1','2','6'))
                    </otherwise>
                </choose>
            </if>
            <if test="regularSubscriber != null and regularSubscriber != ''">
                <choose>
                    <when test='regularSubscriber != "1" '>
                        and sl.regular_subscriber != '1'
                    </when>
                    <otherwise>
                        and sl.regular_subscriber = #{regularSubscriber}
                    </otherwise>
                </choose>
            </if>
            <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                and sl.is_houserholds_visit = #{isHouserholdsVisit}
            </if>
            <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                and (
                <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                    <choose>
                        <when test="type == 1">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                        </when>
                        <when test="type == 2">
                            (sl.decorate_company_category IN (1, 2, 3)
                            AND designer_code is not null and designer_code != '' and designer_code != ' ')
                        </when>
                        <otherwise>
                            sl.decorate_company_category = #{type}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="drainageChannelList != null and drainageChannelList.size != 0">
                and (
                <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                    <choose >
                        <when test="item.type == 1 or item.type == 2 or item.type == 4">
                            sl.decorate_company_code in (#{item.code})
                        </when>
                        <otherwise>
                            sl.designer_code in (#{item.code})
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                            #{item.followEfficeStartDate}
                            and UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                            #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                            #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                            #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>

                </foreach>
                )
            </if>

            <!--<if test="serverCodes != null and serverCodes.size != 0">
                and
                <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                    CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                </foreach>
            </if>-->

            <if test="relationShipFlag != null">
                <choose>
                    <when test="relationShipFlag == 1">
                        and sl.id in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where
                        tcr.is_deleted = 0 )
                    </when>
                    <otherwise>
                        and sl.id not in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr
                        where tcr.is_deleted = 0 )
                    </otherwise>
                </choose>
            </if>
            <if test="participateActivities != null and participateActivities.size != 0">
                and sl.id in (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where
                ucar.is_deleted = 0
                and ucar.activity_id in
                <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="isSceneInteraction != null">
                and sl.is_scene_interaction = #{isSceneInteraction}
            </if>
            <if test="orderEarnestFlag != null">
                and sl.order_earnest_flag = #{orderEarnestFlag}
            </if>
            <if test="creatCluesMethod != null">
                <choose>
                    <when test="creatCluesMethod == 1">
                        and sl.created_by = 'anonymousUser'
                    </when>
                    <otherwise>
                        and sl.created_by != 'anonymousUser'
                    </otherwise>
                </choose>
            </if>
        </where>
        group by sl.id
        order by
        <if test="visitSort != null and visitSort != ''">
            sl.visit_time ${visitSort},
        </if>
        <if test="fundingTimeSort != null and fundingTimeSort != ''">
            sl.funding_time ${fundingTimeSort}
        </if>
        <if test="fundingTimeSort == null or fundingTimeSort == ''">
            sl.funding_time desc
        </if>
        limit #{start},#{size}
    </select>

    <select id="newExportCluesId" resultType="java.lang.Long">
        SELECT count(1) from (
            select sl.id
            from marketingcenter.user_clues sl
            <!-- 服务动作&服务时间过滤 added by zd -->
                inner join
                <include refid="cluesServiceActionTable2"/>
                on follow.clue_related_id=sl.id
            <if test="activityName != null and activityName != ''">
            left join marketingcenter.activity a on sl.activity_id = a.id
            </if>
            left join orgcenter.t_store s on sl.stroe_id = s.org_id and s.is_deleted = 0
            <if test="distributorId != null">
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
            </if>

            left join marketingcenter.user_clues_extend uce on uce.user_clues_id = sl.id

        left join marketingcenter.user_clues_score ucs on sl.id = ucs.id
        left join customercenter.user_address ua on sl.address_id = ua.id

        <where>
                sl.is_deleted = 0
            <if test="houseTypeIds!= null and houseTypeIds.size > 0">
                <choose>
                    <when test="houseTypeIds.contains('-1')">

                        and (ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or ua.house_type is null)
                    </when>
                    <otherwise>
                        and ua.house_type in
                        <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
                <choose>
                    <when test="designerIndustryLevelList.contains('-1')">

                        and (sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or sl.designer_industry_level is null)
                    </when>
                    <otherwise>
                        and sl.designer_industry_level in
                        <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
                <choose>
                    <when test="houseRenovationTypeIds.contains('-1')">

                        and (uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        or uce.house_renovation_type is null)
                    </when>
                    <otherwise>
                        and uce.house_renovation_type in
                        <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
                <if test="startUnfollowTime != null">
                    and if(sl.last_follow_time is not null,sl.last_follow_time &lt;= #{startUnfollowTime},sl.funding_time &lt;= #{startUnfollowTime})
                </if>
                <if test="endUnfollowTime != null">
                    and if(sl.last_follow_time is not null,sl.last_follow_time &gt;= #{endUnfollowTime},sl.funding_time &gt;= #{endUnfollowTime})
                </if>
                <if test="imIds != null and imIds.size != 0">
                    and uce.integrate_id
                    <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
                    and sl.designer_club_member_grade = #{designerClubMemberGrade}
                </if>
                <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
                    <choose>
                        <when test="designerClubMemberGradeList.contains('-1')">
                            and  ( sl.designer_club_member_grade is null or designer_club_member_grade = ''
                            <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or sl.designer_club_member_grade in (" close=")">
                                #{item}
                            </foreach>
                            )
                        </when>
                        <otherwise>
                            <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and sl.designer_club_member_grade in (" close=")">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>
                </if>
                <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
                    <choose>
                        <when test="designerClubMemberIdList.contains(-1L)">
                            and  ( sl.designer_club_member_id is null or designer_club_member_id = ''
                            <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or sl.designer_club_member_id in (" close=")">
                                #{item}
                            </foreach>
                            )
                        </when>
                        <otherwise>
                            <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and sl.designer_club_member_id in (" close=")">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>
                </if>
                <if test = "kitchenDesignFlag != null">
                    and sl.kitchen_design_flag = #{kitchenDesignFlag}
                </if>
                <if test = "costumeChangeReportFlag != null">
                    and sl.costume_change_report_flag = #{costumeChangeReportFlag}
                </if>
                <if test = "freehandSketchingFlag != null">
                    and sl.freehand_sketching_flag = #{freehandSketchingFlag}
                </if>
                <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                    and sl.id
                    <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeLevels != null and storeLevels.size()>0 ">
                    <choose>
                        <when test='storeLevels.contains("-1")'>
                            and (
                            s.store_level in
                            <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                                #{level}
                            </foreach>
                            or s.store_level is null or s.store_level = ''
                            )
                        </when>
                        <otherwise>
                            and s.store_level in
                            <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                                #{level}
                            </foreach>
                        </otherwise>
                    </choose>
                </if>
                <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                    <choose>
                        <when test="storeChannelSubdivisionCodes.contains(null)">
                            and ( sl.store_sub_channel_code is null or sl.store_sub_channel_code = '' or  sl.store_sub_channel_code in
                            <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                                #{item}
                            </foreach>
                            )
                        </when>
                        <otherwise>
                            and sl.store_sub_channel_code in
                            <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>

                </if>
                <if test="keyWordSet != null and keyWordSet.size() > 0">
                    AND
                    <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                        FIND_IN_SET(#{keyWord},s.key_word)
                    </foreach>
                </if>
                <if test="developSalesmanId != null">
                    and s.develop_salesman_id = #{developSalesmanId}
                </if>
                <if test="clueLevels != null and clueLevels.size != 0">
                    <choose>
                        <when test="clueLevels.contains('-1')">

                            and (sl.clues_level in
                            <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                            or sl.clues_level is null)
                        </when>
                        <otherwise>
                            and sl.clues_level in
                            <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>

                </if>
                <if test="scores != null and scores.size > 0">
                    <choose>
                        <when test="scores.contains(0)">
                            and ( ucs.score in
                            <foreach collection="scores" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            or ucs.score is null
                            )
                        </when>
                        <otherwise>
                            and ucs.score in
                            <foreach collection="scores" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>
                </if>
                <if test="storeStatus != null and storeStatus.size() != 0">
                    and s.status in
                    <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="distributorId != null">
                    AND d.id = #{distributorId}
                </if>
                <if test="id != null">
                    and sl.id=#{id}
                </if>
                <choose>
                    <when test="visitStatusFlag">
                        and (sl.visit_status is null or sl.visit_status = ''
                        <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                            or
                            <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                                CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                            </foreach>
                        </if>
                        )

                    </when>
                    <otherwise>
                        <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                            and
                            <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                                CONCAT(',', sl.visit_status,',') like CONCAT( '%,',#{item},',%')
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
                <choose>
                    <when test="villageFlag">
                        and (sl.village_id is null or sl.address_id is null
                        <if test="villageIds != null  and villageIds.size() != 0">
                            or sl.village_id in
                            <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                        </if>
                        )
                    </when>
                    <otherwise>
                        <if test="villageIds != null  and villageIds.size() != 0">
                            and sl.address_id is not null and sl.village_id in
                            <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
                <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
                    and sl.store_type_code in
                    <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="ids != null and ids.size > 0">
                    and sl.id in (
                    <foreach collection="ids" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="cluesType != null and cluesType.size > 0 ">

                    and sl.clues_type in (
                    <foreach collection="cluesType" item="item" index="idex" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="activityId != null and activityId.size > 0">
                    and sl.activity_id in (
                    <foreach collection="activityId" item="item" index="idex" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <choose>
                    <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
                            and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                        and (
                        <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                            sl.channel_category_code in (
                            <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                                #{item}
                            </foreach>
                            )
                        </if>
                        <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                            or sl.channel_subdivide_code in (
                            <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                                #{item}
                            </foreach>
                            )
                        </if>
                        )
                    </when>
                    <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                        <if test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                            and sl.channel_subdivide_code in (
                            <foreach collection="channelSubdivideCodeList" item="item" index="idex" separator=",">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </when>
                    <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                        <if test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                            and sl.channel_category_code in (
                            <foreach collection="channelCategoryCodeList" item="item" index="idex" separator=",">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
                <if test="isIntoStores != null">
                    and sl.is_into_stores = #{isIntoStores}
                </if>
                <if test="isFollowUp != null">
                    <if test="isFollowUp == 0">
                        and sl.follow_up_status = 1
                    </if>
                    <if test="isFollowUp == 1">
                        and sl.follow_up_status > 1
                    </if>
                </if>
                <if test="isLostOrder != null and isLostOrder == 1">
                    and sl.follow_up_status = 5
                </if>
                <if test="isLostOrder != null and isLostOrder == 0">
                    and sl.follow_up_status  <![CDATA[<]]>  5
                </if>
                <if test="orderStatus != null">
                    and sl.order_status = #{orderStatus}
                </if>
                <if test="cluesSource != null and cluesSource.size > 0">
                    and sl.clues_source in (
                    <foreach collection="cluesSource" item="item" index="idex" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="state != null and state.size > 0">
                    and sl.status in
                    <foreach collection="state" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="followUpStatus != null and followUpStatus.size > 0 ">
                    and sl.follow_up_status in (
                    <foreach collection="followUpStatus" item="item" index="idex" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="cluesIds != null  and cluesIds.size > 0 ">
                    and sl.id in (
                    <foreach collection="cluesIds" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="companyId != null and companyId.size > 0 ">
                    <choose>
                        <when test="companyOrgId != null and companyOrgId == -11L">
                            and (sl.company_id is null or sl.company_id in
                            <foreach collection="companyId" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>)
                        </when>
                        <otherwise>
                            and sl.company_id in
                            <foreach collection="companyId" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </otherwise>
                    </choose>
                </if>
                <if test="companyIds != null and companyIds.size > 0">
                    and sl.company_id in (
                    <foreach collection="companyIds" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="storeIds != null and storeIds.size() > 0">
                    and ( sl.stroe_id in
                    (
                    <foreach collection="storeIds" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                    or sl.stroe_id is null
                    or sl.stroe_id = ''
                    )
                </if>
                <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
                    and sl.stroe_id in
                    <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="distributorStoreIds != null and distributorStoreIds.size > 0">
                    and sl.stroe_id in
                    <foreach collection="distributorStoreIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="companys != null and companys.size > 0">
                    and sl.company_id in (
                    <foreach collection="companys" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="channels != null and channels.size > 0">
                    and sl.channel_code in (
                    <foreach collection="channels" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="channelId != null and channelId.size > 0">
                    and
                    sl.channel_code in (
                    <foreach collection="channelId" item="item" index="index"
                             separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="radioCodeIds != null and radioCodeIds.size > 0">
                    and sl.radio_code in
                    <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="stroeId != null and stroeId != ''">
                    <choose>
                        <when test="stroeId == '-1' ">
                            and (sl.stroe_id is null or sl.stroe_id = '')
                        </when>
                        <otherwise>
                            and sl.stroe_id = #{stroeId}
                        </otherwise>
                    </choose>
                </if>
                <if test="customServiceCode != null and customServiceCode != ''">
                    and sl.custom_service_code = #{customServiceCode}
                </if>
                <if test="auditStatus != null and auditStatus != ''">
                    and sl.audit_status=#{auditStatus}
                </if>
                <if test="activityName != null and activityName != ''">
                    and a.title like CONCAT(
                    '%',#{activityName},'%')
                </if>
                <!--<if test="customerName != null and customerName != ''">
                    and sl.customer_name =
                    #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
                </if>-->
                <if test="customerPhone != null and customerPhone != ''">
                    and sl.customer_phone =
                    #{customerPhone}
                </if>
                <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
                    and sl.charge_user_id = #{salesmanId}
                </if>
                <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
                    and (sl.charge_user_id is null or sl.charge_user_id = '-1' or sl.charge_user_id = '')
                </if>
                <if test="companyName != null and companyName != ''">
                    and sl.company_name like CONCAT( '%', #{companyName},'%')
                </if>
                <if test="visitStartTime != null and visitStartTime != ''">
                    and sl.visit_time <![CDATA[>=]]>#{visitStartTime}
                </if>
                <if test="startTime != null">
                    and
                    sl.funding_time <![CDATA[>=]]> #{startTime}
                </if>
                <if test="endTime != null">
                    and
                    sl.funding_time <![CDATA[<=]]> #{endTime}
                </if>
                <if test="visitEndTime != null and visitEndTime != ''">
                    and sl.visit_time <![CDATA[<=]]>#{visitEndTime}
                </if>
                <if test="utmSource != null and utmSource != ''">
                    and sl.utm_source like '%${utmSource}%'
                </if>
                <if test="withinType == 1 and durationStartTime != null">
                    and sl.created_date <![CDATA[>=]]> #{durationStartTime}
                </if>
                <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
                    and sl.created_date <![CDATA[>]]> #{durationEndTime}
                    and sl.created_date <![CDATA[<=]]> #{durationStartTime}
                </if>
                <if test="withinType == 3 and durationEndTime != null">
                    and sl.created_date <![CDATA[<=]]>  #{durationEndTime}
                </if>
                <if test="flag != null and flag == 0 and dueTime != null">
                    and sl.created_date <![CDATA[>=]]> #{dueTime}
                </if>
                <if test="provinceCode != null">
                    and sl.province_code = #{provinceCode}
                </if>
                <if test="cityCode != null">
                    and sl.city_code = #{cityCode}
                </if>
                <if test="areaCode != null">
                    and sl.area_code = #{areaCode}
                </if>
                <if test="reviseTimeStart != null">
                    and sl.revise_time <![CDATA[>=]]>#{reviseTimeStart}
                </if>
                <if test="reviseTimeEnd != null">
                    and sl.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
                </if>
                <if test="followUpTimeStart != null">
                    and sl.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
                </if>
                <if test="followUpTimeEnd != null">
                    and sl.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
                </if>
                <if test="auditTimeStart != null">
                    and sl.audit_time <![CDATA[>=]]>#{auditTimeStart}
                </if>
                <if test="auditTimeEnd != null">
                    and sl.audit_time <![CDATA[<=]]>#{auditTimeEnd}
                </if>
                <if test="isComeDevise != null and isComeDevise != ''">
                    and sl.is_come_devise = #{isComeDevise}
                </if>
                <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
                    and sl.come_devise_status in
                    <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
                    and sl.decorate_type in
                    <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
                    and sl.stroe_id in
                    <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 是否公海  0否 1是 -->
                <if test="isOpenSea != null">
                    <choose>
                        <when test="isOpenSea == 1">
                            and sl.audit_status = '2'
                            and (sl.company_id is not null )
                            and (sl.stroe_id is not null and sl.stroe_id != '')
                            and (sl.charge_user_id is null or sl.charge_user_id = '')
                            and sl.status in ('3','4','5')
                        </when>
                        <otherwise>
                            and (sl.audit_status != '2'
                            or sl.stroe_id is null or sl.stroe_id = ''
                            or (sl.charge_user_id is not null and sl.charge_user_id != '')
                            or sl.company_id is null
                            or sl.status in ('1','2','6'))
                        </otherwise>
                    </choose>
                </if>
                <if test="regularSubscriber != null and regularSubscriber != ''">
                    <choose>
                        <when test='regularSubscriber != "1" '>
                            and sl.regular_subscriber != '1'
                        </when>
                        <otherwise>
                            and sl.regular_subscriber = #{regularSubscriber}
                        </otherwise>
                    </choose>
                </if>
                <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
                    and sl.is_houserholds_visit = #{isHouserholdsVisit}
                </if>
                <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
                    and (
                    <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                        <choose>
                            <when test="type == 1">
                                (sl.decorate_company_category IN (1, 2, 3)
                            AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                            </when>
                            <when test="type == 2">
                                (sl.decorate_company_category IN (1, 2, 3)
                                AND designer_code is not null and designer_code != '' and designer_code != ' ')
                            </when>
                            <otherwise>
                                sl.decorate_company_category = #{type}
                            </otherwise>
                        </choose>
                    </foreach>
                    )
                </if>
                <if test="drainageChannelList != null and drainageChannelList.size != 0">
                    and (
                    <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                        <choose >
                            <when test="item.type == 1 or item.type == 2 or item.type == 4">
                                sl.decorate_company_code in (#{item.code})
                            </when>
                            <otherwise>
                                sl.designer_code in (#{item.code})
                            </otherwise>
                        </choose>
                    </foreach>
                    )
                </if>
                <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
                    and (
                    <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">

                        <choose>
                            <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                                ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                                #{item.followEfficeStartDate}
                                and UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                                #{item.followEfficeEndDate})
                            </when>
                            <when test="item.followEfficeStartDate != null">
                                ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[>]]>
                                #{item.followEfficeStartDate})
                            </when>
                            <otherwise>
                                ( UNIX_TIMESTAMP(sl.first_follow_time) - UNIX_TIMESTAMP(sl.audit_time) <![CDATA[<=]]>
                                #{item.followEfficeEndDate})
                            </otherwise>
                        </choose>

                    </foreach>
                    )
                </if>

                <!--<if test="serverCodes != null and serverCodes.size != 0">
                    and
                    <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                        CONCAT(',', sl.service_action,',') like CONCAT( '%,',#{item},',%')
                    </foreach>
                </if>-->

                <if test="relationShipFlag != null">
                    <choose>
                        <when test="relationShipFlag == 1">
                            and sl.id in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr where
                            tcr.is_deleted = 0 )
                        </when>
                        <otherwise>
                            and sl.id not in (SELECT clues_id FROM marketingcenter.`t_qywx_customer_clues_relation` tcr
                            where tcr.is_deleted = 0 )
                        </otherwise>
                    </choose>
                </if>
                <if test="participateActivities != null and participateActivities.size != 0">
                    and sl.id in (SELECT clues_id FROM marketingcenter.`user_clues_activity_record` ucar where
                    ucar.is_deleted = 0
                    and ucar.activity_id in
                    <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="isSceneInteraction != null">
                    and sl.is_scene_interaction = #{isSceneInteraction}
                </if>
                <if test="orderEarnestFlag != null">
                    and sl.order_earnest_flag = #{orderEarnestFlag}
                </if>
                <if test="creatCluesMethod != null">
                    <choose>
                        <when test="creatCluesMethod == 1">
                            and sl.created_by = 'anonymousUser'
                        </when>
                        <otherwise>
                            and sl.created_by != 'anonymousUser'
                        </otherwise>
                    </choose>
                </if>

            </where>
            group by sl.id
        ) a
    </select>

    <sql id="cluesServiceActionTable2">
        (
        select distinct sa.clue_related_id
        from marketingcenter.clues_follow_up sa
        where sa.is_deleted=0
        <if test="serverCodes != null and serverCodes.size > 0">
            and
            <foreach collection="serverCodes" item="sc" separator="or" open="(" close=")">
                concat(',', sa.service_action,',') like concat( '%,',#{sc},',%')
            </foreach>
        </if>
        <if test="serverTimeStart != null">
            and sa.follow_date >= #{serverTimeStart}
        </if>
        <if test="serverTimeEnd != null">
            and sa.follow_date &lt;= #{serverTimeEnd}
        </if>
        <if test="followUpEvaluate!=null">
            and sa.evaluate_status = #{followUpEvaluate}
        </if>
        ) follow
    </sql>

    <select id="newCluesFollowServiceExportCount" resultType="java.lang.Long">
        SELECT count(1) FROM (
        SELECT
        cfu.id
        from marketingcenter.user_clues sl
        inner join marketingcenter.clues_follow_up cfu on cfu.clue_related_id=sl.id and cfu.is_deleted = 0
        <where>
            cfu.is_deleted = 0
            <foreach open="and cfu.clue_related_id in (" collection="list" item="item" separator="," close=")">
                #{item}
            </foreach>
        </where>
        ) a
    </select>

    <select id="getCluesServiceByCluesIds" resultType="com.fotile.exportcenter.marketing.pojo.dto.CluesServiceInfoDto">
        select
        id,
        clues_id cluesId,
        csm_order_code csmOrderCode,
        service_engineer_csn serviceEngineerCsn
        from t_clues_service_info
        where is_deleted = 0
          and clues_id in
         <foreach collection="list" item="item" separator="," open="(" close=")">
              #{item}
        </foreach>
    </select>


    <select id="getUserCluesAdvertiseSourceByCluesIds"  resultType="com.fotile.exportcenter.marketing.pojo.entity.UserCluesAdvertiseSource">
        select
        id,
        is_deleted,
        created_by,
        created_date,
        modified_by,
        modified_date,
        user_clues_id,
        type,
        advertise_source_account_name,
        advertise_source_order_id,
        advertise_source_clue_id
        from user_clues_advertise_source
        where is_deleted = 0
        and user_clues_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>