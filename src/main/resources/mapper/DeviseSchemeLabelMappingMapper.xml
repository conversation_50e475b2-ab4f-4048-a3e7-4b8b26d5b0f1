<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeLabelMappingMapper">

    <select id="selectPropertyListBySchemeId"
            resultType="com.fotile.exportcenter.cmscenter.scheme.pojo.dto.DeviseSchemeLabelMappingDTO">
        select
        tlm.id,
        tlm.scheme_id,
        tlm.property_parameter_id,
        tlm.field_id
        from cmscenter.t_devise_scheme_label_mapping tlm
        where tlm.is_deleted = 0
        and tlm.field_id != 'kitchen_area_range'
        and tlm.scheme_id
        <foreach collection="list" item="item" separator="," close=")" open="in (">
            #{item}
        </foreach>
    </select>


</mapper>