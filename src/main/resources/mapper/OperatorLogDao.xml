<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.fotile.exportcenter.marketing.dao.OperatorLogDao">

    <!-- 返回线索日志导出个数-->
    <select id="exportOperatorLogCount"  resultType="java.lang.Long" parameterType="com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto">
        select sum(count)
        from
        (
            select count(1) as count
            from marketingcenter.user_clues uc
            <!-- 服务动作&服务时间过滤 added by zd -->
            <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
                inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
            </if>
            left join marketingcenter.activity a on uc.activity_id = a.id
            inner join marketingcenter.operator_marketing_log opl on uc.id = opl.source_id and opl.source_table_name = 'user_clues'
            left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 and s.org_id = dm.org_id and dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 and d.id = dm.distributor_id
            left join marketingcenter.user_clues_extend uce on uce.user_clues_id = uc.id
            left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
                <include refid="common_where_if"/>
            </where>

            union all

            select count(1) as count
            from marketingcenter.user_clues uc
            <!-- 服务动作&服务时间过滤 added by zd -->
            <if test="serverCodes != null and serverCodes.size != 0">
                inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
            </if>
            left join marketingcenter.activity a on uc.activity_id = a.id
            inner join marketingcenter.clues_follow_up cfu on uc.id = cfu.clue_related_id
            left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
            left join marketingcenter.user_clues_extend uce on uce.user_clues_id = uc.id
            <if test="followUpEvaluate!=null">
                inner join marketingcenter.clues_follow_up follow on follow.clue_related_id = uc.id
                and follow.is_deleted = 0 and follow.evaluate_status = #{followUpEvaluate}
            </if>
             left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
                <include refid="common_where_if"/>
            </where>

        ) aa
    </select>



    <!-- 返回线索日志导出信息-->
    <select id="newExportOperatorLogList"  resultType="com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto"
            parameterType="com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto">
        select *
        from
        (
            select
                opl.id id,
                'middleground' genre,
                'operator_marketing_log' sourceTableName,
                opl.event_description type,
                opl.event_description title,
                opl.description description,
                '' contentText,
                opl.created_date createdDate,
                opl.source_id sourceId,
                '' decorateProgres,
                '' serviceAction,
                opl.created_date followDate,
                '' address,
                opl.created_by chargeUserId,
                opl.`type` appType,
                opl.operator_name chargeUserName,
                '' `describe`,
                null serviceDate,
                '' serviceType,
                uc.stroe_id storeId,
                uc.stroe_name storeName,
                uc.company_id companyId,
                uc.company_name companyName,
                uc.address_id addressId,
                uc.village_id villageId,
                uc.customer_name customerName,
                uc.customer_phone customerPhone,
                uc.charge_user_name salesMan,
                uc.charge_code chargeCode,
                uc.create_user_name createUserName,
                uc.stroe_code stroeCode,
                uc.distributor_name distributorName,
                case uc.follow_up_status
                WHEN '1' THEN
                '未跟进'
                WHEN '2' THEN
                '已跟进'
                WHEN '3' THEN
                '已进店'
                WHEN '4' THEN
                '已成交'
                WHEN '5' THEN
                '已丢单' ELSE ''
                END AS followUpStatus,
                uc.is_come_devise AS isComeDevise,
                null score,
                uc.come_devise_status comeDeviseStatus,
                null lengthOfStay

            from marketingcenter.user_clues uc
            <!-- 服务动作&服务时间过滤 added by zd -->
            <if test="serverCodes != null and serverCodes.size != 0">
                inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
            </if>
            left join marketingcenter.activity a on uc.activity_id = a.id
            inner join marketingcenter.operator_marketing_log opl on uc.id = opl.source_id and opl.source_table_name = 'user_clues'
            left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 and s.org_id = dm.org_id and dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 and d.id = dm.distributor_id
        left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
                <include refid="common_where_if"/>
            </where>

            union all

            select
                cfu.id id,
                'appUserClues' genre,
                'clues_follow_up' sourceTableName,
                CASE
                WHEN cfu.`type` = 4 THEN
                '历史日志' ELSE '新增跟进'
                END `type`,
                cfu.title title,
                '' description,
                cfu.content_text contentText,
                cfu.created_date createdDate,
                cfu.source_id sourceId,
                cfu.decorate_progres decorateProgres,
                cfu.service_action serviceAction,
                cfu.follow_date followDate,
                cfu.address address,
                cfu.charge_user_id chargeUserId,
                cfu.`type` appType,
                cfu.charge_user_name chargeUserName,
                '' `describe`,
                null serviceDate,
                '' serviceType,
                uc.stroe_id storeId,
                uc.stroe_name storeName,
                uc.company_id companyId,
                uc.company_name companyName,
                uc.address_id addressId,
                uc.village_id villageId,
                uc.customer_name customerName,
                uc.customer_phone customerPhone,
                uc.charge_user_name salesMan,
                uc.charge_code chargeCode,
                uc.create_user_name createUserName,
                uc.stroe_code stroeCode,
                uc.distributor_name distributorName,
                case uc.follow_up_status
                WHEN '1' THEN
                '未跟进'
                WHEN '2' THEN
                '已跟进'
                WHEN '3' THEN
                '已进店'
                WHEN '4' THEN
                '已成交'
                WHEN '5' THEN
                '已丢单' ELSE ''
                END AS followUpStatus,
                uc.is_come_devise AS isComeDevise,
                cfu.score score,
                uc.come_devise_status comeDeviseStatus,
                length_of_stay lengthOfStay

            from marketingcenter.user_clues uc
            <!-- 服务动作&服务时间过滤 added by zd -->
            <if test="serverCodes != null and serverCodes.size != 0">
                inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
            </if>
            left join marketingcenter.activity a on uc.activity_id = a.id
            inner join marketingcenter.clues_follow_up cfu on uc.id = cfu.clue_related_id
            left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
            left join orgcenter.t_distributor_mapping dm on dm.type = 3 AND s.org_id = dm.org_id AND dm.is_deleted = 0
            left join orgcenter.t_distributor d on d.is_deleted = 0 AND d.id = dm.distributor_id
        left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
                <include refid="common_where_if"/>
            </where>

        ) aa
        order by aa.sourceId asc, aa.createdDate desc
        limit #{start},#{size}
    </select>


    <select id="exportPhoneLogCount" resultType="java.lang.Long">
        select count(1) as count
        from marketingcenter.user_clues uc
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
        </if>
        left join marketingcenter.activity a on uc.activity_id = a.id
        inner join marketingcenter.operator_marketing_log opl on uc.id = opl.source_id and opl.source_table_name = 'user_clues'
        left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 and s.org_id = dm.org_id and dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 and d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = uc.id
        left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
            <include refid="common_where_if"/>
            and opl.event_description_code = 'clues_call'

        </where>
    </select>

    <select id="exportPhoneLogExport"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto">
        SELECT
        opl.id id,
        'middleground' genre,
        'operator_marketing_log' sourceTableName,
        opl.event_description type,
        opl.event_description title,
        opl.description description,
        opl.old_data oldData,
        '' contentText,
        opl.created_date createdDate,
        opl.source_id sourceId,
        '' decorateProgres,
        '' serviceAction,
        opl.created_date followDate,
        '' address,
        opl.created_by chargeUserId,
        opl.`type` appType,
        opl.operator_name chargeUserName,
        '' `describe`,
        null serviceDate,
        '' serviceType,
        uc.stroe_id storeId,
        uc.stroe_name storeName,
        uc.company_id companyId,
        uc.company_name companyName,
        uc.address_id addressId,
        uc.village_id villageId,
        uc.customer_name customerName,
        uc.customer_phone customerPhone,
        uc.charge_user_name salesMan,
        uc.charge_code chargeCode,
        uc.create_user_name createUserName,
        uc.stroe_code stroeCode,
        uc.distributor_name distributorName,
        case uc.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        uc.is_come_devise AS isComeDevise,
        null score,
        uc.come_devise_status comeDeviseStatus

        FROM marketingcenter.user_clues uc
        <!-- 服务动作&服务时间过滤 added by zd -->
        <if test="(serverCodes != null and serverCodes.size != 0) or (followUpEvaluate!=null)">
            inner join <include refid="cluesServiceActionTable" /> on follow.clue_related_id=uc.id
        </if>
        left join marketingcenter.activity a on uc.activity_id = a.id
        inner join marketingcenter.operator_marketing_log opl on uc.id = opl.source_id and opl.source_table_name = 'user_clues'
        left join orgcenter.t_store s on uc.stroe_id = s.org_id and s.is_deleted = 0
        left join orgcenter.t_distributor_mapping dm on dm.type = 3 and s.org_id = dm.org_id and dm.is_deleted = 0
        left join orgcenter.t_distributor d on d.is_deleted = 0 and d.id = dm.distributor_id
        left join marketingcenter.user_clues_extend uce on uce.user_clues_id = uc.id
        left join marketingcenter.user_clues_score ucs on uc.id = ucs.id
        left join customercenter.user_address ua on uc.address_id = ua.id

        <where>
            <include refid="common_where_if"/>
            and opl.event_description_code = 'clues_call'

            <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
                and uc.id
                <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by opl.created_date desc
        limit #{start},#{size}
    </select>


    <!--设置导出的查询条件，统一在这个地方，便于统一维护-->
    <sql id="common_where_if">
        uc.is_deleted = 0
        <if test="houseTypeIds!= null and houseTypeIds.size > 0">
            <choose>
                <when test="houseTypeIds.contains('-1')">

                    and (ua.house_type in
                    <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or ua.house_type is null)
                </when>
                <otherwise>
                    and ua.house_type in
                    <foreach collection="houseTypeIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="designerIndustryLevelList!= null and designerIndustryLevelList.size > 0">
            <choose>
                <when test="designerIndustryLevelList.contains('-1')">

                    and (uc.designer_industry_level in
                    <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or uc.designer_industry_level is null)
                </when>
                <otherwise>
                    and uc.designer_industry_level in
                    <foreach collection="designerIndustryLevelList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="houseRenovationTypeIds!= null and houseRenovationTypeIds.size > 0">
            <choose>
                <when test="houseRenovationTypeIds.contains('-1')">

                    and (uce.house_renovation_type in
                    <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or uce.house_renovation_type is null)
                </when>
                <otherwise>
                    and uce.house_renovation_type in
                    <foreach collection="houseRenovationTypeIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="clueLevels != null and clueLevels.size != 0">
            <choose>
                <when test="clueLevels.contains('-1')">

                    and (uc.clues_level in
                    <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or uc.clues_level is null)
                </when>
                <otherwise>
                    and uc.clues_level in
                    <foreach collection="clueLevels" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>

        </if>
        <if test="scores != null and scores.size > 0">
            <choose>
                <when test="scores.contains(0)">
                    and ( ucs.score in
                    <foreach collection="scores" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or ucs.score is null
                    )
                </when>
                <otherwise>
                    and ucs.score in
                    <foreach collection="scores" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="id!=null">
            and uc.id=#{id}
        </if>
        <if test="startUnfollowTime != null">
            and if(uc.last_follow_time is not null,uc.last_follow_time &lt;= #{startUnfollowTime},uc.funding_time &lt;= #{startUnfollowTime})
        </if>
        <if test="endUnfollowTime != null">
            and if(uc.last_follow_time is not null,uc.last_follow_time &gt;= #{endUnfollowTime},uc.funding_time &gt;= #{endUnfollowTime})
        </if>
        <if test="designerClubMemberGrade != null and designerClubMemberGrade != ''">
            and uc.designer_club_member_grade = #{designerClubMemberGrade}
        </if>
        <if test="designerClubMemberGradeList != null and designerClubMemberGradeList.size != 0">
            <choose>
                <when test="designerClubMemberGradeList.contains('-1')">
                 and  ( uc.designer_club_member_grade is null or designer_club_member_grade = ''
                <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" or uc.designer_club_member_grade in (" close=")">
                    #{item}
                </foreach>
                    )
                </when>
                <otherwise>
                    <foreach collection="designerClubMemberGradeList" item="item" separator="," open=" and uc.designer_club_member_grade in (" close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="designerClubMemberIdList != null and designerClubMemberIdList.size != 0">
            <choose>
                <when test="designerClubMemberIdList.contains(-1L)">
                    and  ( uc.designer_club_member_id is null or uc.designer_club_member_id = ''
                    <foreach collection="designerClubMemberIdList" item="item" separator="," open=" or uc.designer_club_member_id in (" close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <otherwise>
                    <foreach collection="designerClubMemberIdList" item="item" separator="," open=" and uc.designer_club_member_id in (" close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test = "kitchenDesignFlag != null">
            and uc.kitchen_design_flag = #{kitchenDesignFlag}
        </if>
        <if test = "costumeChangeReportFlag != null">
            and uc.costume_change_report_flag = #{costumeChangeReportFlag}
        </if>
        <if test = "freehandSketchingFlag != null">
            and uc.freehand_sketching_flag = #{freehandSketchingFlag}
        </if>
        <if test="imIds != null and imIds.size != 0">
            and uce.integrate_id
            <foreach collection="imIds" item="item" separator="," open="in (" close=")">
                #{item}
            </foreach>
        </if>
        <if test="fuzzyMatchCluesIds != null and fuzzyMatchCluesIds.size != 0">
            and uc.id
            <foreach collection="fuzzyMatchCluesIds" item="item" separator="," open="in (" close=")">
                #{item}
            </foreach>
        </if>
        <if test="storeLevels != null and storeLevels.size()>0 ">
            <choose>
                <when test='storeLevels.contains("-1")'>
                    and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                    )
                </when>
                <otherwise>
                    and s.store_level in
                    <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                        #{level}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
            <choose>
                <when test="storeChannelSubdivisionCodes.contains(null)">
                    and ( uc.store_sub_channel_code is null or uc.store_sub_channel_code = '' or   uc.store_sub_channel_code in
                    <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    )
                </when>
                <otherwise>
                    and uc.store_sub_channel_code in
                    <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>

        </if>
        <if test="keyWordSet != null and keyWordSet.size() > 0">
            and
            <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                find_in_set(#{keyWord},s.key_word)
            </foreach>
        </if>
        <if test="developSalesmanId != null">
            and s.develop_salesman_id = #{developSalesmanId}
        </if>
        <if test="storeStatus != null and storeStatus.size() != 0">
            and s.status in
            <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="distributorId != null">
            and d.id = #{distributorId}
        </if>
        <choose>
            <when test="visitStatusFlag">
                and (
                    uc.visit_status is null or uc.visit_status = ''
                    <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                        or
                        <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                            concat(',', uc.visit_status,',') like concat( '%,',#{item},',%')
                        </foreach>
                    </if>
                )
            </when>
            <otherwise>
                <if test="visitStatusCodes != null and visitStatusCodes.size != 0">
                    and
                    <foreach collection="visitStatusCodes" item="item" separator="or" open="(" close=")">
                        concat(',', uc.visit_status,',') like concat( '%,',#{item},',%')
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="villageFlag">
                and (uc.village_id is null or uc.address_id is  null
                <if test="villageIds != null  and villageIds.size() != 0">
                    or uc.village_id in
                    <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>)
            </when>
            <otherwise>
                <if test="villageIds != null  and villageIds.size() != 0">
                    and  uc.address_id is not  null and uc.village_id in
                    <foreach collection="villageIds" item="item" index="idex" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test="ids != null and ids.size>0">
            and uc.id in
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cluesIds != null and cluesIds.size > 0 ">
            and uc.id in
            <foreach collection="cluesIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="storeTypeCodeList != null and storeTypeCodeList.size > 0">
            and uc.store_type_code in
            <foreach collection="storeTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="(channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0 )
				and  (channelCategoryCodeList != null and channelCategoryCodeList.size > 0)">
                and (
                    uc.channel_category_code in
                    <foreach collection="channelCategoryCodeList" item="item" index="idex" open="(" separator="," close=")">
                        #{item}
                    </foreach>

                    or uc.channel_subdivide_code in
                    <foreach collection="channelSubdivideCodeList" item="item" index="idex" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </when>
            <when test="channelSubdivideCodeList != null and channelSubdivideCodeList.size > 0">
                and uc.channel_subdivide_code in
                <foreach collection="channelSubdivideCodeList" item="item" index="idex" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="channelCategoryCodeList != null and channelCategoryCodeList.size > 0">
                and  uc.channel_category_code in
                <foreach collection="channelCategoryCodeList" item="item" index="idex" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="cluesType != null and cluesType.size > 0 ">
            and uc.clues_type in
            <foreach collection="cluesType" item="item" index="idex" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activityId != null and activityId.size > 0">
            and uc.activity_id in
            <foreach collection="activityId" item="item" index="idex" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="isIntoStores != null">
            and uc.is_into_stores  = #{isIntoStores}
        </if>
        <if test="isFollowUp != null">
            <if test="isFollowUp == 0">
                and uc.follow_up_status  = 1
            </if>
            <if test="isFollowUp == 1">
                and uc.follow_up_status  > 1
            </if>
        </if>
        <if test="isLostOrder != null and isLostOrder == 1">
            and uc.follow_up_status  = 5
        </if>
        <if test="isLostOrder != null and isLostOrder == 0">
            and uc.follow_up_status  <![CDATA[<]]>  5
        </if>
        <if test="orderStatus != null">
            and uc.order_status  = #{orderStatus}
        </if>
        <if test="cluesSource != null and cluesSource.size > 0">
            and uc.clues_source in
            <foreach collection="cluesSource" item="item" index="idex" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="state != null and state.size > 0">
            and uc.status in
            <foreach collection="state" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="followUpStatus != null and followUpStatus.size > 0 ">
            and uc.follow_up_status in
            <foreach collection="followUpStatus" item="item" index="idex" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyId != null and companyId.size > 0 ">
            <choose>
                <when test="companyOrgId != null and companyOrgId == -11L">
                    and (uc.company_id is null or uc.company_id in
                    <foreach collection="companyId" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>)
                </when>
                <otherwise>
                    and uc.company_id in
                    <foreach collection="companyId" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="companyIds != null and companyIds.size>0">
            and uc.company_id in
            <foreach collection="companyIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" stroeId != null and stroeId != '' ">
            <choose>
                <when test="stroeId == '-1' ">
                    and (uc.stroe_id is null or uc.stroe_id = '')
                </when>
                <otherwise>
                    and uc.stroe_id = #{stroeId}
                </otherwise>
            </choose>
        </if>
        <if test="storeIds != null and storeIds.size() > 0">
            and ( uc.stroe_id in
            <foreach collection="storeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            or uc.stroe_id is null
            or uc.stroe_id = ''
            )
        </if>
        <if test="stroeLabelOrgIds  != null and stroeLabelOrgIds.size() > 0">
            and  uc.stroe_id in
            <foreach collection="stroeLabelOrgIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="channelId != null and channelId.size > 0 ">
            and uc.channel_code in
            <foreach collection="channelId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="radioCodeIds != null and radioCodeIds.size > 0 ">
            and uc.radio_code in
            <foreach collection="radioCodeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="customServiceCode != null and customServiceCode != '' ">
            and uc.custom_service_code = #{customServiceCode}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            and uc.audit_status=#{auditStatus}
        </if>
        <if test="activityName != null and activityName != ''">
            and a.title like concat('%',#{activityName},'%')
        </if>
        <if test="customerPhone != null and customerPhone != ''">
            and uc.customer_phone = #{customerPhone}
        </if>
        <if test="salesmanId != null and salesmanId != ''  and salesmanId != '-1'">
            and uc.charge_user_id = #{salesmanId}
        </if>
        <if test="salesmanId != null and salesmanId != '' and salesmanId == '-1'">
            and (uc.charge_user_id is null or uc.charge_user_id = '-1' or uc.charge_user_id = '')
        </if>
        <if test="companyName != null and companyName != ''">
            and uc.company_name like concat( '%', #{companyName},'%')
        </if>
        <if test="visitStartTime !=null and visitStartTime !=''">
            and uc.visit_time <![CDATA[>=]]>#{visitStartTime}
        </if>
        <if test=" startTime != null  ">
            and uc.funding_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test=" endTime != null   ">
            and uc.funding_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="visitEndTime !=null and visitEndTime !=''">
            and uc.visit_time <![CDATA[<=]]>#{visitEndTime}
        </if>
        <if test="utmSource != null and utmSource != ''">
            and uc.utm_source like '%${utmSource}%'
        </if>
        <if test="withinType == 1 and durationStartTime != null">
            and uc.created_date <![CDATA[>=]]> #{durationStartTime}
        </if>
        <if test="withinType == 2 and durationStartTime != null and durationEndTime != null">
            and uc.created_date <![CDATA[>]]> #{durationEndTime}
            and uc.created_date <![CDATA[<=]]> #{durationStartTime}
        </if>
        <if test="withinType == 3 and durationEndTime != null">
            and uc.created_date <![CDATA[<=]]>  #{durationEndTime}
        </if>
        <if test="flag != null and flag == 0 and dueTime != null">
            and uc.created_date <![CDATA[>=]]> #{dueTime}
        </if>
        <if test="reviseTimeStart != null">
            and uc.revise_time <![CDATA[>=]]>#{reviseTimeStart}
        </if>
        <if test="reviseTimeEnd != null">
            and uc.revise_time <![CDATA[<=]]>#{reviseTimeEnd}
        </if>
        <if test="followUpTimeStart != null">
            and uc.last_follow_time <![CDATA[>=]]>#{followUpTimeStart}
        </if>
        <if test="followUpTimeEnd != null">
            and uc.last_follow_time <![CDATA[<=]]>#{followUpTimeEnd}
        </if>
        <if test="auditTimeStart != null">
            and uc.audit_time <![CDATA[>=]]>#{auditTimeStart}
        </if>
        <if test="auditTimeEnd != null">
            and uc.audit_time <![CDATA[<=]]>#{auditTimeEnd}
        </if>
        <if test="isComeDevise != null and isComeDevise != ''">
            and uc.is_come_devise = #{isComeDevise}
        </if>
        <if test="comeDeviseStatusIds != null and comeDeviseStatusIds.size > 0 ">
            and uc.come_devise_status in
            <foreach collection="comeDeviseStatusIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="decorateTypeIds != null and decorateTypeIds.size > 0 ">
            and uc.decorate_type in
            <foreach collection="decorateTypeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="stroeLevelsOrgIds != null and stroeLevelsOrgIds.size > 0">
            and uc.stroe_id in
            <foreach collection="stroeLevelsOrgIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 是否公海  0否 1是 -->
        <if test="isOpenSea != null">
            <choose>
                <when test="isOpenSea == 1">
                    and uc.audit_status = '2'
                    and (uc.company_id is not null  )
                    and (uc.stroe_id is not null and uc.stroe_id != '')
                    and (uc.charge_user_id is null or uc.charge_user_id = '')
                    and uc.status in ('3','4','5')
                </when>
                <otherwise>
                    and (uc.audit_status != '2'
                    or uc.stroe_id is null or uc.stroe_id = ''
                    or (uc.charge_user_id is not null and uc.charge_user_id != '')
                    or uc.company_id is null
                    or uc.status  in ('1','2','6'))
                </otherwise>
            </choose>
        </if>
        <if test="regularSubscriber != null and regularSubscriber != ''">
            <choose>
                <when test='regularSubscriber != "1" '>
                    and uc.regular_subscriber != '1'
                </when>
                <otherwise>
                    and uc.regular_subscriber = #{regularSubscriber}
                </otherwise>
            </choose>
        </if>
        <if test="isHouserholdsVisit != null and isHouserholdsVisit != ''">
            and uc.is_houserholds_visit = #{isHouserholdsVisit}
        </if>
        <if test="decorateCompanyTypeList !=null and decorateCompanyTypeList.size != 0 ">
            and (
            <foreach collection="decorateCompanyTypeList" item="type" separator=" or ">
                <choose>
                    <when test="type == 1">
                        (uc.decorate_company_category IN (1, 2, 3)
                        AND decorate_company_code is not null and decorate_company_code != '' and decorate_company_code != ' ')
                    </when>
                    <when test="type == 2">
                        (uc.decorate_company_category IN (1,2,3)
                        AND designer_code is not null and designer_code != '' and designer_code != ' ')
                    </when>
                    <otherwise>
                        uc.decorate_company_category = #{type}
                    </otherwise>
                </choose>
            </foreach>
            )

        </if>
        <if test="drainageChannelList != null and drainageChannelList.size != 0">
            and (
            <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                <choose >
                    <when test="item.type == 1 or item.type == 2 or item.type == 4">
                        uc.decorate_company_code in (#{item.code})
                    </when>
                    <otherwise>
                        uc.designer_code in (#{item.code})
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>
        <if test="drainageChannelList != null and drainageChannelList.size != 0">
            and (
            <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                <choose >
                    <when test="item.type == 1 or item.type == 2 or item.type == 4">
                        uc.decorate_company_code in (#{item.code})
                    </when>
                    <otherwise>
                        uc.designer_code in (#{item.code})
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>
        <if test="drainageChannelList != null and drainageChannelList.size != 0">
            and (
            <foreach collection="drainageChannelList" item="item" open="(" close=")" separator="or">

                <choose >
                    <when test="item.type == 1 or item.type == 2 or item.type == 4">
                        uc.decorate_company_code in (#{item.code})
                    </when>
                    <otherwise>
                        uc.designer_code in (#{item.code})
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>
        <if test="followEfficiencyBO != null and followEfficiencyBO.size > 0">
            and (
                <foreach collection="followEfficiencyBO" item="item" index="idex" separator=" or ">
                    <choose>
                        <when test="item.followEfficeStartDate != null and item.followEfficeEndDate != null">
                            (   unix_timestamp(uc.first_follow_time) - unix_timestamp(uc.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate}
                            and  unix_timestamp(uc.first_follow_time) - unix_timestamp(uc.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </when>
                        <when test="item.followEfficeStartDate != null">
                            (  unix_timestamp(uc.first_follow_time) - unix_timestamp(uc.audit_time) <![CDATA[>]]> #{item.followEfficeStartDate})
                        </when>
                        <otherwise>
                            (  unix_timestamp(uc.first_follow_time) - unix_timestamp(uc.audit_time) <![CDATA[<=]]> #{item.followEfficeEndDate})
                        </otherwise>
                    </choose>
                </foreach>
            )
        </if>
        <if test="relationShipFlag != null">
            <choose>
                <when test="relationShipFlag == 1">
                    and uc.id in  (select clues_id from marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                </when>
                <otherwise>
                    and uc.id not in  (select clues_id from marketingcenter.`t_qywx_customer_clues_relation` tcr where tcr.is_deleted = 0 )
                </otherwise>
            </choose>
        </if>
        <if test="participateActivities != null and participateActivities.size != 0">
            and uc.id in  (
                select clues_id
                from marketingcenter.`user_clues_activity_record` ucar
                where ucar.is_deleted = 0
                    and ucar.activity_id in
                    <foreach collection="participateActivities" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
            )
        </if>
        <if test="isSceneInteraction != null">
            and uc.is_scene_interaction = #{isSceneInteraction}
        </if>
        <if test="orderEarnestFlag != null">
            and uc.order_earnest_flag = #{orderEarnestFlag}
        </if>

        <if test="creatCluesMethod != null">
            <choose>
                <when test="creatCluesMethod == 1">
                    and uc.created_by = 'anonymousUser'
                </when>
                <otherwise>
                    and uc.created_by != 'anonymousUser'
                </otherwise>
            </choose>
        </if>

        <!--<if test="customerName != null and customerName != ''">
            and uc.customer_name =
            #{customerName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
        </if>
        <if test="serverCodes != null and serverCodes.size != 0">
            and
            <foreach collection="serverCodes" item="item" separator="or" open="(" close=")">
                CONCAT(',', uc.service_action,',') like CONCAT( '%,',#{item},',%')
            </foreach>
        </if>-->
    </sql>

    <sql id="cluesServiceActionTable">
        (
        select distinct sa.clue_related_id
        from marketingcenter.clues_follow_up sa
        where sa.is_deleted=0
        <if test="serverCodes != null and serverCodes.size != 0">
            and
            <foreach collection="serverCodes" item="sc" separator="or" open="(" close=")">
                concat(',', sa.service_action,',') like concat( '%,',#{sc},',%')
            </foreach>
        </if>
        <if test="serverTimeStart != null">
            and sa.follow_date >= #{serverTimeStart}
        </if>
        <if test="serverTimeEnd != null">
            and sa.follow_date &lt;= #{serverTimeEnd}
        </if>
        <if test="followUpEvaluate!=null">
            and sa.evaluate_status = #{followUpEvaluate}
        </if>
        ) follow
    </sql>


    <select id="newExportOperatorLogByCluesIds"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto">
        select
        opl.id id,
        'middleground' genre,
        'operator_marketing_log' sourceTableName,
        opl.event_description type,
        opl.event_description title,
        opl.description description,
        '' contentText,
        opl.created_date createdDate,
        opl.source_id sourceId,
        '' decorateProgres,
        '' serviceAction,
        opl.created_date followDate,
        '' address,
        opl.created_by chargeUserId,
        opl.`type` appType,
        opl.operator_name chargeUserName,
        '' `describe`,
        null serviceDate,
        '' serviceType,
        uc.stroe_id storeId,
        uc.stroe_name storeName,
        uc.company_id companyId,
        uc.company_name companyName,
        uc.address_id addressId,
        uc.village_id villageId,
        uc.customer_name customerName,
        uc.customer_phone customerPhone,
        uc.charge_user_name salesMan,
        uc.charge_code chargeCode,
        uc.create_user_name createUserName,
        uc.stroe_code stroeCode,
        uc.distributor_name distributorName,
        case uc.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        uc.is_come_devise AS isComeDevise,
        null score,
        uc.come_devise_status comeDeviseStatus,
        null lengthOfStay

        from marketingcenter.user_clues uc
        inner join marketingcenter.operator_marketing_log opl on uc.id = opl.source_id and opl.source_table_name = 'user_clues'

        <where>
            and opl.is_deleted = 0
           and opl.source_table_name = 'user_clues'
           <foreach collection="collection" item="item" separator="," open="and  opl.source_id in(" close=")" index="">
               #{item,jdbcType=BIGINT}
           </foreach>
        </where>
    </select>

    <select id="newFollowUpByCluesIds"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto">
        select
        cfu.id id,
        'appUserClues' genre,
        'clues_follow_up' sourceTableName,
        CASE
        WHEN cfu.`type` = 4 THEN
        '历史日志' ELSE '新增跟进'
        END `type`,
        cfu.title title,
        '' description,
        cfu.content_text contentText,
        cfu.created_date createdDate,
        cfu.clue_related_id sourceId,
        cfu.decorate_progres decorateProgres,
        cfu.service_action serviceAction,
        cfu.follow_date followDate,
        cfu.address address,
        cfu.charge_user_id chargeUserId,
        cfu.`type` appType,
        cfu.charge_user_name chargeUserName,
        '' `describe`,
        null serviceDate,
        '' serviceType,
        uc.stroe_id storeId,
        uc.stroe_name storeName,
        uc.company_id companyId,
        uc.company_name companyName,
        uc.address_id addressId,
        uc.village_id villageId,
        uc.customer_name customerName,
        uc.customer_phone customerPhone,
        uc.charge_user_name salesMan,
        uc.charge_code chargeCode,
        uc.create_user_name createUserName,
        uc.stroe_code stroeCode,
        uc.distributor_name distributorName,
        case uc.follow_up_status
        WHEN '1' THEN
        '未跟进'
        WHEN '2' THEN
        '已跟进'
        WHEN '3' THEN
        '已进店'
        WHEN '4' THEN
        '已成交'
        WHEN '5' THEN
        '已丢单' ELSE ''
        END AS followUpStatus,
        uc.is_come_devise AS isComeDevise,
        cfu.score score,
        uc.come_devise_status comeDeviseStatus,
        length_of_stay lengthOfStay,
        cfu.evaluate_result_desc evaluateResultDesc,
        cfu.evaluate_user_name evaluateUserName,
        cfu.evaluate_time evaluateTime
        from marketingcenter.user_clues uc
        inner join marketingcenter.clues_follow_up cfu on uc.id = cfu.clue_related_id

        <where>
            and cfu.is_deleted = 0
            <foreach open="and cfu.clue_related_id in (" collection="list" item="item" separator="," close=")">
                #{item}
            </foreach>
        </where>

    </select>

</mapper>