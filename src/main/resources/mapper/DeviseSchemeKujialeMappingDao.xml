<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeKujialeMappingDao">

    <resultMap type="com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeKujialeMapping" id="DeviseSchemeKujialeMappingMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="schemeId" column="scheme_id" jdbcType="INTEGER"/>
        <result property="planId" column="plan_id" jdbcType="INTEGER"/>
        <result property="planName" column="plan_name" jdbcType="VARCHAR"/>
        <result property="planJson" column="plan_json" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `is_deleted`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `scheme_id`,
        `plan_id`,
        `plan_name`,
        `plan_json`
    </sql>

    <select id="selectListBySchemeIds" resultMap="DeviseSchemeKujialeMappingMap">
        select
        <include refid="Base_Column_List"/>
        from cmscenter.t_devise_scheme_kujiale_mapping
        where is_deleted = 0
        <if test="schemeIds != null and schemeIds.size > 0">
            and scheme_id in
            <foreach collection="schemeIds" item="schemeId" open="(" separator="," close=")">
                #{schemeId}
            </foreach>
        </if>
    </select>

</mapper>

