package com.fotile.exportcenter.util.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface CompareField {
    String name();
    boolean replace() default false;
    /**
     * 值的替换  导出是{a_id,b_id} 导入反过来,所以只用写一个
     */
    public String[] replaceEnum() default {};

}
