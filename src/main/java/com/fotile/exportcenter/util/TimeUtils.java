package com.fotile.exportcenter.util;

/**
 * 时间处理
 */
public class TimeUtils {
    public final static Integer seconds = 60;
    public final static Integer hours = 24;
    public final static Integer minutes = 60;

    /**
     * 分钟转化为秒
     *
     * @param minutes
     * @return
     */
    public static Integer minutesConversionSeconds(Integer minutes) {
        Integer s = 0;
        s = minutes * seconds;
        return s;
    }

    /**
     * 小时转化为秒
     *
     * @param hours
     * @return
     */
    public static Integer hoursConversionSeconds(Integer hours) {
        Integer s = 0;
        s = hours * minutes * seconds;
        return s;
    }

    /**
     * time转化为秒
     *
     * @param days
     * @return
     */
    public static Integer daysConversionSeconds(Integer days) {
        Integer s = 0;
        s = days * hours * minutes * seconds;
        return s;
    }
}
