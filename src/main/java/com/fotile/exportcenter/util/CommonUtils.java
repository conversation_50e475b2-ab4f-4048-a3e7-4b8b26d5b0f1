package com.fotile.exportcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CommonUtils {
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, (Math.min((i + 1) * len, size)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 格式化手机号
     */
    public static String formatPhone(String phone) {
        String str = "";
        if (org.apache.commons.lang.StringUtils.isNotBlank(phone)) {
            String self = StringUtils.isNotBlank(phone) ? phone : "";
            if (self.length() < 5) {
                str = self;
            } else if (self.length() > 4 && self.length() < 9) {
                str = "****" + self.substring(self.length() - 4);
            } else {
                str = self.substring(0, 3) + "****" + self.substring(self.length() - 4);
            }
        }
        return str;
    }

    /**
     * 格式化客户姓名
     *
     * @param str 客户姓名
     * @return string
     */
    public static String formatCustomerName(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        String regex1 = "\\uD83C[\\uDF00-\\uDFFF]|\\uD83D[\\uDC00-\\uDE4F]";
        String regex2 = "(.{1}).*(.{1})";
        String a = str.replaceAll(regex1, "").replaceAll(regex2, "$1***$2");
        return a.isEmpty() ? "***" : a;
    }
}
