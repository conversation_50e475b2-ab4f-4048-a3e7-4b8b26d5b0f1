package com.fotile.exportcenter.util;


import com.fotile.exportcenter.util.annotation.CompareField;
import com.fotile.exportcenter.util.annotation.FieldChange;
import com.fotile.framework.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description java反射工具类
 * <AUTHOR>
 * @Date 2021/8/30 10:50
 * @Version 1.0
 */
@Slf4j
public class ReflectUtil {


    /**
     * @description 比较两个对象属性值的不同
     * @date 2021/8/30 11:07 
     * @param oldObj
     * @param newObj
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    public static String getFieldDifferentInfo(Object oldObj, Object newObj) throws IllegalAccessException, NoSuchFieldException {
        return StringUtils.join(ReflectUtil.getFieldDifferentInfo2(oldObj,newObj), "\n");
    }

    public static List<String> getFieldDifferentInfo2(Object oldObj, Object newObj) throws IllegalAccessException, NoSuchFieldException {
        // 着异结果
        List<String> diffList = new ArrayList<>();

        Class<?> newClazz = newObj.getClass();
        Class<?> oldClazz = oldObj.getClass();

        Field[] fieldArr = newClazz.getDeclaredFields();
        for (Field newField : fieldArr) {
            newField.setAccessible(true);
            boolean annotationPresent = newField.isAnnotationPresent(CompareField.class);
            if (annotationPresent) {
                //新值
                Object newValue = newField.get(newObj);

                //获取旧值
                Field oldField = oldClazz.getDeclaredField(newField.getName());
                oldField.setAccessible(true);
                Object oldValue = oldField.get(oldObj);
                //比较两值是否相等
                // 获取注解值
                String name = newField.getAnnotation(CompareField.class).name();

                //是否要进行替换
                String[] dicArr = newField.getAnnotation(CompareField.class).replaceEnum();

                if (newField.getGenericType().toString().equals("class java.lang.String")) {
                    //是否要进行替换
                    boolean replace = newField.getAnnotation(CompareField.class).replace();
                    String newValueStr = newValue == null || StringUtils.isEmpty(newValue.toString().trim()) ? null : newValue.toString().trim();
                    String oldValueStr = oldValue == null || StringUtils.isEmpty(oldValue.toString().trim()) ? null : oldValue.toString().trim();
                    if(replace){
                        newValueStr = ReflectUtil.replace(newValueStr);
                        oldValueStr = ReflectUtil.replace(oldValueStr);
                    }
                    if(dicArr != null && dicArr.length > 0){
                        newValueStr = ReflectUtil.dealDicData(dicArr,newValueStr);
                        oldValueStr = ReflectUtil.dealDicData(dicArr,oldValueStr);
                    }
                    if(!StringUtils.equals(newValueStr,oldValueStr)){
                        String description = name + "从" + (StringUtils.isBlank(oldValueStr) ? "空" : oldValueStr) + "变更为" + ((StringUtils.isBlank(newValueStr) ? "空" : newValueStr));
                        diffList.add(description);
                    }
                }else if(newField.getGenericType().toString().equals("class java.math.BigDecimal")){
                    BigDecimal oldValueDec = (BigDecimal)oldValue;
                    BigDecimal newValueDec = (BigDecimal)newValue;
                    if( (oldValueDec != null && (newValue == null || oldValueDec.compareTo(newValueDec) != 0) ) ||
                            (newValueDec != null && ( oldValueDec == null || newValueDec.compareTo(oldValueDec) != 0)) ){
                        String description = name + "从" + (oldValueDec == null ? "空" : oldValueDec.toString()) + "变更为" + (newValueDec == null ? "空" : newValueDec.toString());
                        diffList.add(description);
                    }
                }else if(newField.getGenericType().toString().equals("class java.util.Date")){
                    String oldValueStr = DateUtil.dateFormat((Date)oldValue,"yyyy-MM-dd HH:mm:ss");
                    String newValueStr = DateUtil.dateFormat((Date)newValue,"yyyy-MM-dd HH:mm:ss");
                    if(!StringUtils.equals(oldValueStr,newValueStr)){
                        String description = name + "从" + (StringUtils.isBlank(oldValueStr) ? "空" : oldValueStr) + "变更为" + ((StringUtils.isBlank(newValueStr) ? "空" : newValueStr));
                        diffList.add(description);
                    }
                }else if(newField.getGenericType().toString().equals("class java.time.LocalDateTime")){
                    DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String oldValueStr = oldValue == null ? "" : ((LocalDateTime)oldValue).format(pattern);
                    String newValueStr = newValue == null ? "" : ((LocalDateTime)newValue).format(pattern);
                    if(!StringUtils.equals(oldValueStr,newValueStr)){
                        String description = name + "从" + (StringUtils.isBlank(oldValueStr) ? "空" : oldValueStr) + "变更为" + ((StringUtils.isBlank(newValueStr) ? "空" : newValueStr));
                        diffList.add(description);
                    }
                }else{
                    String newValueStr = newValue == null ? null : newValue.toString().trim();
                    String oldValueStr = oldValue == null ? null : oldValue.toString().trim();
                    if(dicArr != null && dicArr.length > 0){
                        newValueStr = ReflectUtil.dealDicData(dicArr,newValueStr);
                        oldValueStr = ReflectUtil.dealDicData(dicArr,oldValueStr);
                    }
                    if (!StringUtils.equals(newValueStr, oldValueStr)) {
                        String description = name + "从" + (StringUtils.isBlank(oldValueStr) ? "空" : oldValueStr) + "变更为" + (StringUtils.isBlank(newValueStr) ? "空" :newValueStr);
                        diffList.add(description);
                    }
                }
            }
        }
        return diffList;
    }

    public static String dealDicData(String[] dicArr,String key){
        String value = "空";
        if(StringUtils.isBlank(key)){
            return value;
        }
        if(dicArr == null || dicArr.length == 0){
            return key;
        }
        for (String s : dicArr) {
            String[] splitArr = s.split("_");
            if(splitArr != null && splitArr.length == 2){
                if(StringUtils.equalsIgnoreCase(splitArr[0],key)){
                    value = splitArr[1];
                    break;
                }
            }
        }
        return value;
    }


    public static boolean checkFieldChange(Object oldObj, Object newObj) throws IllegalAccessException, NoSuchFieldException {

        boolean result = false;

        Class<?> newClazz = newObj.getClass();
        Class<?> oldClazz = oldObj.getClass();

        Field[] fieldArr = newClazz.getDeclaredFields();
        for (Field newField : fieldArr) {
            newField.setAccessible(true);
            boolean annotationPresent = newField.isAnnotationPresent(FieldChange.class);
            if (annotationPresent) {
                //新值
                Object newValue = newField.get(newObj);

                //获取旧值
                Field oldField = oldClazz.getDeclaredField(newField.getName());
                oldField.setAccessible(true);
                Object oldValue = oldField.get(oldObj);
                //比较两值是否相等
                // 获取注解值
                if (newField.getGenericType().toString().equals("class java.lang.String")) {
                    //是否要进行替换
                    String newValueStr = newValue == null || StringUtils.isEmpty(newValue.toString().trim()) ? null : newValue.toString().trim();
                    String oldValueStr = oldValue == null || StringUtils.isEmpty(oldValue.toString().trim()) ? null : oldValue.toString().trim();
                    if(!StringUtils.equals(newValueStr,oldValueStr)){
                        result = true;
                    }
                }else if(newField.getGenericType().toString().equals("class java.math.BigDecimal")){
                    BigDecimal oldValueDec = (BigDecimal)oldValue;
                    BigDecimal newValueDec = (BigDecimal)newValue;
                    if( (oldValueDec != null && (newValue == null || oldValueDec.compareTo(newValueDec) != 0) ) ||
                            (newValueDec != null && ( oldValueDec == null || newValueDec.compareTo(oldValueDec) != 0)) ){
                        result = true;
                    }
                }else if(newField.getGenericType().toString().equals("class java.util.Date")){
                    String oldValueStr = DateUtil.dateFormat((Date)oldValue,"yyyy-MM-dd HH:mm:ss");
                    String newValueStr = DateUtil.dateFormat((Date)oldValue,"yyyy-MM-dd HH:mm:ss");
                    if(!StringUtils.equals(oldValueStr,newValueStr)){
                        result = true;
                    }
                }else{
                    if (!Objects.equals(oldValue, newValue)) {
                        result = true;
                    }
                }
            }
        }
        return result;
    }


    public static String replace(String str){
        if(StringUtils.isNotBlank(str)){
            int length = str.length();
            if(length <= 3){
            }else if(length >= 7){
                str = str.substring(0, 3) + "****" + str.substring(7, length);
            }else {
                str = str.substring(0, 3);
                for(int i = 0; i< length -3 ; i++){
                    str += "*";
                }
            }
        }
        return str;
    }

    public static void main(String[] args) throws Exception {
        String tel = "187123457890";

        log.info(ReflectUtil.replace(tel));
    }


}
