package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

@FeignClient(value = "workflow-center")
public interface WorkflowClient {
    /**
     * 新增闭店流程
     */

    /**
     * 查询字典表数据
     * */
    @GetMapping(value = {"/api/dict/api/open/typeCode"}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<Dict>> typeCode(@RequestParam("typeCode") String typeCode);


    @RequestMapping(value = { "/api/dict/api/open/subChannelCode/list"}, method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<Dict>> getSubChannelCodeList(@RequestParam("channelCode") String channelCode);
}
