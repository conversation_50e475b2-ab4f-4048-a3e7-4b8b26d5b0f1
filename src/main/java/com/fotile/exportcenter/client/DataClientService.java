package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(value = "data-center", path = "/api/exportTask")
public interface DataClientService {

    @RequestMapping(value = "/insertTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> insertTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = {"/api/open/getTaskById"}, method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> getTaskById(@RequestParam("id") Long id);

    @RequestMapping(value = {"/api/open/startTask"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> startTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/updateTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> updateTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/successTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> successTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/failureTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> failureTask(@RequestBody ExportTaskRecord exportTaskRecord);
}
