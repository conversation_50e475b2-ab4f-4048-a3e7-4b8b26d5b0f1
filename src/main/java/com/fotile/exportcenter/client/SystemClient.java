package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.QueryByParamsOutDto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ Author     ：黄学后
 * @ Date       ：Created in 14:44 2020/4/10
 * @ Description：
 * @ Modified By：
 * @Version: $
 */
@FeignClient(value = "system-center")
public interface SystemClient {
    @GetMapping("/api/open/dicExpand/queryByType")
    public Result<PageInfo<QueryByParamsOutDto>> queryByType(@RequestParam("typeCode") String typeCode,
                                                             @RequestParam("pageNum") int pageNum,
                                                             @RequestParam("pageSize") int pageSize);

    @GetMapping("/api/dic/api/open/queryByTypeCodes")
    public Result<List<Dict>> queryByTypeCodes(@RequestParam("typeCodes") List<String> typeCodes);

}
