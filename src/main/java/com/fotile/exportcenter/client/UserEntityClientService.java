package com.fotile.exportcenter.client;

import com.fotile.exportcenter.client.pojo.UserEntityExtend;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "user-center", path = "/api/user")
public interface UserEntityClientService {
    //根据账号id查询账号信息多个
    @PostMapping(value = { "/api/open/findUserEntityExtendByUserIdsPost"}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserEntityExtend>> findUserEntityExtendByUserIdsPost(@RequestBody List<String> userIds);

}
