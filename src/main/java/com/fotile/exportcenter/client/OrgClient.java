package com.fotile.exportcenter.client;


import com.fotile.exportcenter.client.pojo.SalesmanInfoForQYWX;
import com.fotile.exportcenter.client.pojo.org.FindSalesmanIdByLabelValueInDto;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "org-center")
public interface OrgClient {

    @RequestMapping(value = "/api/open/findCompanyByOrgIdList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindCompanyAreaByOrgIdsOutDto>> findCompanyByOrgIdList(@RequestBody FindCompanyByIdsInDto findCompanyByIdsInDto);

    @RequestMapping(value = "/api/open/findByChannelCodes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ChannelEntity>> findByChannelCodes(@RequestBody List<String> codeList);

    @RequestMapping(value = "/api/open/findByRadioCodes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindRadioAndChannelByCodesOutDto>> findByRadioCodes2(@RequestBody List<String> codeList);

    @RequestMapping(value = "/api/diffIndustry/api/open/findAllByIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DiffIndustryEntity>> findAllByIds(@RequestBody List<Long> ids);

    @RequestMapping(value = "/api/designer/api/open/getDecorateInfoForExportByCodeList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DecorateAndDesignerDto>> getDecorateInfoForExportByCodeList(@RequestBody List<DecorateAndDesignerDto> paramList);

    @ApiOperation("根据id集合，取得业务员分公司，门店信息")
    @RequestMapping(value = "/api/salesman/api/open/getSalesmanInfoByIdsForQYWX", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanInfoForQYWX>> getSalesmanInfoByIdsForQYWX(@RequestBody List<Long> ids);

    /**
     * 根据能力标签查询业务员id
     */
    @RequestMapping(value = "/api/salesman/api/open/findSalesmanIdByLabelValue", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<Long>> findSalesmanIdByLabelValue(@Valid @RequestBody FindSalesmanIdByLabelValueInDto dto);
    /**
     * 根据userid查询业务员
     */
    @RequestMapping(value = "/api/open/findByUserIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanInfoForQYWX>> findByUserIdsOpen(@RequestBody List<String> userEntityIds);


}
