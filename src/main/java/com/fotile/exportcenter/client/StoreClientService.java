package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.dto.FindStoreByOrgIdOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindStoreByOrgIdsInDto;
import com.fotile.framework.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(value = "org-center" ,path="/api")
public interface StoreClientService {


    //根据门店orgIds查询门店
    @PostMapping(value = "/store/api/open/findByOrgIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByOrgIdOutDto>> findByOrgIds(@RequestBody FindStoreByOrgIdsInDto storeByOrgIdsInDto);



}
