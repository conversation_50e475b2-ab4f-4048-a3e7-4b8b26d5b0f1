package com.fotile.exportcenter.client;

import com.fotile.exportcenter.client.pojo.AddWorkLogInDTO;
import com.fotile.exportcenter.client.pojo.FindUserEntityForOpenBySalesmanIdsPostDTO;
import com.fotile.exportcenter.client.pojo.UserEntityExtend;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
* <AUTHOR>
* @description  
* @version 创建时间：2019年7月24日 下午3:53:06
*/
@FeignClient(value = "user-center" ,path="/api")
public interface UserEntityClient {
	//根据用户id查询用户昵称
    @GetMapping("/open/user/findUserEntityExtendByUserId")
    Result<UserEntityExtend> findUserEntityExtendByUserId(@RequestParam("userId")String userId);


    @RequestMapping(value = "/user/api/open/findUserEntityForOpenBySalesmanIds", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<UserEntityExtend>> apiOpenFindUserEntityBySalesmanIds(@RequestParam("salesmanIds")String salesmanIds);

    @RequestMapping(value = "/user/api/open/findUserEntityForOpenBySalesmanIdsPost", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<UserEntityExtend>> findUserEntityForOpenBySalesmanIdsPost(FindUserEntityForOpenBySalesmanIdsPostDTO findUserEntityForOpenBySalesmanIdsPostDTO);


    @PostMapping(value = { "/user/api/open/findUserEntityExtendByUserIdsPost"}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserEntityExtend>> findUserEntityExtendByUserIdsPost(@RequestBody List<String> userIds);


    @ApiOperation("根据账号id查询用户数据权限")
    @RequestMapping(value = "/user/api/open/findUserEntityById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<UserAuthor> findUserEntityById2(@SpringQueryMap UserAuthor userAuthor);

    @RequestMapping(value = "/work/log/api/open/async-batch-insert", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<?> asyncBatchInsertLogs(@Valid @RequestBody List<AddWorkLogInDTO> logs);

}
