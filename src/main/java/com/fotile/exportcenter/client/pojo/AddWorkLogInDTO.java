package com.fotile.exportcenter.client.pojo;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class AddWorkLogInDTO implements Serializable {
    /**
     * 工作日志类型(详见工作日志类型枚举)
     *
     * @link https://tools.showdoc.efotile.com/web/#/5?page_id=2667
     */
    @NotNull(message = "日志类型不能为空！")
    private Integer logType;
    /**
     * 记录日志操作时间(yyyy-MM-dd hh:mm:ss)
     */
    @NotNull(message = "工作日志执行时间不能为空！")
    //private Date logTime;
    private String logTime;

    /**
     * 记录日志操作内容
     */
    private String logContent;
    /**
     * 日志附件URL(多个使用半角逗号分隔)
     */
    private String logAttachmentUrl;
    /**
     * 关联记录id
     */
    private Long targetId;
    /**
     * 关联记录编码
     */
    private String targetCode;
    /**
     * 关联记录名称
     */
    private String targetName;
    /**
     * 关联记录标题(e.g.:异业门店名称...)
     */
    private String targetTitle;
    /**
     * 关联记录金额(e.g.:订单金额)
     */
    private BigDecimal targetAmount;
    /**
     * 执行人id(业务员，用于填充业务员基础信息)
     */
    @NotNull(message = "执行人ID不能为空！")
    private Long executorId;
    /**
     * 操作人Id(如果传参为null/空，则取当前操作人id)
     */
    private String operatorId;
    /**
     * 操作人名称(为null或空时，基于operatorId值去填充)
     */
    private String operatorName;
    /**
     * 关联记录数据归属门店orgId
     */
    private Long targetStoreId;

    public AddWorkLogInDTO(Integer logType, Date logTime, Long executorId) {
        this.logType = logType;
        if (logTime != null) {
            final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            this.logTime = dateFormat.format(logTime);
        }
        this.executorId = executorId;
    }
}
