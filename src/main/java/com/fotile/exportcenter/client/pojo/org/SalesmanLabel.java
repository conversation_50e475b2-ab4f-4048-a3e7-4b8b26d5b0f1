package com.fotile.exportcenter.client.pojo.org;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class SalesmanLabel implements Serializable {
    private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "")
	private Integer id;
	
	@ApiModelProperty(value = "是否删除；0：否；其他：是")
	private Integer isDeleted;
	
	@ApiModelProperty(value = "创建者")
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "创建日期")
	private Date createdDate;
	
	@ApiModelProperty(value = "修改者")
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "修改日期")
	private Date modifiedDate;
	
	@ApiModelProperty(value = "业务员id")
	private Integer salesmanId;
	
	@ApiModelProperty(value = "标签类型，:目前只有能力标签，值为1")
	private Integer type;
	
	/**
	 * 标签值
	 */
	private String labelValue;
	
	/**
	 * 标签名
	 */
	private String labelName;

}