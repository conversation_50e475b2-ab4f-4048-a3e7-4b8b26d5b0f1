package com.fotile.exportcenter.client.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("keycloak用户表扩展表")
public class UserEntityExtend implements Serializable {
	
	@ApiModelProperty("主键")
    private Long id;
	
    @ApiModelProperty("keycloak用户ID")
    @NotNull(message="账号id不能为空!")
    private String userEntityId;
    
    @ApiModelProperty("昵称")
    @FieldEncrypt
    private String nickName;

    private String userName;

    private String firstName;
    
    @ApiModelProperty("手机号")
    private String phone;
    
    @ApiModelProperty("关联业务员")
    private Long salesmanId;
    
    @ApiModelProperty("关联业务员名称")
    private String salesmanName;
    
    @ApiModelProperty("关联业务员编码")
    private String salesmanCode;
    
    @ApiModelProperty("关联业务员手机号")
    private String salesmanPhone;
    
    @ApiModelProperty("最后一次登录时间")
    private String lastLoginDate;
    
    @ApiModelProperty("头像url")
    private String headPortrait;
    
    @ApiModelProperty("手机唯一标识")
    private String registrationId;
    
    @ApiModelProperty("出生日期")
    private Date birthday;
    
    @ApiModelProperty("qq")
    private String customerQQ;
    
    @ApiModelProperty("性别")
    private Integer sex;
    
    @ApiModelProperty("微信")
    private String wechat;
    
    @ApiModelProperty("注册时间")
    private Date registerTime;
    
    @ApiModelProperty("注册ip")
    private String registerIp;
    
    @ApiModelProperty("设备类型")
    private String mobileSystemType;

    @ApiModelProperty("是否启用")
    private String stage;

    @ApiModelProperty("邮箱")
    private String email;
    
    @ApiModelProperty("常用工具，多个值以逗号分隔")
    private String commonTools;
    
    @ApiModelProperty("首页页面布局")
    private String pageLayout;
    
    @ApiModelProperty("域")
    private String realmId;

    /**
     * 关联员工
     */
    private String employeeId;

    /**
     * 关联员工名称
     */
    private String employeeName;

    /**
     * 关联员工手机号
     */
    private String employeePhone;

    /**
     * 是否只走公司数据权限 0：否；1：是
     */
    private Integer useCompanyAuthor;

}
