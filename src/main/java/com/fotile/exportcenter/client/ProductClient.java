package com.fotile.exportcenter.client;

import com.fotile.exportcenter.client.pojo.product.GoodsCategoryTreeDto;
import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.QueryByParamsOutDto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 */
@FeignClient(value = "product-center", path = "/api")
public interface ProductClient {
    @ApiOperation("商品分类树一级")
    @RequestMapping(value = {"/goodsCategory/api/open/findGoodsCategoryAll"}, method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GoodsCategoryTreeDto>> findGoodsCategoryAll();
}
