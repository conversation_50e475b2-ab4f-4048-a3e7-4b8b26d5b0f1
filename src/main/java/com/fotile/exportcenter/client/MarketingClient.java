package com.fotile.exportcenter.client;


import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.BaseLabelDTO;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName ChannelClient
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/12/25
 * @Version
 **/
@FeignClient(value = "marketing-center")
public interface MarketingClient {


    /**
     * 查询基础标签
     */
    @RequestMapping(value = "/api/baseAttribute/api/open/queryBaseLabel", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<BaseLabelDTO>> queryBaseLabel(@RequestParam("pageType") String pageType , @RequestParam("companyOrgId") Long companyOrgId);
}
