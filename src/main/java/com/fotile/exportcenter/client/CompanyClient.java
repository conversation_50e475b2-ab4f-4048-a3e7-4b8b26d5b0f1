package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByAreaIdsDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByIdOutDto;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@Component
@FeignClient(value = "org-center" ,path="/api/company")
public interface CompanyClient {


    @RequestMapping(value = "/findCompanyByAreaIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindCompanyByIdOutDto>> findCompanyByAreaIds(@RequestBody FindCompanyByAreaIdsDto areaIdsDto);


}
