package com.fotile.exportcenter.client;

import com.fotile.exportcenter.marketing.pojo.dto.FindVillageByIdOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindVillageByVillageIdsInDto;
import com.fotile.framework.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(value = "org-center" ,path="/api/village")

public interface VillageClientService {
    @RequestMapping(value="/findById",method=RequestMethod.GET,consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindVillageByIdOutDto> findById(@RequestParam(value = "id")  Long id);

    @RequestMapping (value="/findVillageByVillageIds",method=RequestMethod.POST,consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindVillageByIdOutDto>> findVillageByVillageIds(@RequestBody FindVillageByVillageIdsInDto findVillageByVillageIdsInDto);

    @RequestMapping (value="/api/open/findVillageByVillageIds",method=RequestMethod.POST,consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindVillageByIdOutDto>> findVillageByVillageIds2(@RequestBody FindVillageByVillageIdsInDto findVillageByVillageIdsInDto);
}
