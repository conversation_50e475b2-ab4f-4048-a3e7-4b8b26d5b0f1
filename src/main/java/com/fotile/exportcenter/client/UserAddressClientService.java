package com.fotile.exportcenter.client;

import com.fotile.exportcenter.client.pojo.UserAddressSimpleInDTO;
import com.fotile.exportcenter.client.pojo.UserAddressSimpleOutDTO;
import com.fotile.exportcenter.marketing.pojo.dto.UserAddressOutDto;
import com.fotile.framework.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(value = "customer-center" ,path="/api/userAddress")
public interface UserAddressClientService {
    @PostMapping(value = "/selectUserAddressByIdList",consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserAddressOutDto>> selectUserAddressByIdList(@RequestBody List<Long> ids);

    @PostMapping(value = "/api/open/selectUserAddressByIdList",consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserAddressOutDto>> selectUserAddressByIdList2(@RequestBody List<Long> ids);

    @PostMapping(value = "/api/open/selectSimpleUserAddressByIdList",consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserAddressSimpleOutDTO>> selectSimpleUserAddressByIdList(@RequestBody UserAddressSimpleInDTO dto);

}
