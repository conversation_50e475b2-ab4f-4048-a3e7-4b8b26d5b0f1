package com.fotile.exportcenter.cmscenter.material.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum MaterialTypeEnum {

    CONTENT_LIST(1,"内容列表"),
    PRODUCT_LIST(2,"商品列表"),
    VIDEO(3,"视频"),
    FILE(4,"文件"),
    TEXT(5,"文字"),
    PICTURE(6,"图片"),
    LINK(7,"外链"),

    COMBINATION(8,"组合型"),
    ;

    private Integer code;

    private String desc;

    MaterialTypeEnum(Integer code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getByCode(Integer code){
        MaterialTypeEnum[] enums = MaterialTypeEnum.values();
        for (MaterialTypeEnum e : enums) {
            if (Objects.equals(e.getCode(),code)) {
                return e.getDesc();
            }
        }
        return null;
    }
}
