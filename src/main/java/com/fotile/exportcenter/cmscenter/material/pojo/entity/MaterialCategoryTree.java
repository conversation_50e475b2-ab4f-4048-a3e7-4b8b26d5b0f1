package com.fotile.exportcenter.cmscenter.material.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.*;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

/**
 * <AUTHOR>
 * 树
 * @date 2023/8/30 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("t_material_category_tree")
public class MaterialCategoryTree extends AuditingEntity {
    /**
     * 树名称
     */
    private String title;

    /**
     * 生效企微：qywx_qg_1；qywx_sh_1
     */
    private String effectiveWechat;

    /**
     * 上下架状态，1上架，0下架，默认下架
     */
    private Integer status;

    /**
     * 是否全国树，1代表全国
     */
    private Integer isGlobal;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 最新编辑人
     */
    @FieldEncrypt
    private String modifiedUsername;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifiedDate;
    /**
     * 复制状态 1复制中 2复制失败 3复制成功
     */
    private Integer copyStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 复制失败原因
     */
    private String errorLog;
}
