package com.fotile.exportcenter.cmscenter.material.pojo;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
@Data
public class MaterialDto {

    private Integer start;
    /**
     * 内容id
     */
    private Long materialId;
    /**
     * 分类节点id
     */
    @ApiModelProperty("分类节点id")
    private Integer categoryNodeId;
    /**
     * 树id
     */
    private Integer treeId;

    /**
     * 1级节点
     */
    private Long node1Id;

    /**
     * 2级节点
     */
    private Long node2Id;

    /**
     * 3级节点
     */
    private Long node3Id;

    /**
     * 4级节点
     */
    private Long node4Id;

    /**
     * t_material表的type
     */
    private List<Integer> contentTypeList;
    /**
     * 搜索内容用
     */
    private String keyword;
    /**
     * 搜索类型,1:内容、2：白景图-产品、3：白景图：案例
     */
    private Integer logType=1;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private Integer type;
    /**
     * 状态
     */
    @ApiModelProperty("上下架状态，1-上架，0-下架")
    private Integer status;

    /**
     * 页数。公用
     */
    @NotNull
    private Integer page;
    /**
     * 条数。公用
     */
    @NotNull
    private Integer size;
    /**
     * 查询内容类型 0：发送 1：收藏 2：好评 3:默认排序
     */
    @ApiModelProperty("查询内容类型 0：发送 1：收藏 2：好评")
    private Integer queryContentType;
    /**
     * DESC 降序 ASC 升序
     */
    @ApiModelProperty("排序条件")
    private String conditionsType;
    /**
     * 1：序号 2：最新编辑时间 3：创建时间 4:发送
     */
    @ApiModelProperty("排序类型")
    private Integer sortType;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;
    /**
     * 是否切换节点
     */
    @ApiModelProperty("是否切换节点")
    private boolean isToggleNode;
    /**
     * 内容组idList
     */
    @ApiModelProperty("内容组idList")
    private List<Integer> materialIdList = Lists.newArrayList();
    /**
     * 基础素材id
     */
    private Integer basicMaterialContentId;

    /**
     * 是否推荐
     */
    private Integer recommendFlag;
    /**
     * 产品型号关键词
     */
    private String productModeWord;
    /**
     * 内容标签列表
     */
    private List<Long> contentTagList;
    /**
     * 空值查询
     */
    private Integer isNull;

    private List<Integer> notMaterialIdList;
    /**
     * 品类
     */
    private List<Integer> productList;
    /**
     * 型号
     */
    private List<String> modelList;

    /**
     * 导出文件名
     */
    private String fileName;

    private List<Integer> nodeIdList;
}
