package com.fotile.exportcenter.cmscenter.material.dao;


import com.fotile.exportcenter.cmscenter.material.pojo.*;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MaterialMapper {

    @DruidTxcMultiDataSourceAnnontation("second")
    List<MaterialExportDto> getExportMaterial(@Param("material") MaterialDto exportDto,
                                                         @Param("materialIdList") List<Integer> materialIdList);
    @DruidTxcMultiDataSourceAnnontation("second")
    String materialTreeName(Integer treeId);
    @DruidTxcMultiDataSourceAnnontation("second")
    List<MaterialNodeDto> materialNodeName(List<Integer> NodeIdList);
    @DruidTxcMultiDataSourceAnnontation("second")
    List<BasicMaterialDto> findBasicMaterial(List<Integer> materialIdList);
    @DruidTxcMultiDataSourceAnnontation("second")
    List<BasicMaterialProductDto> findBasicMaterialProduct(List<Integer> materialIdList);
    @DruidTxcMultiDataSourceAnnontation("second")
    List<BasicMaterialTagDto> findBasicMaterialTag(List<Integer> materialIdList);
}