package com.fotile.exportcenter.cmscenter.material.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Data 交互助手内容列表
 */
@ApiModel("内容列表")
@Data
public class MaterialExportDto implements Serializable {

    private Integer treeId;

    private String treeName;
    /**
     * 聊天素材id
     */
    @ApiModelProperty(value = "聊天素材id")
    private Integer materialId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;

    private String typeDesc;
    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    private Integer nodeId;
    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;
    /**
     * 相关产品型号
     */
    private String productName;
    /**
     * 相关产品型号
     */
    private String modelName;
    /**
     * 内容标签
     */
    private String materialTag;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusDesc;
    /**
     * 发送数
     */
    @ApiModelProperty(value = "发送数")
    private Integer shareCount;
    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Integer favoriteCount;
    /**
     * 有用数
     */
    @ApiModelProperty(value = "有用数")
    private Integer helpfulCount;
    /**
     * 无用数
     */
    @ApiModelProperty(value = "无用数")
    private Integer unhelpfulCount;
    @ApiModelProperty(value = "修改时间")
    private Date modifiedDate;
    private String modifiedDateDesc;
    @ApiModelProperty(value = "修改人")
    @FieldEncrypt
    private String modifiedUsername;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    private String createdDateDesc;
    private Integer basicMaterialIdOne;
    private String sourceDescNamesOne;
    private String contentTypeDescOne;
    private String materialUrlOne;
    private String contentTextOne;
    private Integer basicMaterialIdSecond;
    private String sourceDescNamesSecond;
    private String contentTypeDescSecond;
    private String materialUrlSecond;
    private String contentTextSecond;
    private Integer basicMaterialIdThird;
    private String sourceDescNamesThird;
    private String contentTypeDescThird;
    private String materialUrlThird;
    private String contentTextThird;
    private Integer basicMaterialIdFourth;
    private String sourceDescNamesFourth;
    private String contentTypeDescFourth;
    private String materialUrlFourth;
    private String contentTextFourth;
    private Integer basicMaterialIdFifth;
    private String sourceDescNamesFifth;
    private String contentTypeDescFifth;
    private String materialUrlFifth;
    private String contentTextFifth;
    private Integer basicMaterialIdSix;
    private String sourceDescNamesSix;
    private String contentTypeDescSix;
    private String materialUrlSix;
    private String contentTextSix;
    private Integer basicMaterialIdSeven;
    private String sourceDescNamesSeven;
    private String contentTypeDescSeven;
    private String materialUrlSeven;
    private String contentTextSeven;
    private Integer basicMaterialIdEighth;
    private String sourceDescNamesEighth;
    private String contentTypeDescEighth;
    private String materialUrlEighth;
    private String contentTextEighth;
    private Integer basicMaterialIdNine;
    private String sourceDescNamesNine;
    private String contentTypeDescNine;
    private String materialUrlNine;
    private String contentTextNine;

}
