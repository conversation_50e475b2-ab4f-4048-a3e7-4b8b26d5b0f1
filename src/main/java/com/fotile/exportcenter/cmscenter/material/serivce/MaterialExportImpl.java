package com.fotile.exportcenter.cmscenter.material.serivce;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.client.ProductClient;
import com.fotile.exportcenter.client.SystemClient;
import com.fotile.exportcenter.client.pojo.product.GoodsCategoryTreeDto;
import com.fotile.exportcenter.cmscenter.material.dao.MaterialMapper;
import com.fotile.exportcenter.cmscenter.material.enums.MaterialTypeEnum;
import com.fotile.exportcenter.cmscenter.material.pojo.*;
import com.fotile.exportcenter.cmscenter.scheme.mapper.ExportImpl;
import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.DateUtil;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 内容分类树导出
 */
@Component("1004")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class MaterialExportImpl implements ExportImpl {
    @Autowired
    private MaterialMapper materialMapper;
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private ProductClient productClient;

    private static final List<String> FIELD_MAPPING = List.of("One", "Second", "Third", "Fourth", "Fifth", "Six", "Seven", "Eighth", "Nine");
    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        MaterialDto exportDto = JsonUtils.parse(exportTaskRecord.getParamJson(), MaterialDto.class);
        //设置密钥
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count = 5000;
        Integer start1 = exportDto.getStart();

        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;
        List<MaterialExportDto> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count;
            size = count;
            if (j == i) {
                size = lastCount;
            }
            exportDto.setPage(start);
            exportDto.setSize(size);
            List<MaterialExportDto> materialExportDtos = materialMapper.getExportMaterial(exportDto,
                    exportDto.getMaterialIdList());
            resultList.addAll(materialExportDtos);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(exportDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            exportTask.setExt1(String.valueOf(resultList.size()));
            dataClientService.updateTask(exportTask);
        }
        if (resultList.size() <= 0) {
            throw new BusinessException("未能查询到结果！");
        }
        String sheetName = "分类树节点表";
        String fileName = exportDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            //查询树信息
            String treeName = materialMapper.materialTreeName(exportDto.getTreeId());
            //查询节点
            List<Integer> nodeIdList = resultList.stream().map(MaterialExportDto::getNodeId).distinct().collect(Collectors.toList());
            List<MaterialNodeDto> nodeList = materialMapper.materialNodeName(nodeIdList);
            Map<Integer, String> nodeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(nodeList)){
                nodeMap = nodeList.stream().collect(Collectors.toMap(MaterialNodeDto::getId, MaterialNodeDto::getNodeName));
            }
            List<Integer> materialIdList = resultList.stream().map(MaterialExportDto::getMaterialId).collect(Collectors.toList());
            //查询基础素材相关信息
            List<BasicMaterialDto> basicMaterialList = materialMapper.findBasicMaterial(materialIdList);
            Map<Integer, List<BasicMaterialDto>> basicMaterialMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(basicMaterialList)){
                basicMaterialMap = basicMaterialList.stream().collect(Collectors.groupingBy(BasicMaterialDto::getMaterialId));
            }
            //查询相关产品，型号
            List<BasicMaterialProductDto> basicMaterialProductList = materialMapper.findBasicMaterialProduct(materialIdList);
            Map<Integer, List<String>> productName = Maps.newHashMap();
            Map<Integer, List<String>> modelMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(basicMaterialProductList)){
                List<Long> productIdList = basicMaterialProductList.stream()
                        .filter(p-> Objects.equals(p.getType(),1))
                        .map(BasicMaterialProductDto::getRefId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(productIdList)){
                    Result<List<GoodsCategoryTreeDto>> goodsCategoryAllResult = productClient.findGoodsCategoryAll();
                    if (goodsCategoryAllResult.getSuccess()&& CollectionUtils.isNotEmpty(goodsCategoryAllResult.getData())){
                        List<String> goodsCategoryList = goodsCategoryAllResult.getData().stream()
                                .map(GoodsCategoryTreeDto::getCategoryId).collect(Collectors.toList());
                        //过滤品类
                        productName = basicMaterialProductList.stream()
                                .filter(p -> Objects.equals(p.getType(), 1)&&goodsCategoryList.contains(String.valueOf(p.getRefId())))
                                .collect(Collectors.groupingBy(BasicMaterialProductDto::getMaterialId,
                                        Collectors.mapping(BasicMaterialProductDto::getRefName, Collectors.toList())));
                    }else {
                        productName = basicMaterialProductList.stream()
                                .filter(p -> Objects.equals(p.getType(), 1))
                                .collect(Collectors.groupingBy(BasicMaterialProductDto::getMaterialId, Collectors.mapping(BasicMaterialProductDto::getRefName, Collectors.toList())));
                    }
                }
                //型号
                modelMap = basicMaterialProductList.stream()
                        .filter(p -> Objects.equals(p.getType(), 2))
                        .collect(Collectors.groupingBy(BasicMaterialProductDto::getMaterialId,
                                Collectors.mapping(BasicMaterialProductDto::getRefName, Collectors.toList())));
            }
            //查询相关标签
            List<BasicMaterialTagDto> basicMaterialTagList = materialMapper.findBasicMaterialTag(materialIdList);
            Map<Integer, List<String>> basicMaterialTagMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(basicMaterialTagList)){
                Result<List<Dict>> dicResult = systemClient.queryByTypeCodes(List.of("basic_material_tag"));
                if (dicResult.getSuccess()&&CollectionUtils.isNotEmpty(dicResult.getData())){
                    Map<String,String> data = dicResult.getData().stream()
                            .filter(dic->dic.getActive().equals(1)).collect(Collectors.toMap(Dict::getValueCode, Dict::getValueName));
                    basicMaterialTagMap = basicMaterialTagList.stream()
                            .filter(tag -> data.containsKey(String.valueOf(tag.getTagId())))
                            .collect(Collectors.groupingBy(BasicMaterialTagDto::getMaterialId,
                                    Collectors.mapping(tag->data.get(String.valueOf(tag.getTagId())),Collectors.toList())));
                }
            }

            for (MaterialExportDto dto: resultList){
                if (!nodeMap.isEmpty()&&nodeMap.containsKey(dto.getNodeId())){
                    dto.setNodeName(nodeMap.get(dto.getNodeId()));
                }
                if (StringUtils.isNotBlank(dto.getModifiedUsername())){
                    dto.setModifiedUsername(MybatisMateConfig.decrypt(dto.getModifiedUsername()));
                }
                dto.setModifiedDateDesc(DateUtil.dateFormat(dto.getModifiedDate(),"yyyy-MM-dd HH:mm:ss"));
                dto.setCreatedDateDesc(DateUtil.dateFormat(dto.getCreatedDate(),"yyyy-MM-dd HH:mm:ss"));
                dto.setTypeDesc(MaterialTypeEnum.getByCode(dto.getType()));
                dto.setTreeId(exportDto.getTreeId());
                dto.setTreeName(treeName);
                dto.setStatusDesc(ObjectUtil.equals(dto.getStatus(),1)?"上架":"下架");
                if (!productName.isEmpty()&&productName.containsKey(dto.getMaterialId())){
                    dto.setProductName(productName.get(dto.getMaterialId()).stream().distinct().collect(Collectors.joining(",")));
                }
                if (!modelMap.isEmpty()&&modelMap.containsKey(dto.getMaterialId())){
                    dto.setModelName(modelMap.get(dto.getMaterialId()).stream().distinct().collect(Collectors.joining(",")));
                }
                if (!basicMaterialTagMap.isEmpty()&&basicMaterialTagMap.containsKey(dto.getMaterialId())){
                    dto.setMaterialTag(basicMaterialTagMap.get(dto.getMaterialId()).stream().distinct().collect(Collectors.joining("；")));
                }
                if (!basicMaterialMap.isEmpty()&&basicMaterialMap.containsKey(dto.getMaterialId())){
                    List<BasicMaterialDto> basicMaterialDtos = basicMaterialMap.get(dto.getMaterialId());
                    if (CollectionUtils.isNotEmpty(basicMaterialDtos)){
                        for (BasicMaterialDto basic : basicMaterialDtos) {
                            try {
                                int key = basic.getSort() - 1;
                                setFieldValue(dto, "basicMaterialId" + FIELD_MAPPING.get(key), basic.getBasicMaterialId());
                                setFieldValue(dto, "sourceDescNames" + FIELD_MAPPING.get(key), StringUtils.isNotBlank(basic.getSourceDescNames()) ? basic.getSourceDescNames() : "/");
                                setFieldValue(dto, "contentTypeDesc" + FIELD_MAPPING.get(key), MaterialTypeEnum.getByCode(basic.getType()));
                                if (StringUtils.isNotBlank(basic.getMaterialCoverUrl())) {
                                    setFieldValue(dto, "materialUrl" + FIELD_MAPPING.get(key), urlReplace(basic.getMaterialCoverUrl()));
                                }
                                if (StringUtils.isNotBlank(basic.getMaterialUrl())) {
                                    setFieldValue(dto, "materialUrl" + FIELD_MAPPING.get(key), urlReplace(basic.getMaterialUrl()));
                                }
                                if (StringUtils.isNotBlank(basic.getDisplayPictureUrl())) {
                                    setFieldValue(dto, "materialUrl" + FIELD_MAPPING.get(key), urlReplace(basic.getDisplayPictureUrl()));
                                }
                                setFieldValue(dto, "contentText" + FIELD_MAPPING.get(key), StringUtils.isNotBlank(basic.getContent()) ? basic.getContent() : "/");
                            } catch (Exception e) {
                                log.error("动态拼接导出字段");
                            }
                        }
                    }
                }
            }

            List<MaterialExportOutDto> exportOutDtos = MaterialExportMapper.INSTANCE.convertExportDTO(resultList);
            //写入excel文件流
            EasyExcel.write(os, MaterialExportOutDto.class).sheet(sheetName).doWrite(exportOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            dataClientService.successTask(exportTask);
        }catch (Exception e){
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        }finally {
            try {
                if (os != null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    private void setFieldValue(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    private String urlReplace(String url){
        String oldUrl = "https://hslibrary.oss-cn-shanghai.aliyuncs.com/";
        String newUrl = "https://hslibrary.fotile.com/";
        if (StringUtils.isBlank(url)){
            return "/";
        }
        if (!url.startsWith(oldUrl)||!((url.contains("png")||url.contains("jpg"))
                ||(url.contains("PNG")||url.contains("JPG"))
                ||(url.contains("jpeg")||url.contains("JPEG")))){
            return url;
        }
        return newUrl+url.substring(oldUrl.length());
    }
}
