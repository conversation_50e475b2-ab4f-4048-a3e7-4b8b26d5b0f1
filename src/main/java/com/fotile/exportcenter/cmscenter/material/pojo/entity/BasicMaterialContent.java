package com.fotile.exportcenter.cmscenter.material.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.*;

/**
 * <AUTHOR>
 * 基础素材表
 * @date 2023/8/30 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("t_basic_material_content")
public class BasicMaterialContent extends AuditingEntity {
    /**
     * 素材id
     */
    @TableField(value = "material_id")
    private Long materialId;

    /**
     * 来源类型，1-内容列表，2-商品列表，3-视频，4-文件，5-文字，6-图片，7-外链
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 关联id，内容id或商品id
     */
    @TableField(value = "ref_id")
    private Long refId;

    /**
     * 关联code，目前只有商品编码
     */
    @TableField(value = "ref_code")
    private String refCode;

    /**
     * 关联名称，内容标题或商品名
     */
    @TableField(value = "ref_title")
    private String refTitle;

    /**
     * 素材名称，文件名/图片名
     */
    @TableField(value = "material_name")
    private String materialName;

    /**
     * 素材头图地址
     */
    @TableField(value = "material_cover_url")
    private String materialCoverUrl;

    /**
     * 素材地址，文件地址/图片地址
     */
    @TableField(value = "material_url")
    private String materialUrl;

    /**
     * 跳转链接
     */
    @TableField(value = "link_address")
    private String linkAddress;

    /**
     * 文字话术
     */
    @TableField(value = "content")
    private String content;

    /**
     * 顾客展示平台，1-5S小程序,存储appid
     */
    @TableField(value = "show_platform")
    private String showPlatform;

    /**
     * 顾客展示平台名称
     */
    @TableField(value = "show_platform_name")
    private String showPlatformName;

    /**
     * 页面路径
     */
    @TableField(value = "landing_page")
    private String landingPage;

    /**
     * 内容排序序号
     */
    @TableField(value = "sort")
    private Integer sort;
}
