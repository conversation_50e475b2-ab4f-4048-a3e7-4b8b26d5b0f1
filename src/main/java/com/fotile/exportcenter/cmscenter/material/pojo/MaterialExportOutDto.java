package com.fotile.exportcenter.cmscenter.material.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class MaterialExportOutDto {

    @ColumnWidth(5)
    @ExcelProperty(value = {"树ID"})
    private Integer treeId;
    @ColumnWidth(5)
    @ExcelProperty(value = {"树标题"})
    private String treeName;
    @ApiModelProperty(value = "分类树内容ID")
    @ColumnWidth(5)
    @ExcelProperty(value = {"分类树内容ID"})
    private Integer materialId;
    @ColumnWidth(40)
    @ExcelProperty(value = {"标题"})
    @ApiModelProperty(value = "标题")
    private String title;
    @ColumnWidth(5)
    @ExcelProperty(value = {"类型"})
    @ApiModelProperty(value = "类型")
    private String typeDesc;
    @ColumnWidth(20)
    @ExcelProperty(value = {"分类节点"})
    @ApiModelProperty(value = "分类节点")
    private String nodeName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"相关产品品类"})
    private String productName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"相关产品型号"})
    private String modelName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"内容标签"})
    private String materialTag;
    @ColumnWidth(3)
    @ExcelProperty(value = {"排序"})
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ColumnWidth(2)
    @ExcelProperty(value = {"状态"})
    @ApiModelProperty(value = "状态")
    private String statusDesc;
    @ColumnWidth(5)
    @ExcelProperty(value = {"发送数"})
    @ApiModelProperty(value = "发送数")
    private Integer shareCount;
    @ColumnWidth(5)
    @ExcelProperty(value = {"收藏数"})
    @ApiModelProperty(value = "收藏数")
    private Integer favoriteCount;
    @ColumnWidth(5)
    @ExcelProperty(value = {"有用数"})
    @ApiModelProperty(value = "有用数")
    private Integer helpfulCount;
    @ColumnWidth(5)
    @ExcelProperty(value = {"无用数"})
    @ApiModelProperty(value = "无用数")
    private Integer unhelpfulCount;
    @ColumnWidth(10)
    @ExcelProperty(value = {"最近编辑时间"})
    @ApiModelProperty(value = "修改时间")
    private String modifiedDateDesc;
    @ApiModelProperty(value = "修改人")
    @ColumnWidth(10)
    @ExcelProperty(value = {"最新编辑人"})
    private String modifiedUsername;
    @ColumnWidth(10)
    @ExcelProperty(value = {"创建时间"})
    @ApiModelProperty(value = "创建时间")
    private String createdDateDesc;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号1"})
    private Integer basicMaterialIdOne;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesOne;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescOne;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlOne;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextOne;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号2"})
    private Integer basicMaterialIdSecond;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesSecond;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescSecond;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlSecond;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextSecond;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号3"})
    private Integer basicMaterialIdThird;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesThird;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescThird;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlThird;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextThird;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号4"})
    private Integer basicMaterialIdFourth;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesFourth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescFourth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlFourth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextFourth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号5"})
    private Integer basicMaterialIdFifth;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesFifth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescFifth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlFifth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextFifth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号6"})
    private Integer basicMaterialIdSix;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesSix;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescSix;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlSix;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextSix;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号7"})
    private Integer basicMaterialIdSeven;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesSeven;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescSeven;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlSeven;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextSeven;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号8"})
    private Integer basicMaterialIdEighth;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesEighth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescEighth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlEighth;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextEighth;
    @ColumnWidth(5)
    @ExcelProperty(value = {"素材内容编号9"})
    private Integer basicMaterialIdNine;
    @ColumnWidth(20)
    @ExcelProperty(value = {"来源说明"})
    private String sourceDescNamesNine;
    @ColumnWidth(5)
    @ExcelProperty(value = {"基础素材内容类型"})
    @ApiModelProperty(value = "素材类型")
    private String contentTypeDescNine;
    @ColumnWidth(40)
    @ExcelProperty(value = {"素材地址"})
    private String materialUrlNine;
    @ColumnWidth(40)
    @ExcelProperty(value = {"文本内容"})
    private String contentTextNine;
}
