package com.fotile.exportcenter.cmscenter.material.pojo;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BasicMaterialDto {
    /**
     * 内容树ID
     */
    private Integer materialId;
    /**
     * 基础素材内容编号
     */
    private Integer basicMaterialId;

    private Integer sort;
    /**
     * 来源说明名称
     */
    private String sourceDescNames;
    /**
     * 素材类型
     */
    private Integer type;
    /**
     * 内容列表文本
     */
    private String content;
    /**
     * 素材头图地址
     */
    private String materialCoverUrl;

    /**
     * 素材地址，文件地址/图片地址
     */
    private String materialUrl;
    /**
     * 页面路径
     */
    private String displayPictureUrl;

}
