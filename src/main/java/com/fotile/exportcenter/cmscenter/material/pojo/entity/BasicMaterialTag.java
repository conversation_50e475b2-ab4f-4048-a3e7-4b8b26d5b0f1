package com.fotile.exportcenter.cmscenter.material.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.*;

/**
 * <AUTHOR>
 * 基础素材标签表
 * @date 2023/8/30 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("t_basic_material_tag")
public class BasicMaterialTag extends AuditingEntity {

    /**
     * 素材id
     */
    @TableField(value = "material_id")
    private Long materialId;

    /**
     * 标签id
     */
    @TableField(value = "tag_id")
    private Long tagId;

    /**
     * 标签组id
     */
    @TableField(value = "tag_group_id")
    private Long tagGroupId;
    /**
     * 标签类型  1 内容标签 2 内容匹配顾客标签
     */
    @TableField(value = "tag_type")
    private Integer tagType;
}
