package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/9 14:09
 * 企微内容操作记录查询入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MaterialEventLogQueryInputDTO implements Serializable {
    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    @NotNull(message = "每页显示条数不能为空")
    private Integer pageSize;

    /**
     * 页面类型
     */
    @NotNull(message = "页面类型不能为空,1-导购访问记录,2-导购内容执行动作,3-内容树节点访问记录")
    private Integer pageType;

    /**
     * 业务员标签
     */
    private List<String> salesmanTagList;

    private List<Long> salesmanTagIdList;
    private Long salesmanId;

    /**
     * 事件类型：10：访问页面，20：收藏，30：点赞，40：点踩，50：发送，60：树节点切换
     */
    private List<Integer> eventTypeList;

    /**
     * 树内容组id：当事件类型是20，30，40，50时不能为空
     */
    private List<Integer> materialIdList;

    /**
     * 聊天素材树id，事件类型为60时不能为空
     */
    private List<Integer> treeIdList;

    /**
     * 聊天素材树节点id，事件类型为60时不能为空
     */
    private List<Long> nodeIdList;

    /**
     * 账号
     */
    private String userName;

    /**
     * 账号姓名
     */
    private String firstName;

    /**
     * 岗位列表
     */
    private List<Integer> stationList;

    /**
     * 大区code
     */
    private List<String> areaCodeList;

    /**
     * 分公司id
     */
    private List<Long> companyIdList;

    /**
     * 门店id
     */
    private List<Long> storeIdList;

    /**
     * 门店渠道
     */
    private List<String> storeChannelCodeList;

    /**
     * 门店标签
     */
    private List<String> storeTagCodeList;

    /**
     * 门店关键字
     */
    private List<String> storeKeywordList;

    /**
     * 访问开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdDateStart;

    private String createdDateStartStr;

    /**
     * 访问结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdDateEnd;

    private String createdDateEndStr;

    /**
     * mybatis分页查询用
     */
    private Long offset;

    /**
     * 业务类型：1-内容工场 2-找案例 3-找产品
     */
    private List<Integer> bizTypeList;

    /**
     * 关联数据类型，4:内容专题
     */
    private Integer relationSourceType=4;

    /**
     * 查询转发数的分组统计字段
     */
    private String relayGroupBy="relation_parent_id";

    private Long relationParentId;

    private List<Long> relationParentIdList;

}
