package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DeviseSchemeMainInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * id
     */
	private Long id;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Long isDeleted;
	
	/**
     * 创建者
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 创建时间
     */
	private Date createdDate;
	
	/**
     * 修改者
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 修改时间
     */
	private Date modifiedDate;
	
	/**
     * 方案编码
     */
	private String projectCode;
	
	/**
     * 方案标题
     */
	private String projectName;
	
	/**
     * 1:通用,2:小区通用,3:个性化
     */
	private Integer type;
	
	/**
     * 分公司orgid
     */
	private Long companyId;
	
	/**
     * 分公司名称
     */
	private String companyName;
	
	/**
     * 门店orgid
     */
	private Long storeOrgId;
	
	/**
     * 门店code
     */
	private String storeCode;
	
	/**
     * 门店name
     */
	private String storeName;
	
	/**
     * 门店简称
     */
	private String abbreviation;
	
	/**
     * 省id
     */
	private Long provinceId;
	
	/**
     * 省
     */
	private String provinceName;
	
	/**
     * 市id
     */
	private Long cityId;
	
	/**
     * 市
     */
	private String cityName;
	
	/**
     * 县/区id
     */
	private Long countyId;
	
	/**
     * 县/区
     */
	private String countyName;
	
	/**
     * 小区id
     */
	private Long villageId;
	
	/**
     * 小区
     */
	private String villageName;
	
	/**
     * 线索id
     */
	private Long cluesId;
	
	/**
     * 0禁用，1启用
     */
	private Integer status;
	
	/**
     * 附件链接
     */
	private String projectUrl;

	/**
     * 附件名
     */
	private String attachmentName;
	
	/**
     * 备注
     */
	private String remark;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 最新修改时间
     */
	private Date reviseTime;
	
	/**
     * 最新修改人名称
     */
	private String reviseName;
	
	/**
     * 创建人名称
     */
	private String createdName;

	/**
     * 讲解状态，0：未讲解，1：已讲解
     */
	private Integer explainStatus;

	/**
     * 复制父id
     */
	private Long parentId;

	/**
     * 优秀案例标志，1是，0否
     */
	private Integer excellentFlag;

	/**
     * 封面图url
     */
	private String coverPictureUrl;

	/**
     * 3d链接
     */
	private String threeDimensionalLink;

	/**
	 * 视频演示链接
	 */
	private String viewShowLink;

	/**
	 * 视频演示名称
	 */
	private String viewShowName;

	/**
	 * 视频演示首图
	 */
	private String viewShowCover;


	/**
	 * 讲解教学视频url
	 */
	private String explainVideoUrl;
	/**
	 * 讲解教学视频封面图
	 */
	private String explainVideoCover;
	/**
	 * 讲解教学视频url
	 */
	private String explainVideoName;
	/**
	 * 线索名称
	 */
	private String cluesCustomerName;
	/**
	 * 线索手机号
	 */
	private String cluesCustomerPhone;



	/**
	 *标题省id
	 */
	private Long titleProvinceId;

	/**
	 *标题省名称
	 */
	private String titleProvinceName;
	/**
	 *标题市id
	 */
	private Long titleCityId;
	/**
	 *标题市名称
	 */
	private String titleCityName;
	/**
	 *标题小区id
	 */
	private Long titleVillageId;
	/**
	 *标题小区名称
	 */
	private String titleVillageName;
	/**
	 *楼栋号
	 */
	private String buildingNum;

	/**
	 *厨房户型
	 */
	private Integer kitchenType;

	/**
	 *面积
	 */
	private String kitchenArea;

}