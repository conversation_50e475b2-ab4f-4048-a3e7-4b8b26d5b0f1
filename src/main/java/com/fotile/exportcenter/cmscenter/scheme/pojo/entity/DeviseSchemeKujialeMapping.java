package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 场景方案酷家乐方案关联表(DeviseSchemeKujialeMapping)实体类
 *
 * <AUTHOR>
 * @since 2025-02-28 10:28:04
 */

@Data
public class DeviseSchemeKujialeMapping implements Serializable {
    private static final long serialVersionUID = 699253907804025969L;

    private Long id;
    /**
     * 是否删除：0：否；其它：是
     */
    private Long isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;
    /**
     * 方案id
     */
    private Long schemeId;
    /**
     * 酷家乐方案id
     */
    private String planId;
    /**
     * 酷家乐方案name
     */
    private String planName;
    /**
     * 酷家乐方案返回数据json
     */
    private String planJson;


}
