package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DeviseSchemeExplainRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "id")
	private Long id;
	
	@ApiModelProperty(value = "是否删除：0：否；其它：是")
	private Long isDeleted;
	
	@ApiModelProperty(value = "创建者")
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "创建时间")
	private Date createdDate;
	
	@ApiModelProperty(value = "修改者")
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "修改时间")
	private Date modifiedDate;
	
	@ApiModelProperty(value = "方案id")
	private Long schemeId;
	
	@ApiModelProperty(value = "分公司orgid")
	private Long companyId;
	
	@ApiModelProperty(value = "分公司名称")
	private String companyName;
	
	@ApiModelProperty(value = "门店orgid")
	private Long storeOrgId;
	
	@ApiModelProperty(value = "门店code")
	private String storeCode;
	
	@ApiModelProperty(value = "门店name")
	private String storeName;
	
	@ApiModelProperty(value = "门店简称")
	private String abbreviation;
	
	@ApiModelProperty(value = "提交人业务员id")
	private Long chargeUserId;
	
	@ApiModelProperty(value = "提交人业务员名称")
	private String chargeUserName;
	
	@ApiModelProperty(value = "提交人业务员编码")
	private String chargeCode;
	
	@ApiModelProperty(value = "提交人业务员手机号")
	private String chargePhone;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "开始时间")
	private Date startTime;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "结束时间")
	private Date endTime;
	
	@ApiModelProperty(value = "讲解时长，单位s")
	private Integer durationTime;
	
	@ApiModelProperty(value = "备注")
	private String remark;
	
	@ApiModelProperty(value = "创建人名称")
	private String createdName;

	private Integer status;

	private Date reviseTime;

	private Integer source;

	/**
	 * 讲解线索id
	 */
	private Long explainCluesId;

	/**
	 * 是否需要线索标志
	 */
	private Integer needCluesFlag;


	/**
	 * 关联线索时间
	 */
	private Date relatedCluesTime;

	/**
	 * 暂停时间
	 */
	private Date pauseTime;


	/**
	 * 异常结束类型，0正常结束，1定时任务异常结束(播放任务中断自动结束讲解)，2长时间前端调用结束(长时间未操作自动结束讲解)
	 */
	private Integer abnormalEndType;


}