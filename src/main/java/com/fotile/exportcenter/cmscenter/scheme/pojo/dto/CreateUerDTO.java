package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class CreateUerDTO implements Serializable {
    @ApiModelProperty(value = "创建者")
    private String createdBy;

    /**
     * 门店orgid
     */
    @ApiModelProperty(value = "创建人门店orgid")
    private Long createStoreOrgId;

    /**
     * 门店code
     */
    @ApiModelProperty(value = "创建人门店code")
    private String createStoreCode;

    /**
     * 门店name
     */
    @ApiModelProperty(value = "创建人门店name")
    private String createStoreName;

    /**
     * 门店简称
     */
    @ApiModelProperty(value = "创建人门店简称")
    private String createAbbreviation;

    /**
     * 提交人业务员id
     */
    @ApiModelProperty(value = "创建人业务员id")
    private Long createChargeUserId;

    /**
     * 提交人业务员名称
     */
    @ApiModelProperty(value = "创建人业务员名称")
    private String createChargeUserName;

    /**
     * 提交人业务员编码
     */
    @ApiModelProperty(value = "创建人业务员编码")
    private String createChargeCode;

    /**
     * 提交人业务员手机号
     */
    @ApiModelProperty(value = "创建人业务员手机号")
    private String createChargePhone;


}
