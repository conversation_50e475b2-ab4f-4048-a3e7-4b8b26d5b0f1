package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Data
public class GetSchemeListDTO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    private Long offSize;

    private String keyWords;

    private String goodsKeyWords;

    private Long goodsId;

    private String goodsCode;

    /**
     * 商品分类idList
     */
    private List<Long> goodsCategoryIdList;
    /**
     * 商品idList
     */
    private List<Long> goodsIdList;


    private Integer excellentFlag;

    /**
     * 大区
     */
    private Long area;

    /**
     * 成交金额开始值
     */
    private BigDecimal amountStart;
    /**
     * 成交金额结束值
     */
    private BigDecimal amountEnd;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 查询条数
     */
    private Integer totalCount;


    /**
     * 开始条数
     */
    private Integer startCount;

    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ApiModelProperty(value = "1:通用,2:小区通用,3:个性化")
    private Integer type;

    private List<Long> ids;

    private List<Long> companyIds;

    private List<Long> authCompanyIds;

    private List<Long> companyArea;

    private List<Long> storeOrgIds;

    private List<Long> authStoreOrgIds;

    @ApiModelProperty(value = "省id")
    private Long provinceId;

    @ApiModelProperty(value = "市id")
    private Long cityId;

    @ApiModelProperty(value = "县/区id")
    private Long countyId;

    @ApiModelProperty(value = "小区")
    private String villageName;

    @ApiModelProperty(value = "线索id")
    private Long cluesId;

    @ApiModelProperty(value = "0禁用，1启用")
    private Integer status;

    private String createdName;

    /**
     * 创建人岗位id
     */
    private List<Long> createdStationList;

    /**
     * 创建人能力标签,最终转化为createdChargeUserIdList条件
     *
     */
    private List<String> createdLabelValueList;

    private Date createdStartDate;

    private Date createdEndDate;

    private String aesKey;

    /**
     * 5:3d连接   6：场景化
     */
    private Integer contentType;

    private Long userId;

    //标签ids
    private List<Long> labelIds;

    //根据标签查询获取后的方案id交集
    private List<Long> labelSchemeIds;

    private List<SchemeLabelLinkDTO> labelFieldIdAndAttributeIds;

    //根据标签查询获取后的方案id交集
    private Map<String, List<SchemeLabelLinkDTO>> groupedMap;

    //线索id集合
    private List<Long> cluesIds;

    /**
     * es查询冗余字段的条件，id会作为排序条件使用
     */
    private List<Long> esCaseIds;

    private String threeDUrlLike;

    /**
     * 关联酷家乐方案id
     */
    private String planName;

    /**
     * 门店渠道
     */
    private List<String> storeChannelCodeList;
    /**
     * 门店渠道细分
     */
    private List<String> storeChannelSubdivisionCodeList;

    private Integer storeChannelSubDivisionCodeFlag;

    /**
     * 创建人业务员id
     */
    private List<Long> createdChargeUserIdList;
}
