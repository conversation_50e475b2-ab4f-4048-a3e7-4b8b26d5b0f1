package com.fotile.exportcenter.cmscenter.scheme.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeviseSchemeVO implements Serializable {

    @ApiModelProperty(value = "院子id")
    private Long id;

    @ApiModelProperty(value = "是否删除：0：否；其它：是")
    private Long isDeleted;

    @ApiModelProperty(value = "创建者")
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;

    @ApiModelProperty(value = "修改者")
    private String modifiedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date modifiedDate;

    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ApiModelProperty(value = "1:通用,2:小区通用,3:个性化")
    private Integer type;

    @ApiModelProperty(value = "分公司orgid")
    private Long companyId;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "门店orgid")
    private Long storeOrgId;

    @ApiModelProperty(value = "门店code")
    private String storeCode;

    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ApiModelProperty(value = "门店简称")
    private String abbreviation;

    @ApiModelProperty(value = "省id")
    private Long provinceId;

    @ApiModelProperty(value = "省")
    private String provinceName;

    @ApiModelProperty(value = "市id")
    private Long cityId;

    @ApiModelProperty(value = "市")
    private String cityName;

    @ApiModelProperty(value = "县/区id")
    private Long countyId;

    @ApiModelProperty(value = "县/区")
    private String countyName;

    @ApiModelProperty(value = "小区id")
    private Long villageId;

    @ApiModelProperty(value = "小区")
    private String villageName;

    @ApiModelProperty(value = "线索id")
    private Long cluesId;

    @ApiModelProperty(value = "0禁用，1启用")
    private Integer status;

    @ApiModelProperty(value = "附件链接")
    private String projectUrl;

    @ApiModelProperty(value = "备注")
    private String remark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "最新修改时间")
    private Date reviseTime;

    @ApiModelProperty(value = "最新修改人名称")
    @FieldEncrypt
    private String reviseName;

    @ApiModelProperty(value = "创建人名称")
    @FieldEncrypt
    private String createdName;

    @ApiModelProperty(value = "线索名称")
    private String cluesCustomerName;

    @ApiModelProperty(value = "线索手机号")
    private String cluesCustomerPhone;

    /**
     * 创建人业务员id
     */
    private Long createdChargeUserId;

    /**
     * 创建人业务员名称
     */
    @FieldEncrypt
    private String createdChargeUserName;

    /**
     * 创建人业务员编码
     */
    private String createdChargeCode;

    /**
     * 创建人业务员手机号
     */
    @FieldEncrypt
    private String createdChargePhone;


    private String labelNames;

    private Integer explainCount;

    private String durationTimes;

    private Integer excellentFlag;

    private String goodsName;

    /**
     * 讲解线索数
     */
    private Integer explainCluesCount;

    /**
     * 讲解成交线索数
     */
    private Integer explainDealCluesCount;

    /**
     * 成交金额
     */
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "封面图url")
    private String coverPictureUrl;


    /**
     * 成交金额
     */
    private BigDecimal dealOrderAmount;

    /**
     * 门店orgid
     */
    @ApiModelProperty(value = "创建人门店orgid")
    private Long createStoreOrgId;

    /**
     * 门店code
     */
    @ApiModelProperty(value = "创建人门店code")
    private String createStoreCode;

    /**
     * 门店name
     */
    @ApiModelProperty(value = "创建人门店name")
    private String createStoreName;

    /**
     * 门店简称
     */
    @ApiModelProperty(value = "创建人门店简称")
    private String createAbbreviation;

    /**
     * 提交人业务员id
     */
    @ApiModelProperty(value = "创建人业务员id")
    private Long createChargeUserId;

    /**
     * 提交人业务员名称
     */
    @ApiModelProperty(value = "创建人业务员名称")
    private String createChargeUserName;

    /**
     * 提交人业务员编码
     */
    @ApiModelProperty(value = "创建人业务员编码")
    private String createChargeCode;

    /**
     * 提交人业务员手机号
     */
    @ApiModelProperty(value = "创建人业务员手机号")
    private String createChargePhone;

    /**
     * 创建人岗位
     */
    @ApiModelProperty(value = "创建人岗位")
    private String createChargeStationName;

    /**
     * 创建人能力标签
     */
    @ApiModelProperty(value = "创建人能力标签")
    private String createChargeLabelValue;

    private String typeName;

    private String relationClues;

    private String statusName;
    /**
     * 所属大区
     */
    @ApiModelProperty(value = "所属大区")
    private String areaName;

    /**
     * 3D浏览数
     */
    private Integer threeViewCount;

    /**
     * 3D点赞数
     */
    private Integer threeThumbsCount;

    /**
     * 3D收藏数
     */
    private Integer threeCollectCount;

    /**
     * 3D发送数
     */
    private Integer threeSendCount;


    /**
     * 通用浏览数
     */
    private Integer commonViewCount;

    /**
     * 通用点赞数
     */
    private Integer commonThumbsCount;

    /**
     * 通用收藏数
     */
    private Integer commonCollectCount;

    /**
     * 通用发送数
     */
    private Integer commonSendCount;
    /**
     * 关联酷家乐方案id
     */
    private String planId;
    /**
     * 酷家乐方案name
     */
    private String planName;

    /**
     * 门店渠道
     */
    private String storeChannelName;
    /**
     * 门店渠道细分
     */
    private String storeSubChannelName;
}
