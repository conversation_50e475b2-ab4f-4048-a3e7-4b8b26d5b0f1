package com.fotile.exportcenter.cmscenter.scheme.dao;

import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.SchemeCaseStatisticalDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户案例点赞表(cmscenter.content_case_user_helpful)Dao类
 *
 * <AUTHOR>
 * @since 2023-12-13 19:25:50
 */
@Mapper
public interface ContentCaseUserHelpfulDao {


     List<SchemeCaseStatisticalDTO> getSchemeCaseHelpful(@Param("caseIds") List<Long> ids);


}

