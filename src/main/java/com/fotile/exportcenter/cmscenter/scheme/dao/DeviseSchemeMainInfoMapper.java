package com.fotile.exportcenter.cmscenter.scheme.dao;


import com.fotile.exportcenter.client.pojo.org.SalesmanLabel;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.GetSchemeListDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO;
import com.fotile.exportcenter.common.KvLs;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景化方案主表 DeviseSchemeMainInfoMapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-12-21 16:11:20
 */
public interface DeviseSchemeMainInfoMapper {

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<DeviseSchemeVO> findSchemeList(GetSchemeListDTO getSchemeListDTO);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<StoreInfoVO> getStoreInfo(@Param("ids") List<Long> ids);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<KvLs> findSalesmanStation(@Param("salesmanIds") List<Long> salesmanIds);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SalesmanLabel> querySalesmanLabel(@Param("salesmanIds") List<Long> salesmanIds);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<Long> querySalesmanIdByLabelValue(@Param("labelValueList") List<String> labelValueList);
}