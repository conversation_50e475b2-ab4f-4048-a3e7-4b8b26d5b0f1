package com.fotile.exportcenter.cmscenter.scheme.service.impl;

import com.alibaba.excel.EasyExcel;
import com.fotile.exportcenter.client.*;
import com.fotile.exportcenter.client.pojo.SalesmanInfoForQYWX;
import com.fotile.exportcenter.client.pojo.UserEntityExtend;
import com.fotile.exportcenter.client.pojo.org.SalesmanLabel;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.*;
import com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeGoodsMapping;
import com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeKujialeMapping;
import com.fotile.exportcenter.cmscenter.scheme.pojo.entity.DeviseSchemeStoreMapping;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeVO;
import com.fotile.exportcenter.cmscenter.scheme.dao.*;
import com.fotile.exportcenter.cmscenter.scheme.mapper.ExportImpl;
import com.fotile.exportcenter.cmscenter.scheme.mapper.SchemeMapper;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO;
import com.fotile.exportcenter.common.KvLs;
import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyAreaByOrgIdsOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByIdsInDto;
import com.fotile.exportcenter.marketing.service.SalesLeadsService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.exportcenter.util.SplitUtils;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component("121")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class SchemeExportServiceImpl implements ExportImpl {


    @Autowired
    private DeviseSchemeMainInfoMapper deviseSchemeMainInfoMapper;

    @Autowired
    private DeviseSchemeStoreMappingMapper deviseSchemeStoreMappingMapper;

    @Autowired
    private DeviseSchemeLabelMappingMapper deviseSchemeLabelMappingMapper;

    @Autowired
    private DeviseSchemeExplainRecordMapper deviseSchemeExplainRecordMapper;

    @Autowired
    private DeviseSchemeGoodsMappingMapper deviseSchemeGoodsMappingMapper;

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    private UserEntityClient userEntityClient;

    @Autowired
    private MarketingClient marketingClient;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private SalesLeadsService salesLeadsService;

    @Autowired
    private OssService ossService;

    @Autowired
    private MaterialEventLogMapper materialEventLogMapper;

    @Autowired
    private ContentCaseUserHelpfulDao contentCaseUserHelpfulDao;

    @Autowired
    private ContentCaseUserCollectDao contentCaseUserCollectDao;
    @Autowired
    private DeviseSchemeKujialeMappingDao deviseSchemeKujialeMappingDao;
    @Autowired
    private WorkflowClient workflowClient;



    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        //类型转换
        GetSchemeListDTO inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), GetSchemeListDTO.class);
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count1 = 1000;
        Integer start1 = inDto.getStartCount();
        Integer i = totalCount / count1;
        Integer lastCount = totalCount % count1;
        Integer size = 0;

        if (CollectionUtils.isNotEmpty(inDto.getCreatedLabelValueList())) {
            List<Long> salesmanIds = getSalesmanIdsByLabelValue(inDto.getCreatedLabelValueList());
            if (CollectionUtils.isEmpty(salesmanIds)) {
                throw new BusinessException("未能查询到结果！");
            }
            if (CollectionUtils.isEmpty(inDto.getCreatedChargeUserIdList())) {
                inDto.setCreatedChargeUserIdList(salesmanIds);
            } else {
                List<Long> chargeUserIds = inDto.getCreatedChargeUserIdList();
                chargeUserIds.retainAll(salesmanIds);
                if (CollectionUtils.isEmpty(chargeUserIds)) {
                    throw new BusinessException("未能查询到结果！");
                }
                inDto.setCreatedChargeUserIdList(chargeUserIds);
            }
        }

        log.info("场景化导出定时任务开始时间："+System.currentTimeMillis());
        List<DeviseSchemeVO> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count1;
            size = count1;
            if (j == i) {
                size = lastCount;
            }
            inDto.setOffSize(start.longValue());
            inDto.setPageSize(size);
            List<DeviseSchemeVO> outDto = deviseSchemeMainInfoMapper.findSchemeList(inDto);

            if (CollectionUtils.isNotEmpty(outDto)) {
                List<Long> salesmanIds = outDto.stream().map(DeviseSchemeVO::getCreatedChargeUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                // 查询创建人岗位
                Map<Long, String> stationMap = querySalesmanStation(salesmanIds);
                // 查询创建人能力标签
                Map<Long, String> labelValueMap = querySalesmanLabel(salesmanIds);

                for (DeviseSchemeVO vo : outDto) {
                    vo.setCreateChargeStationName(stationMap.get(vo.getCreatedChargeUserId()));
                    vo.setCreateChargeLabelValue(labelValueMap.get(vo.getCreatedChargeUserId()));
                }

                resultList.addAll(outDto);
            }



            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getPageSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);
        }
        log.info("场景化导出定时任务结束时间："+System.currentTimeMillis());
        if (resultList == null || resultList.size() <= 0) {
            throw new BusinessException("未能查询到结果！");
        }


        if (resultList != null && resultList.size() > 0) {
            List<Long> ids = resultList.parallelStream().map(DeviseSchemeVO::getId).collect(Collectors.toList());
            List<Long> clueIds = resultList.parallelStream().filter(p -> p.getCluesId() != null).map(DeviseSchemeVO::getCluesId).distinct().collect(Collectors.toList());
            //查询标签
            log.info("查询场景化导出关联标签开始时间："+System.currentTimeMillis());
            List<DeviseSchemeLabelMappingDTO> labelMappingDTOS = getLabelMappingDTOS(ids);
//            List<DeviseSchemeLabelMappingDTO> labelMappingDTOS = deviseSchemeLabelMappingMapper.selectPropertyListBySchemeId(ids);
            log.info("查询场景化导出关联标签结束时间："+System.currentTimeMillis());
            //获取创建人userid
            List<String> userIds = resultList.parallelStream().map(DeviseSchemeVO :: getCreatedBy).distinct().collect(Collectors.toList());
            List<CreateUerDTO> createUerDTOList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(userIds)){
                createUerDTOList = setCreateUserInfo(userIds);
            }
            log.info("查询场景化创建人结束时间："+System.currentTimeMillis());
            //先分组

            if (labelMappingDTOS != null && labelMappingDTOS.size() > 0) {
                if(CollectionUtils.isNotEmpty(labelMappingDTOS)){
                    try {
                        List<BaseLabelDTO> baseLabelDTOList = marketingClient.queryBaseLabel("20", 275L).getData();
                        if (CollectionUtils.isNotEmpty(baseLabelDTOList)){
                            labelMappingDTOS.forEach(x ->{
                                BaseLabelDTO baseLabelDTO = baseLabelDTOList.parallelStream().filter(y -> y.getGroupId().equals(x.getFieldId()) && y.getLabelId().equals(x.getPropertyParameterId())).findFirst().orElse(null);
                                if(baseLabelDTO != null && StringUtils.isNotBlank(baseLabelDTO.getLabelName())){
                                    x.setName(baseLabelDTO.getLabelName());
                                }
                            });
                        }
                    } catch (Exception e) {
                        log.error("匹配场景化标签数据错误：{}", e.toString());
                    }
                }
            }
            Map<Long, List<DeviseSchemeLabelMappingDTO>> groupedMap = new HashMap<>();
            if (labelMappingDTOS != null && labelMappingDTOS.size() > 0) {
                groupedMap =
                        labelMappingDTOS.stream().collect(Collectors.groupingBy(DeviseSchemeLabelMappingDTO::getSchemeId));
            }

            //查询门店
            List<DeviseSchemeStoreMapping> storeMappingList = getStoreListBySchemeIds(ids);
//            List<DeviseSchemeStoreMapping> storeMappingList = deviseSchemeStoreMappingMapper.selectListBySchemeIds(ids);
            Map<Long, List<DeviseSchemeStoreMapping>> groupStoreMap = new HashMap<>();
            if (storeMappingList != null && storeMappingList.size() > 0) {
                groupStoreMap = storeMappingList.stream().collect(Collectors.groupingBy(DeviseSchemeStoreMapping::getSchemeId));
            }
            log.info("查询场景化门店结束时间："+System.currentTimeMillis());

            List<FindCompanyAreaByOrgIdsOutDto> companyAreaByOrgIdsOutDtoList = new ArrayList<>();
            List<Long> companyIds = resultList.parallelStream().filter(x -> x.getCompanyId() != null).map(DeviseSchemeVO::getCompanyId).distinct().collect(Collectors.toList());
            if (companyIds != null && companyIds.size() > 0) {
//                FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
//                findCompanyByIdsInDto.setIdList(companyIds);
//                companyAreaByOrgIdsOutDtoList = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
                companyAreaByOrgIdsOutDtoList = findCompanyByOrgIdList(companyIds);

            }
            log.info("查询场景化公司大区结束时间："+System.currentTimeMillis());

            List<DeviseSchemeGoodsMapping> goodsMappingList = selectListBySchemeIds(ids);
            log.info("查询场景化关联商品结束时间："+System.currentTimeMillis());
//            List<DeviseSchemeGoodsMapping> goodsMappingList = deviseSchemeGoodsMappingMapper.selectListBySchemeIds(ids);
            Map<Long, List<DeviseSchemeGoodsMapping>> groupGoodsMap = new HashMap<>();
            if (goodsMappingList != null && goodsMappingList.size() > 0) {
                groupGoodsMap = goodsMappingList.stream().collect(Collectors.groupingBy(DeviseSchemeGoodsMapping::getSchemeId));
            }
            //查询线索
            List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOList = new ArrayList<>();
            if (clueIds != null && clueIds.size() > 0) {
//                cluesCustomerInfoForSchemeDTOList = marketingClient.queryCluesCustomerIndoByCluesIdsOpen(clueIds).getData();
                cluesCustomerInfoForSchemeDTOList = queryCluesCustomerIndoByCluesIdsOpen(clueIds);
            }
            log.info("查询场景化关联线索结束时间："+System.currentTimeMillis());
            //获取3D统计数据
//            List<SchemeMaterialStatisticalDTO> threeStatisticalList = materialEventLogMapper.getSchemeMaterialStatistical(5,ids);
            List<SchemeMaterialStatisticalDTO> threeStatisticalList = getSchemeMaterialStatistical(ids);
            log.info("场景化3D统计数据结束时间："+System.currentTimeMillis());
            //获取通用统计数据
            //List<SchemeMaterialStatisticalDTO> commonStatisticalList = materialEventLogMapper.getSchemeMaterialStatistical(6,ids);
            //获取点赞数据
//            List<SchemeCaseStatisticalDTO> helpFulList = contentCaseUserHelpfulDao.getSchemeCaseHelpful(ids);
            List<SchemeCaseStatisticalDTO> helpFulList = getSchemeCaseHelpful(ids);
            log.info("场景化点赞数据结束时间："+System.currentTimeMillis());
            //获取收藏数据
//            List<SchemeCaseStatisticalDTO> collectList = contentCaseUserCollectDao.getSchemeCaseCollect(ids);
            List<SchemeCaseStatisticalDTO> collectList = getSchemeCaseCollect(ids);
            log.info("场景化收藏数据结束时间："+System.currentTimeMillis());


            //查询讲解
//            List<ExplainRecordDTO> explainRecordDTOS = deviseSchemeExplainRecordMapper.selectListBySchemeIds(ids);
            List<ExplainRecordDTO> explainRecordDTOS = getExplainListBySchemeIds(ids);
            log.info("场景化查询讲解结束时间："+System.currentTimeMillis());
            //查询成交线索数
//            List<DealCluesCountDTO> dealCluesCountDTOS = deviseSchemeExplainRecordMapper.selectDealCluesCountBySchemeIds(ids);
            List<DealCluesCountDTO> dealCluesCountDTOS = selectDealCluesCountBySchemeIds(ids);
            log.info("场景化查询成交线索数结束时间："+System.currentTimeMillis());
            //查询讲解记录关联的线索
            List<Long> explainClueIds = new ArrayList<>();
            if(explainRecordDTOS != null && explainRecordDTOS.size() > 0){
                for(ExplainRecordDTO explainRecordDTO : explainRecordDTOS){
                    if(StringUtils.isNotBlank(explainRecordDTO.getExplainCluesIds())){
                        List<String> ss = Arrays.asList(explainRecordDTO.getExplainCluesIds().split(","));
                        List<Long> clues = ss.stream().filter(x -> StringUtils.isNotBlank(x)).map(item ->
                                Long.parseLong(item)).distinct().collect(Collectors.toList());
                        if(clues != null && clues.size() > 0){
                            explainClueIds.addAll(clues);
                        }
                    }
                }
            }
            //查询酷家乐
            List<DeviseSchemeKujialeMapping> kujialeMappingList = getKujialeListBySchemeIds(ids);
            log.info("场景化导出匹配参数开始时间："+System.currentTimeMillis());

            List<StoreInfoVO> storeInfos = deviseSchemeMainInfoMapper.getStoreInfo(ids);
            Map<String, StoreInfoVO> chargeStoreMap = Maps.newHashMap();
            if (!org.springframework.util.CollectionUtils.isEmpty(storeInfos)){
                chargeStoreMap = storeInfos.parallelStream().collect(Collectors.toMap(StoreInfoVO::getCreatedBy, s -> s));
            }

            for (DeviseSchemeVO deviseSchemeVO : resultList) {
                deviseSchemeVO.setOrderAmount(deviseSchemeVO.getDealOrderAmount());
                if (cluesCustomerInfoForSchemeDTOList != null && cluesCustomerInfoForSchemeDTOList.size() > 0) {
                    CluesCustomerInfoForSchemeDTO customerInfoForSchemeDTO = cluesCustomerInfoForSchemeDTOList.parallelStream().filter(x -> x.getId().equals(deviseSchemeVO.getCluesId())).findFirst().orElse(null);
                    if (customerInfoForSchemeDTO != null) {
                        deviseSchemeVO.setCluesCustomerName(customerInfoForSchemeDTO.getCustomerName());
                        deviseSchemeVO.setCluesCustomerPhone(customerInfoForSchemeDTO.getCustomerPhone());
                    }
                }
                if (explainRecordDTOS != null && explainRecordDTOS.size() > 0) {
                    ExplainRecordDTO explainRecordDTO = explainRecordDTOS.parallelStream().filter(x -> x.getSchemeId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if (explainRecordDTO != null) {
                        deviseSchemeVO.setExplainCount(explainRecordDTO.getCount());
                        deviseSchemeVO.setDurationTimes(changeTimeSecond2Minute(explainRecordDTO.getDurationTime()));
                        if (explainRecordDTO.getExplainCluesIds() != null) {
                            List<String> ss = Arrays.asList(explainRecordDTO.getExplainCluesIds().split(","));
                            if (CollectionUtils.isNotEmpty(ss)) {
                                List<Long> clues = ss.stream().filter(x -> StringUtils.isNotBlank(x)).map(item ->
                                        Long.parseLong(item)).distinct().collect(Collectors.toList());
                                deviseSchemeVO.setExplainCluesCount(clues.size());
                            }
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(companyAreaByOrgIdsOutDtoList)){
                    Optional<FindCompanyAreaByOrgIdsOutDto> first = companyAreaByOrgIdsOutDtoList.stream().filter(dto -> dto.getOrgId()
                            .equals(deviseSchemeVO.getCompanyId())).findFirst();
                    first.ifPresent(findCompanyAreaByOrgIdsOutDto -> deviseSchemeVO.setAreaName(findCompanyAreaByOrgIdsOutDto.getValueName()));
                }

                if(dealCluesCountDTOS != null && dealCluesCountDTOS.size() > 0){
                    DealCluesCountDTO dealCluesCountDTO = dealCluesCountDTOS.parallelStream().filter(x -> x.getSchemeId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if(dealCluesCountDTO != null ){
                        deviseSchemeVO.setExplainDealCluesCount(dealCluesCountDTO.getDealCluesCount());
                    }
                }

                if (labelMappingDTOS != null) {
                    List<DeviseSchemeLabelMappingDTO> signalList = groupedMap.get(deviseSchemeVO.getId());
                    if (signalList != null && signalList.size() > 0) {
                        String label = signalList
                                .stream().filter(x -> StringUtils.isNotBlank(x.getName()))
                                .map(DeviseSchemeLabelMappingDTO::getName)
                                .collect(Collectors.joining(","));
                        deviseSchemeVO.setLabelNames(label);
                    }
                }
                if(storeMappingList != null){
                    List<DeviseSchemeStoreMapping> storeMappingList1 = groupStoreMap.get(deviseSchemeVO.getId());
                    if(storeMappingList1 != null && storeMappingList1.size() > 0){
                        String storeName = storeMappingList1.stream().map( x-> x.getStoreCode() + "/" + x.getStoreName()).collect(Collectors.joining(","));
                        String shortValue = setLongStringValue(storeName);
                        deviseSchemeVO.setStoreName(shortValue);
                    }
                }
                if(goodsMappingList != null){
                    List<DeviseSchemeGoodsMapping> goodsMappingList1 = groupGoodsMap.get(deviseSchemeVO.getId());
                    if(goodsMappingList1 != null && goodsMappingList1.size() > 0){
                        String goodsName = goodsMappingList1.stream().map( x-> x.getGoodsCode() + "/" + x.getGoodsName()).collect(Collectors.joining(","));
                        deviseSchemeVO.setGoodsName(goodsName);
                    }
                }
                if(CollectionUtils.isNotEmpty(createUerDTOList)){
                    CreateUerDTO createUerDTO = createUerDTOList.parallelStream().filter(x -> x.getCreatedBy().equals(deviseSchemeVO.getCreatedBy())).findFirst().orElse(null);
                    if(createUerDTO != null){
                        deviseSchemeVO.setCreateStoreOrgId(createUerDTO.getCreateStoreOrgId());
                        deviseSchemeVO.setCreateStoreCode(createUerDTO.getCreateStoreCode());
                        deviseSchemeVO.setCreateStoreName(createUerDTO.getCreateStoreName());
                        deviseSchemeVO.setCreateAbbreviation(createUerDTO.getCreateAbbreviation());
                        deviseSchemeVO.setCreateChargeUserId(deviseSchemeVO.getCreatedChargeUserId());
                        deviseSchemeVO.setCreateChargeCode(deviseSchemeVO.getCreatedChargeCode());
                        deviseSchemeVO.setCreateChargeUserName(deviseSchemeVO.getCreatedChargeUserName());
                        deviseSchemeVO.setCreateChargePhone(deviseSchemeVO.getCreatedChargePhone());
                    }
                }
                if (StringUtils.isNotBlank(deviseSchemeVO.getProvinceName()) && StringUtils.isNotBlank(deviseSchemeVO.getCityName()) && StringUtils.isNotBlank(deviseSchemeVO.getCountyName())) {
                    deviseSchemeVO.setProvinceName(deviseSchemeVO.getProvinceName() + "-" + deviseSchemeVO.getCityName() + "-" +  deviseSchemeVO.getCountyName());
                }else {
                    deviseSchemeVO.setProvinceName("/");
                }

                if(StringUtils.isBlank(deviseSchemeVO.getVillageName())){
                    deviseSchemeVO.setVillageName("/");
                }

                if(deviseSchemeVO.getType() == 1){
                    deviseSchemeVO.setTypeName("场景化通用方案");
                }else if(deviseSchemeVO.getType() == 2){
                    deviseSchemeVO.setTypeName("小区通用方案");
                }else {
                    deviseSchemeVO.setTypeName("顾客个性化方案");
                }

                if (StringUtils.isNotBlank(deviseSchemeVO.getCluesCustomerName())) {
                    if (deviseSchemeVO.getCluesCustomerName().length() == 1) {
                        deviseSchemeVO.setCluesCustomerName(deviseSchemeVO.getCluesCustomerName() + "***" + deviseSchemeVO.getCluesCustomerName());
                    } else {
                        String firstName = deviseSchemeVO.getCluesCustomerName().substring(0, 1);
                        String lastName = deviseSchemeVO.getCluesCustomerName().substring(deviseSchemeVO.getCluesCustomerName().length() - 1);
                        deviseSchemeVO.setCluesCustomerName(firstName + "***" + lastName);
                    }
                }

                if(StringUtils.isNotBlank(deviseSchemeVO.getCluesCustomerPhone())){
                    deviseSchemeVO.setCluesCustomerPhone(setCostomerPhone(deviseSchemeVO.getCluesCustomerPhone()));
                }

                if(deviseSchemeVO.getCluesId() != null){
                    deviseSchemeVO.setRelationClues(deviseSchemeVO.getCluesId() + "/"+ deviseSchemeVO.getCluesCustomerName() + "/" + deviseSchemeVO.getCluesCustomerPhone());
                }else {
                    deviseSchemeVO.setRelationClues("/");
                }

                if(deviseSchemeVO.getStatus() == 0){
                    deviseSchemeVO.setStatusName("禁用");
                }else {
                    deviseSchemeVO.setStatusName("启用");
                }

                if(deviseSchemeVO.getExplainCount() == null){
                    deviseSchemeVO.setExplainCount(0);
                }

                if(deviseSchemeVO.getExplainCluesCount() == null){
                    deviseSchemeVO.setExplainCluesCount(0);
                }

                if(deviseSchemeVO.getExplainDealCluesCount() == null){
                    deviseSchemeVO.setExplainDealCluesCount(0);
                }

                if(deviseSchemeVO.getOrderAmount() == null){
                    deviseSchemeVO.setOrderAmount(BigDecimal.ZERO);
                }

                if(deviseSchemeVO.getDurationTimes() == null){
                    deviseSchemeVO.setDurationTimes("0");
                }

                if(CollectionUtils.isNotEmpty(threeStatisticalList)){
                    //设置3D方案浏览-内容工场，3D方案点赞/收藏，3D方案发送
                    //10：浏览，20：收藏，30：点赞，50：发送
                    SchemeMaterialStatisticalDTO threeView = threeStatisticalList.parallelStream().filter(x -> x.getEventType().equals(10) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if(threeView != null && threeView.getEventCount() != null){
                        deviseSchemeVO.setThreeViewCount(threeView.getEventCount());
                    }
//                    SchemeMaterialStatisticalDTO threeThumbs = threeStatisticalList.parallelStream().filter(x -> x.getEventType().equals(30) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(threeThumbs != null && threeThumbs.getEventCount() != null){
//                        deviseSchemeVO.setThreeThumbsCount(threeThumbs.getEventCount());
//                    }
//                    SchemeMaterialStatisticalDTO threeCollect = threeStatisticalList.parallelStream().filter(x -> x.getEventType().equals(20) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(threeCollect != null && threeCollect.getEventCount() != null){
//                        deviseSchemeVO.setThreeCollectCount(threeCollect.getEventCount());
//                    }
                    SchemeMaterialStatisticalDTO threeSend = threeStatisticalList.parallelStream().filter(x -> x.getEventType().equals(50) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if(threeSend != null && threeSend.getEventCount() != null){
                        deviseSchemeVO.setThreeSendCount(threeSend.getEventCount());
                    }
                }

//                if(CollectionUtils.isNotEmpty(commonStatisticalList)){
//                    //设置3D方案浏览-内容工场，3D方案点赞/收藏，3D方案发送
//                    //10：浏览，20：收藏，30：点赞，50：发送
//                    SchemeMaterialStatisticalDTO commonView = commonStatisticalList.parallelStream().filter(x -> x.getEventType().equals(10) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(commonView != null && commonView.getEventCount() != null){
//                        deviseSchemeVO.setCommonViewCount(commonView.getEventCount());
//                    }
//                    SchemeMaterialStatisticalDTO commonThumbs = commonStatisticalList.parallelStream().filter(x -> x.getEventType().equals(30) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(commonThumbs != null && commonThumbs.getEventCount() != null){
//                        deviseSchemeVO.setCommonThumbsCount(commonThumbs.getEventCount());
//                    }
//                    SchemeMaterialStatisticalDTO commonCollect = commonStatisticalList.parallelStream().filter(x -> x.getEventType().equals(20) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(commonCollect != null && commonCollect.getEventCount() != null){
//                        deviseSchemeVO.setCommonCollectCount(commonCollect.getEventCount());
//                    }
//                    SchemeMaterialStatisticalDTO commonSend = commonStatisticalList.parallelStream().filter(x -> x.getEventType().equals(50) && x.getRelationParentId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if(commonSend != null && commonSend.getEventCount() != null){
//                        deviseSchemeVO.setCommonSendCount(commonSend.getEventCount());
//                    }
//                }

                if(CollectionUtils.isNotEmpty(helpFulList)){
                    SchemeCaseStatisticalDTO threeThumbs = helpFulList.parallelStream().filter(x -> x.getCaseType().equals(5) && x.getCaseId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if (threeThumbs != null && threeThumbs.getCaseCount() != null) {
                        deviseSchemeVO.setThreeThumbsCount(threeThumbs.getCaseCount());
                    }
//                    SchemeCaseStatisticalDTO commonThumbs = helpFulList.parallelStream().filter(x -> x.getCaseType().equals(6) && x.getCaseId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if (commonThumbs != null && commonThumbs.getCaseCount() != null) {
//                        deviseSchemeVO.setCommonThumbsCount(commonThumbs.getCaseCount());
//                    }
                }

                if(CollectionUtils.isNotEmpty(collectList)){
                    SchemeCaseStatisticalDTO threeCollect = collectList.parallelStream().filter(x -> x.getCaseType().equals(5) && x.getCaseId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
                    if (threeCollect != null && threeCollect.getCaseCount() != null) {
                        deviseSchemeVO.setThreeCollectCount(threeCollect.getCaseCount());
                    }
//                    SchemeCaseStatisticalDTO commonCollect = collectList.parallelStream().filter(x -> x.getCaseType().equals(6) && x.getCaseId().equals(deviseSchemeVO.getId())).findFirst().orElse(null);
//                    if (commonCollect != null && commonCollect.getCaseCount() != null) {
//                        deviseSchemeVO.setCommonCollectCount(commonCollect.getCaseCount());
//                    }
                }
                deviseSchemeVO.setPlanName("/");
                if (CollectionUtils.isNotEmpty(kujialeMappingList)){
                    Optional<DeviseSchemeKujialeMapping> first = kujialeMappingList.stream().filter(k -> deviseSchemeVO.getId().equals(k.getSchemeId())).findFirst();
                    if (first.isPresent()){
                        deviseSchemeVO.setPlanId(first.get().getPlanId());
                        deviseSchemeVO.setPlanName(first.get().getPlanName());
                    }
                }

                if (!chargeStoreMap.isEmpty()&&chargeStoreMap.containsKey(deviseSchemeVO.getCreatedBy())){
                    deviseSchemeVO.setStoreChannelName(chargeStoreMap.get(deviseSchemeVO.getCreatedBy()).getStoreChannelName());
                    if(StringUtils.isNotBlank(chargeStoreMap.get(deviseSchemeVO.getCreatedBy()).getStoreChannelCode())){
                        try {
                            String subChannelName = Optional.of(storeInfos.get(0).getStoreChannelCode())
                                    .filter(StringUtils::isNotBlank)
                                    .map(workflowClient::getSubChannelCodeList)
                                    .filter(Result::getSuccess)
                                    .map(Result::getData)
                                    .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                                    .map(l -> l.stream().filter(d -> StringUtils.equals(d.getValueCode(), storeInfos.get(0).getStoreSubChannelCode())).findFirst())
                                    .filter(Optional::isPresent)
                                    .map(Optional::get)
                                    .map(Dict::getValueName)
                                    .orElse("缺省");
                            deviseSchemeVO.setStoreSubChannelName(subChannelName);
                        }catch (Exception e){
                            log.error("查询StoreSubChannelCode报错",e);
                        }
                    }
                }
            }
        }

        log.info("场景化导出定时任务上传开始时间："+System.currentTimeMillis());
        //生成Excel，并上传oss
        String fileName = "", sheetName = "方案列表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //写入excel文件流
        try {
            List<ExportDeviseSchemeVO> exportDtos = SchemeMapper.INSTANCE.detailToExcelDto(resultList);
            EasyExcel.write(os, ExportDeviseSchemeVO.class).sheet(sheetName).doWrite(exportDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
            log.info("场景化导出定时任务上传结束时间："+System.currentTimeMillis());
        } catch (Exception e) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            log.error(e.toString());
            dataClientService.failureTask(exportTask);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 根据业务员id获取业务员标签
     *
     * @param salesmanIds 业务员id
     * @return 业务员标签
     */
    private Map<Long, String> querySalesmanLabel(List<Long> salesmanIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(salesmanIds)) {
            return result;
        }

        //增加标签返回
        List<SalesmanLabel> salesmanLabelList = deviseSchemeMainInfoMapper.querySalesmanLabel(salesmanIds);
        if (CollectionUtils.isNotEmpty(salesmanLabelList)) {
            for (SalesmanLabel salesmanLabel : salesmanLabelList) {
                Long salesmanId = Long.valueOf(salesmanLabel.getSalesmanId());
                if (result.containsKey(salesmanId)) {
                    if (StringUtils.isNotBlank(salesmanLabel.getLabelName())) {
                        result.compute(salesmanId, (k, value) -> value + "，" + salesmanLabel.getLabelName());
                    }
                } else {
                    if (StringUtils.isNotBlank(salesmanLabel.getLabelName())) {
                        result.put(salesmanId, salesmanLabel.getLabelName());
                    }
                }
            }
        }

        return result;
    }

    /**
     * 根据业务员id获取业务员岗位
     */
    private Map<Long, String> querySalesmanStation(List<Long> salesmanIds) {

        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(salesmanIds)) {
            return result;
        }
        List<KvLs> data = deviseSchemeMainInfoMapper.findSalesmanStation(salesmanIds);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(x -> result.put(x.getKey(), x.getValue()));
        }
        return result;
    }


    /**
     * 根据能力标签获取业务员id
     */
    private List<Long> getSalesmanIdsByLabelValue(List<String> salesmanLabelValueList) {
        List<Long> salesmanIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(salesmanLabelValueList)) {
            return salesmanIds;
        }

        return deviseSchemeMainInfoMapper.querySalesmanIdByLabelValue(salesmanLabelValueList);
    }

    private List<DeviseSchemeKujialeMapping> getKujialeListBySchemeIds(List<Long> ids) {
        List<DeviseSchemeKujialeMapping> kujialeMappingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<DeviseSchemeKujialeMapping> kujialeMappings =  deviseSchemeKujialeMappingDao.selectListBySchemeIds(idList);
                kujialeMappingList.addAll(kujialeMappings);
            }
        }
        return kujialeMappingList;
    }

    private List<DealCluesCountDTO> selectDealCluesCountBySchemeIds(List<Long> ids) {
        List<DealCluesCountDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<DealCluesCountDTO> dealCluesCountDTOS = deviseSchemeExplainRecordMapper.selectDealCluesCountBySchemeIds(idList);
                list.addAll(dealCluesCountDTOS);
            }
        }
        return list;
    }

    private List<ExplainRecordDTO> getExplainListBySchemeIds(List<Long> ids) {
        List<ExplainRecordDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<ExplainRecordDTO> explainRecordDTOS = deviseSchemeExplainRecordMapper.selectListBySchemeIds(idList);
                list.addAll(explainRecordDTOS);
            }
        }
        return list;
    }

    private List<SchemeCaseStatisticalDTO> getSchemeCaseCollect(List<Long> ids) {
        List<SchemeCaseStatisticalDTO> collectList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<SchemeCaseStatisticalDTO> collectDtos = contentCaseUserCollectDao.getSchemeCaseCollect(idList);
                collectList.addAll(collectDtos);
            }
        }
        return collectList;
    }

    private List<SchemeCaseStatisticalDTO> getSchemeCaseHelpful(List<Long> ids) {
        List<SchemeCaseStatisticalDTO> helpFulList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<SchemeCaseStatisticalDTO> helpFulDtos= contentCaseUserHelpfulDao.getSchemeCaseHelpful(idList);
                helpFulList.addAll(helpFulDtos);
            }
        }
        return helpFulList;
    }

    private List<SchemeMaterialStatisticalDTO> getSchemeMaterialStatistical(List<Long> ids) {
        List<SchemeMaterialStatisticalDTO> threeStatisticalList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<SchemeMaterialStatisticalDTO> threeStatisticalDtos = materialEventLogMapper.getSchemeMaterialStatistical(5,idList);
                threeStatisticalList.addAll(threeStatisticalDtos);
            }
        }
        return threeStatisticalList;
    }

    private List<CluesCustomerInfoForSchemeDTO> queryCluesCustomerIndoByCluesIdsOpen(List<Long> ids) {
        List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                try {
                    List<Long> clueIds = (List<Long>) list1;
                    List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOS = salesLeadsService.queryCluesCustomerIndoByCluesIdsOpen(clueIds);
                    cluesCustomerInfoForSchemeDTOList.addAll(cluesCustomerInfoForSchemeDTOS);
                } catch (Exception e) {
                    log.error("marketingClient.queryCluesCustomerIndoByCluesIdsOpen：{}"+e);
                }
            }
        }
        return cluesCustomerInfoForSchemeDTOList;
    }

    private List<DeviseSchemeGoodsMapping> selectListBySchemeIds(List<Long> ids) {
        List<DeviseSchemeGoodsMapping> goodsMappingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<DeviseSchemeGoodsMapping> companyDtos =  deviseSchemeGoodsMappingMapper.selectListBySchemeIds(idList);
                goodsMappingList.addAll(companyDtos);
            }
        }
        return goodsMappingList;
    }

    private List<FindCompanyAreaByOrgIdsOutDto> findCompanyByOrgIdList(List<Long> ids) {
        List<FindCompanyAreaByOrgIdsOutDto> companyAreaList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                try {
                    List<Long> companyIds = (List<Long>) list1;
                    FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
                    findCompanyByIdsInDto.setIdList(companyIds);
                    List<FindCompanyAreaByOrgIdsOutDto> companyDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
                    companyAreaList.addAll(companyDtos);
                } catch (Exception e) {
                    log.error("orgClient.findCompanyByOrgIdList：{}"+e);
                }
            }
        }
        return companyAreaList;
    }

    private List<DeviseSchemeStoreMapping> getStoreListBySchemeIds(List<Long> ids) {
        List<DeviseSchemeStoreMapping> storeMappingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<DeviseSchemeStoreMapping> storeMappings= deviseSchemeStoreMappingMapper.selectListBySchemeIds(idList);
                storeMappingList.addAll(storeMappings);
            }
        }
        return storeMappingList;
    }

    private List<DeviseSchemeLabelMappingDTO> getLabelMappingDTOS(List<Long> ids) {
        List<DeviseSchemeLabelMappingDTO> labelMappingDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<DeviseSchemeLabelMappingDTO> labelMappingList = deviseSchemeLabelMappingMapper.selectPropertyListBySchemeId(idList);
                labelMappingDTOS.addAll(labelMappingList);
            }
        }
        return labelMappingDTOS;
    }


    private List<CreateUerDTO> setCreateUserInfo(List<String> userIds){
        //去重
        HashSet userIdSet = new HashSet(userIds);
        List<String> userIdArray = new ArrayList<>(userIdSet);
        List<CreateUerDTO> createUerDTOList = new ArrayList<>();
//        List<UserEntityExtend> userEntityExtendList = userEntityClient.findUserEntityExtendByUserIdsPost(userIdArray).getData();
        List<UserEntityExtend> userEntityExtendList = findUserEntityExtendByUserIdsPost(userIdArray);
        if (CollectionUtils.isNotEmpty(userEntityExtendList)) {
            Set<Long> salesManIdSet = userEntityExtendList.parallelStream().filter(p -> p.getSalesmanId() != null).map(UserEntityExtend::getSalesmanId).collect(Collectors.toSet());
            List<Long> salesManIds = new ArrayList<>(salesManIdSet);
            if (CollectionUtils.isNotEmpty(salesManIds)) {
//                List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(salesManIds).getData();
                List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = getSalesmanInfoByIdsForQYWX(salesManIds);
                if (CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)) {
                    CreateUerDTO createUerDTO = new CreateUerDTO();
                    for (UserEntityExtend userEntityExtend : userEntityExtendList) {
                        createUerDTO = new CreateUerDTO();
                        createUerDTO.setCreatedBy(userEntityExtend.getUserEntityId());
                        SalesmanInfoForQYWX salesmanInfoForQYWX = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(userEntityExtend.getSalesmanId())).findFirst().orElse(null);
                        if (salesmanInfoForQYWX != null) {
                            createUerDTO.setCreateStoreOrgId(salesmanInfoForQYWX.getStoreId());
                            createUerDTO.setCreateStoreCode(salesmanInfoForQYWX.getStoreCode());
                            createUerDTO.setCreateStoreName(salesmanInfoForQYWX.getStoreName());
                            createUerDTO.setCreateAbbreviation(salesmanInfoForQYWX.getAbbreviation());
                            createUerDTO.setCreateChargeUserId(salesmanInfoForQYWX.getId());
                            createUerDTO.setCreateChargeCode(salesmanInfoForQYWX.getCode());
                            createUerDTO.setCreateChargeUserName(salesmanInfoForQYWX.getName());
                            createUerDTO.setCreateChargePhone(salesmanInfoForQYWX.getPhone());
                        }
                        createUerDTOList.add(createUerDTO);
                    }
                }
            }
        }
        return createUerDTOList;
    }

    private List<SalesmanInfoForQYWX> getSalesmanInfoByIdsForQYWX(List<Long> ids) {
        List<SalesmanInfoForQYWX> salesmanInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                try {
                    List<Long> salesManIds = (List<Long>) list1;
                    List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(salesManIds).getData();
                    salesmanInfoList.addAll(salesmanInfoForQYWXList);
                } catch (Exception e) {
                    log.error("orgClient.getSalesmanInfoByIdsForQYWX：{}"+e);
                }
            }
        }
        return salesmanInfoList;
    }

    private List<UserEntityExtend> findUserEntityExtendByUserIdsPost(List<String> ids) {
        List<UserEntityExtend> userEntityExtends = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                try {
                    List<String> userIdArray = (List<String>) list1;
                    List<UserEntityExtend> userEntityExtendList = userEntityClient.findUserEntityExtendByUserIdsPost(userIdArray).getData();
                    userEntityExtends.addAll(userEntityExtendList);
                } catch (Exception e) {
                    log.error("userEntityClient.findUserEntityExtendByUserIdsPost：{}"+e);
                }
            }
        }
        return userEntityExtends;
    }


    public static String changeTimeSecond2Minute(Integer second) {
        Integer seconds = Integer.valueOf(second);
        BigDecimal a = new BigDecimal(seconds);
        BigDecimal b = new BigDecimal(60);
        String min =  ((a).divide(b,2,BigDecimal.ROUND_HALF_UP )).toString();
        return min;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4);
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4);
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }

    public String setLongStringValue(String longValue){
        String shortValue = "";
        if(StringUtils.isNotBlank(longValue) && longValue.length() > 20000){
            shortValue = longValue.substring(0,20000) + "...";
            return shortValue;
        } else {
            return longValue;
        }
    }




}
