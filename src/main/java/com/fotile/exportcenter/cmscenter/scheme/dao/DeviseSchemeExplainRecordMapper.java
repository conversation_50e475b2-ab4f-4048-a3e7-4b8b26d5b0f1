package com.fotile.exportcenter.cmscenter.scheme.dao;


import com.fotile.exportcenter.client.pojo.org.SalesmanLabel;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.DealCluesCountDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.ExplainRecordDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.GetSchemeExplainRecordListDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO;
import com.fotile.exportcenter.common.KvLs;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲解记录表 DeviseSchemeExplainRecordMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2022-12-21 16:11:27
 */
public interface DeviseSchemeExplainRecordMapper {


    List<ExplainRecordDTO> selectListBySchemeIds(@Param("list") List<Long> schemeIds);

    List<DealCluesCountDTO> selectDealCluesCountBySchemeIds(@Param("list") List<Long> schemeIds);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<DeviseSchemeExplainRecordVO> findRecordListForWeb(GetSchemeExplainRecordListDTO getSchemeListDTO);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<StoreInfoVO> getStoreInfo(@Param("storeOrgIdList") List<Long> storeOrgIdList);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<KvLs> querySalesmanStation(@Param("salesmanIds") List<Long> salesmanIds);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SalesmanLabel> querySalesmanLabel(@Param("salesmanIds") List<Long> salesmanIds);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<Long> querySalesmanIdByLabelValue(@Param("labelValueList") List<String> labelValueList);
}