package com.fotile.exportcenter.cmscenter.scheme.mapper;


import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */

@Mapper(componentModel = "spring")
public interface SchemeMapper {

    SchemeMapper INSTANCE = Mappers.getMapper(SchemeMapper.class);


    List<ExportDeviseSchemeVO> detailToExcelDto(List<DeviseSchemeVO> likeDetails);


    List<ExportDeviseSchemeExplainRecordVO> explainRecordToExcelDto(List<DeviseSchemeExplainRecordVO> resultList);
}
