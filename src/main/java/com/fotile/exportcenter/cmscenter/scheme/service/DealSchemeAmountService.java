package com.fotile.exportcenter.cmscenter.scheme.service;


import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.cmscenter.scheme.mapper.ExportContext;
import com.fotile.exportcenter.marketing.mq.CluesExportChannel;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
@Slf4j
public class DealSchemeAmountService {

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    ExportContext exportContext;


    @StreamListener(CluesExportChannel.CMS_EXPORT_INPUT)
    public void schemeExport(Message<ExportTaskRecord> message) {
        ExportTaskRecord exportTaskRecord = message.getPayload();
        if(exportTaskRecord == null || exportTaskRecord.getId() == null){
            return;
        }

        //调用开始任务接口
        Integer result = dataClientService.startTask(exportTaskRecord).getData();
        if(result != 1){
            //不等于1，则说明任务状态不是未进行，直接return
            return;
        }
        //调用查询任务接口，判断状态是否可以进行下载
        try {
            //根据类型调用不同的方法生产不同的数据
            exportContext.getExportImpl(exportTaskRecord.getType()).exportFile(exportTaskRecord);
        } catch (Exception e) {
            log.error("错误数据" + exportTaskRecord);
            log.error(String.format("导出错误原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason("error:"+e.getMessage());
            dataClientService.failureTask(exportTask);
        }
        return;
    }


}
