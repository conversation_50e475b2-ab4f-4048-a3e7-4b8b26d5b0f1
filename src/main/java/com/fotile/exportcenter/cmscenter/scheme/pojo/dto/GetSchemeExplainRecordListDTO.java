package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class GetSchemeExplainRecordListDTO implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    private Long offSize;


    /**
     * 方案编码
     */
    private String projectCode;

    /**
     * 方案标题
     */
    private String projectName;

    /**
     * 1:通用,2:小区通用,3:个性化
     */
    private Integer type;

    /**
     * 是否优秀方案
     */
    private Integer excellentFlag;

    /**
     * 讲解人
     */
    private List<Long> chargeUserIds;

    /**
     * 讲解人岗位id
     */
    private List<Long> chargeUserStationList;

    /**
     * 讲解人能力标签,最终转化为chargeUserIds条件
     *
     */
    private List<String> chargeUserLabelValueList;

    /**
     * 大区
     */
    private List<Long> areas;


    /**
     * 分公司
     */
    private List<Long> companyIds;

    /**
     * 门店
     */
    private List<Long> storeOrgIds;

    private List<Long> ids;

//    /**
//     * 门店标签集合
//     */
//    private String storeLabels;
//
//    /**
//     * 门店标签集合
//     */
//    private String storeKeywords;

    /**
     * 门店标签集合
     */
    private List<String> keyWordSet;

    /**
     *门店关键字
     */
    private List<String> realKeyWordsList;

    /**
     * 是否关联线索
     */
    private Integer relationCluesFlag;

    /**
     * 讲解平台
     */
    private Integer source;

    /**
     * 单词讲解时长开始
     */
    private BigDecimal explainTimesStart;

    /**
     * 单词讲解时长结束
     */
    private BigDecimal explainTimesEnd;


    /**
     * 讲解开始时间
     */
    private Date explainStartDate;

    /**
     * 讲解结束时间
     */
    private Date explainEndDate;


    private List<Long> authCompanyIds;

    private List<Long> companyArea;

    private List<Long> authStoreOrgIds;


    private List<Long> storeByConditionsList;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 查询条数
     */
    private Integer totalCount;

    /**
     * 开始条数
     */
    private Integer startCount;

    /**
     * 讲解线索id
     */
    private Long explainCluesId;

    /**
     * 门店渠道
     */
    private List<String> storeChannelCodeList;
    /**
     * 门店渠道细分
     */
    private List<String> storeChannelSubdivisionCodeList;

    private Integer storeChannelSubDivisionCodeFlag;
}
