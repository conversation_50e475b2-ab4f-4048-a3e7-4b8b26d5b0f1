package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.*;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * <AUTHOR>
 * @date 2023/9/9 11:18
 * 企微内容操作事件记录表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("t_material_event_log")
public class MaterialEventLog extends AuditingEntity {

    /**
     * 事件类型：10：访问页面，20：收藏，30：点赞，40：点踩，50：发送，60：树节点切换
     */
    @TableField(value = "event_type")
    private Integer eventType;

    /**
     * 树内容组id：当事件类型是20，30，40，50时不能为空
     */
    @TableField(value = "material_id")
    private Integer materialId;

    /**
     * 聊天素材树id，事件类型为60时不能为空
     */
    @TableField(value = "tree_id")
    private Integer treeId;

    /**
     * 聊天素材树节点id，事件类型为60时不能为空
     */
    @TableField(value = "node_id")
    private Long nodeId;

    /**
     * 登陆人账号
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 登陆人拼音
     */
    @TableField(value = "user_name")
    @FieldEncrypt
    private String userName;

    /**
     * 登陆人中文名称
     */
    @TableField(value = "first_name")
    @FieldEncrypt
    private String firstName;

    /**
     * 大区编码
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 大区名
     */
    @TableField(value = "area_name")
    private String areaName;

    /**
     * 分公司id
     */
    @TableField(value = "company_id")
    private Long companyId;

    /**
     * 分公司名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 门店id
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 门店编码
     */
    @TableField(value = "store_code")
    private String storeCode;

    /**
     * 门店名称
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 业务员id
     */
    @TableField(value = "charge_user_id")
    private Long chargeUserId;

    /**
     * 业务员名称
     */
    @TableField(value = "charge_user_name")
    @FieldEncrypt
    private String chargeUserName;

    /**
     * 业务员编码
     */
    @TableField(value = "charge_code")
    private String chargeCode;

    /**
     * 业务员手机号
     */
    @TableField(value = "charge_phone")
    @FieldEncrypt
    private String chargePhone;

    /**
     * 岗位:1:客户经理；2：厨电顾问，同业务员边存取一致
     */
    @TableField(value = "station")
    private Integer station;

    /**
     * 发送对象userid
     */
    @TableField(value = "object_user_id")
    private String objectUserId;

    /**
     * 业务类型：1-内容工场 2-找案例 3-找产品
     */
    @TableField(value = "biz_type")
    private Integer bizType;

    /**
     * 案例id或商品编码
     */
    @TableField(value = "content_code")
    private String contentCode;

    /**
     * 案例名称或商品名称
     */
    @TableField(value = "content_name")
    private String contentName;
    /**
     * 关联数据类型，4:内容专题
     */
    @TableField(value = "relation_source_type")
    private Integer relationSourceType;
    /**
     * 关联父id
     */
    @TableField(value = "relation_parent_id")
    private Long relationParentId;

    /**
     * 关联父id
     */
    @TableField(value = "biz_detail_type")
    private Integer bizDetailType;
}
