package com.fotile.exportcenter.cmscenter.scheme.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DeviseSchemeExplainRecordVO implements Serializable {

    @ApiModelProperty(value = "院子id")
    private Long id;

    @ApiModelProperty(value = "是否删除：0：否；其它：是")
    private Long isDeleted;

    @ApiModelProperty(value = "创建者")
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;

    @ApiModelProperty(value = "修改者")
    private String modifiedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date modifiedDate;

    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ApiModelProperty(value = "1:通用,2:小区通用,3:个性化")
    private Integer type;

    @ApiModelProperty(value = "分公司orgid")
    private Long companyId;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "门店orgid")
    private Long storeOrgId;

    @ApiModelProperty(value = "门店code")
    private String storeCode;

    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ApiModelProperty(value = "门店简称")
    private String abbreviation;

    @ApiModelProperty(value = "省id")
    private Long provinceId;

    @ApiModelProperty(value = "省")
    private String provinceName;

    @ApiModelProperty(value = "市id")
    private Long cityId;

    @ApiModelProperty(value = "市")
    private String cityName;

    @ApiModelProperty(value = "县/区id")
    private Long countyId;

    @ApiModelProperty(value = "县/区")
    private String countyName;

    @ApiModelProperty(value = "小区id")
    private Long villageId;

    @ApiModelProperty(value = "小区")
    private String villageName;

    @ApiModelProperty(value = "线索id")
    private Long cluesId;

    @ApiModelProperty(value = "0禁用，1启用")
    private Integer status;

    @ApiModelProperty(value = "附件链接")
    private String projectUrl;

    @ApiModelProperty(value = "备注")
    private String remark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "最新修改时间")
    private Date reviseTime;

    @ApiModelProperty(value = "最新修改人名称")
    @FieldEncrypt
    private String reviseName;
    @ApiModelProperty(value = "创建人名称")
    @FieldEncrypt
    private String createdCode;
    @ApiModelProperty(value = "创建人名称")
    @FieldEncrypt
    private String createdName;

    @ApiModelProperty(value = "讲解账号")
    @FieldEncrypt
    private String createdName1;

    @ApiModelProperty(value = "线索名称")
    private String cluesCustomerName;

    @ApiModelProperty(value = "线索手机号")
    private String cluesCustomerPhone;

    private String labelNames;

    private Integer explainCount;

    private String durationTimes;

    private Integer durationTime;
    private String singleDurationTime;

    private Long schemeId;

    private Date startTime;

    private Date endTime;

    @ApiModelProperty(value = "提交人业务员id")
    private Long chargeUserId;
    @ApiModelProperty(value = "提交人业务员名称")
    @FieldEncrypt
    private String chargeUserCode;
    @ApiModelProperty(value = "提交人业务员名称")
    @FieldEncrypt
    private String chargeUserName;

    @ApiModelProperty(value = "提交人业务员编码")
    private String chargeCode;

    @ApiModelProperty(value = "提交人业务员手机号")
    @FieldEncrypt
    private String chargePhone;

    /**
     * 讲解人岗位
     */
    private String chargeUserStationName;

    /**
     * 讲解人能力标签
     */
    private String chargeUserLabelValue;

    private Integer excellentFlag;

    private Integer source;

    /**
     * 讲解线索id
     */
    private Long explainCluesId;

    /**
     * 订单成交状态：1.成交  0无
     */
    private String orderStatus;


    private BigDecimal orderAmount;

    private BigDecimal detailOrderAmount;

    /**
     * 大区名称
     */
    private String regionName;

    private String typeName;

    private String sourceName;

    private String relationClues;

    private String statusName;

    private String createdCompanyName;

    /**
     * 关联线索时间
     */
    private Date relatedCluesTime;


    /**
     * 异常结束类型，0正常结束，1定时任务异常结束(播放任务中断自动结束讲解)，2长时间前端调用结束(长时间未操作自动结束讲解)
     */
    private Integer abnormalEndType;


    private String abnormalEndTypeName;


    /**
     * 关联线索标志
     */
    private Integer needCluesFlag;


    /**
     * 无关联线索类型
     */
    private Integer noCluesType;

    /**
     * 无关联操作时间
     */
    private Date noCluesTime;


    /**
     * 无关联操作任
     */
    private String noCluesOperator;

    /**
     * 讲解线索
     */
    private String explainCluesResult;

    /**
     * 是否是我自己的讲解记录:0否1是
     */
    private Integer myselfRecordFlag;
    /**
     * 门店渠道
     */
    private String storeChannelName;
    /**
     * 门店渠道细分
     */
    private String storeSubChannelName;
}
