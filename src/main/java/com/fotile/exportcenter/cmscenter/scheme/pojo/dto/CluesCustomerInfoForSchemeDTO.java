package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CluesCustomerInfoForSchemeDTO implements Serializable {

    private Long id;

    @ApiModelProperty(value = "顾客名称 -- 门店运营中获取")
    @FieldEncrypt
    private String customerName;

    @ApiModelProperty(value = "顾客电话 -- 门店运营中获取")
    @FieldEncrypt
    private String customerPhone;

    /**
     * 订单成交状态：1.成交  0无
     */
    private String orderStatus;


}
