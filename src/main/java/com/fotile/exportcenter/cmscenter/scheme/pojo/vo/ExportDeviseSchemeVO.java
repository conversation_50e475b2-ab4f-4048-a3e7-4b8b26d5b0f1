package com.fotile.exportcenter.cmscenter.scheme.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExportDeviseSchemeVO implements Serializable {

    @ColumnWidth(10)
    @ExcelProperty(value = {"方案编码"})
    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案标题"})
    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"省市区"})
    @ApiModelProperty(value = "省")
    private String provinceName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"小区名称"})
    @ApiModelProperty(value = "小区")
    private String villageName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"关联酷家乐方案"})
    @ApiModelProperty(value = "关联酷家乐方案")
    private String planName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案类型"})
    private String typeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案标签"})
    private String labelNames;

    @ColumnWidth(30)
    @ExcelProperty(value = {"推荐产品"})
    private String goodsName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"关联线索"})
    private String relationClues;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解次数"})
    private Integer explainCount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解时长（分）"})
    private String durationTimes;

    /**
     * 讲解线索数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索数"})
    private Integer explainCluesCount;

    /**
     * 讲解成交线索数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解成交线索数"})
    private Integer explainDealCluesCount;

    /**
     * 成交金额
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"成交金额"})
    private BigDecimal orderAmount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"状态"})
    private String statusName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty(value = "大区名称")
    private String areaName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"所属门店"})
    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人账号"})
    @ApiModelProperty(value = "创建人名称")
    @FieldEncrypt
    private String createdName;

    /**
     * 创建人业务员手机号
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人手机号"})
    @ApiModelProperty(value = "创建人手机号")
    private String createdChargePhone;


    /**
     * 提交人业务员编码
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人业务员编码"})
    @ApiModelProperty(value = "创建人业务员编码")
    private String createChargeCode;



    /**
     * 提交人业务员名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人业务员名称"})
    @ApiModelProperty(value = "创建人业务员名称")
    private String createChargeUserName;


    /**
     * 创建人岗位
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人岗位"})
    @ApiModelProperty(value = "创建人岗位")
    private String createChargeStationName;

    /**
     * 创建人能力标签
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人能力标签"})
    @ApiModelProperty(value = "创建人能力标签")
    private String createChargeLabelValue;


    @ColumnWidth(30)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty(value = "门店渠道")
    private String storeChannelName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty(value = "门店渠道细分")
    private String storeSubChannelName;

    /**
     * 门店code
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人门店编码"})
    @ApiModelProperty(value = "创建人门店code")
    private String createStoreCode;

    /**
     * 门店name
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人门店名称"})
    @ApiModelProperty(value = "创建人门店name")
    private String createStoreName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"创建时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;

    @ColumnWidth(30)
    @ExcelProperty(value = {"最新编辑人"})
    @ApiModelProperty(value = "最新修改人名称")
    @FieldEncrypt
    private String reviseName;


    @ColumnWidth(30)
    @ExcelProperty(value = {"最新编辑时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "最新修改时间")
    private Date reviseTime;


    /**
     * 3D浏览数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"3D方案浏览-内容工场"})
    @ApiModelProperty(value = "3D方案浏览-内容工场")
    private Integer threeViewCount;

    /**
     * 3D点赞数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"3D方案点赞-内容工场"})
    @ApiModelProperty(value = "3D方案点赞")
    private Integer threeThumbsCount;

    /**
     * 3D收藏数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"3D方案收藏-内容工场"})
    @ApiModelProperty(value = "3D方案收藏")
    private Integer threeCollectCount;

    /**
     * 3D发送数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"3D方案发送-内容工场"})
    @ApiModelProperty(value = "3D方案发送")
    private Integer threeSendCount;


    /**
     * 通用浏览数
     */
//    @ColumnWidth(30)
//    @ExcelProperty(value = {"通用方案浏览-内容工场"}, index = 29)
//    @ApiModelProperty(value = "通用方案浏览")
//    private Integer commonViewCount;

    /**
     * 通用点赞数
     */
//    @ColumnWidth(30)
//    @ExcelProperty(value = {"通用方案点赞-内容工场"}, index = 30)
//    @ApiModelProperty(value = "通用方案点赞")
//    private Integer commonThumbsCount;

    /**
     * 通用收藏数
     */
//    @ColumnWidth(30)
//    @ExcelProperty(value = {"通用方案收藏-内容工场"}, index = 31)
//    @ApiModelProperty(value = "通用方案收藏")
//    private Integer commonCollectCount;

    /**
     * 通用发送数
     */
//    @ColumnWidth(30)
//    @ExcelProperty(value = {"通用方案发送-内容工场"}, index = 32)
//    @ApiModelProperty(value = "通用方案发送")
//    private Integer commonSendCount;

}
