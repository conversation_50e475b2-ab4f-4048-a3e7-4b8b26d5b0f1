package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DeviseSchemeStoreMapping implements Serializable {
    private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "")
	private Long id;
	
	@ApiModelProperty(value = "是否删除：0：否；其它：是")
	private Integer isDeleted;
	
	@ApiModelProperty(value = "")
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date createdDate;
	
	@ApiModelProperty(value = "")
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date modifiedDate;
	
	@ApiModelProperty(value = "方案id")
	private Long schemeId;
	
	@ApiModelProperty(value = "门店orgid")
	private Long storeOrgId;
	
	@ApiModelProperty(value = "门店code")
	private String storeCode;
	
	@ApiModelProperty(value = "门店name")
	private String storeName;
	
	@ApiModelProperty(value = "门店简称")
	private String abbreviation;


	@ApiModelProperty("门店类型，1：专卖店；2：KA店；3：社区店")
	private Integer storeType;

	/**
	 * 门店类型编码
	 */
	private String storeTypeCode;

	private String storeTypeName;

	private Integer storeStatus;
	

	

}