package com.fotile.exportcenter.cmscenter.scheme.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.cmscenter.scheme.pojo.entity.ContentCaseUserCollect;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.SchemeCaseStatisticalDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContentCaseUserCollectDao extends BaseMapper<ContentCaseUserCollect> {

    List<SchemeCaseStatisticalDTO> getSchemeCaseCollect(@Param("caseIds") List<Long> ids);

}