package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DeviseSchemeGoodsMapping implements Serializable {
    private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "")
	private Long id;
	
	@ApiModelProperty(value = "是否删除：0：否；其它：是")
	private Integer isDeleted;
	
	@ApiModelProperty(value = "")
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date createdDate;
	
	@ApiModelProperty(value = "")
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date modifiedDate;
	
	@ApiModelProperty(value = "方案id")
	private Long schemeId;
	
	@ApiModelProperty(value = "商品分类名称")
	private Long goodsId;
	
	@ApiModelProperty(value = "商品编码")
	private String goodsCode;
	
	@ApiModelProperty(value = "商品名称")
	private String goodsName;

}