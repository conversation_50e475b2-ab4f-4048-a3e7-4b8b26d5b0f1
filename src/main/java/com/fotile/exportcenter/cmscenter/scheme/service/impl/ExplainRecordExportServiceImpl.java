package com.fotile.exportcenter.cmscenter.scheme.service.impl;

import com.alibaba.excel.EasyExcel;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.client.OrgClient;
import com.fotile.exportcenter.client.UserEntityClient;
import com.fotile.exportcenter.client.WorkflowClient;
import com.fotile.exportcenter.client.pojo.SalesmanInfoForQYWX;
import com.fotile.exportcenter.client.pojo.UserEntityExtend;
import com.fotile.exportcenter.client.pojo.org.SalesmanLabel;
import com.fotile.exportcenter.cmscenter.scheme.dao.DeviseSchemeExplainRecordMapper;
import com.fotile.exportcenter.cmscenter.scheme.mapper.ExportImpl;
import com.fotile.exportcenter.cmscenter.scheme.mapper.SchemeMapper;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.CluesCustomerInfoForSchemeDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.CreateUerDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.ExplainRecordDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.GetSchemeExplainRecordListDTO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.StoreInfoVO;
import com.fotile.exportcenter.common.KvLs;
import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyAreaByOrgIdsOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByIdsInDto;
import com.fotile.exportcenter.marketing.service.SalesLeadsService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.exportcenter.util.SplitUtils;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component("122")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class ExplainRecordExportServiceImpl implements ExportImpl {


    @Autowired
    private DeviseSchemeExplainRecordMapper deviseSchemeExplainRecordMapper;

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    private UserEntityClient userEntityClient;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private SalesLeadsService salesLeadsService;


    @Autowired
    private OssService ossService;
    @Autowired
    private WorkflowClient workflowClient;


    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        //类型转换
        GetSchemeExplainRecordListDTO inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), GetSchemeExplainRecordListDTO.class);
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count1 = 1000;
        Integer start1 = inDto.getStartCount();
        Integer i = totalCount / count1;
        Integer lastCount = totalCount % count1;
        Integer size = 0;

        if (CollectionUtils.isNotEmpty(inDto.getChargeUserLabelValueList())) {
            List<Long> salesmanIds = getSalesmanIdsByLabelValue(inDto.getChargeUserLabelValueList());
            if (CollectionUtils.isEmpty(salesmanIds)) {
                throw new BusinessException("未能查询到结果！");
            }
            if (CollectionUtils.isEmpty(inDto.getChargeUserIds())) {
                inDto.setChargeUserIds(salesmanIds);
            } else {
                List<Long> chargeUserIds = inDto.getChargeUserIds();
                chargeUserIds.retainAll(salesmanIds);
                if (CollectionUtils.isEmpty(chargeUserIds)) {
                    throw new BusinessException("未能查询到结果！");
                }
                inDto.setChargeUserIds(chargeUserIds);
            }
        }

        List<DeviseSchemeExplainRecordVO> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count1;
            size = count1;
            if (j == i) {
                size = lastCount;
            }
            inDto.setOffSize(start.longValue());
            inDto.setPageSize(size);
            List<DeviseSchemeExplainRecordVO> outDto = deviseSchemeExplainRecordMapper.findRecordListForWeb(inDto);

            if (CollectionUtils.isNotEmpty(outDto)) {

                List<Long> salesmanIds = outDto.stream().map(DeviseSchemeExplainRecordVO::getChargeUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                // 查询创建人岗位
                Map<Long, String> stationMap = querySalesmanStation(salesmanIds);
                // 查询创建人能力标签
                Map<Long, String> labelValueMap = querySalesmanLabel(salesmanIds);

                for (DeviseSchemeExplainRecordVO vo : outDto) {
                    vo.setChargeUserStationName(stationMap.get(vo.getChargeUserId()));
                    vo.setChargeUserLabelValue(labelValueMap.get(vo.getChargeUserId()));
                }
                resultList.addAll(outDto);
            }

            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getPageSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);
        }
        if (resultList == null || resultList.size() <= 0) {
            throw new BusinessException("未能查询到结果！");
        }


        if (resultList != null && resultList.size() > 0) {

            List<Long> ids = resultList.parallelStream().map(DeviseSchemeExplainRecordVO::getSchemeId).distinct().collect(Collectors.toList());
            List<Long> clueIds = resultList.parallelStream().filter(p -> p.getCluesId() != null).map(DeviseSchemeExplainRecordVO::getCluesId).distinct().collect(Collectors.toList());
            //查询线索
            List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOList = new ArrayList<>();
            if (clueIds != null && clueIds.size() > 0) {
//                cluesCustomerInfoForSchemeDTOList = marketingClient.queryCluesCustomerIndoByCluesIdsOpen(clueIds).getData();
                cluesCustomerInfoForSchemeDTOList = queryCluesCustomerIndoByCluesIdsOpen(clueIds);
            }

            //20230821,issue:37092,增加大区展示
            List<FindCompanyAreaByOrgIdsOutDto> companyAreaByOrgIdsOutDtoList = new ArrayList<>();
            List<Long> companyIds = resultList.parallelStream().filter(x -> x.getCompanyId() != null).map(DeviseSchemeExplainRecordVO::getCompanyId).distinct().collect(Collectors.toList());
            if (companyIds != null && companyIds.size() > 0) {
//                FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
//                findCompanyByIdsInDto.setIdList(companyIds);
//                companyAreaByOrgIdsOutDtoList = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
                companyAreaByOrgIdsOutDtoList = findCompanyByOrgIdList(companyIds);
            }
            List<Long> storeOrgIdList = resultList.parallelStream().map(DeviseSchemeExplainRecordVO::getStoreOrgId).distinct().collect(Collectors.toList());

            List<StoreInfoVO> storeInfos = deviseSchemeExplainRecordMapper.getStoreInfo(storeOrgIdList);
            Map<Long, StoreInfoVO> storeMap = Maps.newHashMap();
            if (!org.springframework.util.CollectionUtils.isEmpty(storeInfos)){
                storeMap = storeInfos.parallelStream().collect(Collectors.toMap(StoreInfoVO::getOrgId, s -> s));
            }
            //查询讲解
//            List<ExplainRecordDTO> explainRecordDTOS = deviseSchemeExplainRecordMapper.selectListBySchemeIds(ids);
            List<ExplainRecordDTO> explainRecordDTOS = getExplainListBySchemeIds(ids);
            for (DeviseSchemeExplainRecordVO deviseSchemeVO : resultList) {
                deviseSchemeVO.setOrderAmount(deviseSchemeVO.getDetailOrderAmount());
                if (cluesCustomerInfoForSchemeDTOList != null && cluesCustomerInfoForSchemeDTOList.size() > 0) {
                    CluesCustomerInfoForSchemeDTO customerInfoForSchemeDTO = cluesCustomerInfoForSchemeDTOList.parallelStream().filter(x -> x.getId().equals(deviseSchemeVO.getCluesId())).findFirst().orElse(null);
                    if (customerInfoForSchemeDTO != null) {
                        deviseSchemeVO.setCluesCustomerName(customerInfoForSchemeDTO.getCustomerName());
                        deviseSchemeVO.setCluesCustomerPhone(customerInfoForSchemeDTO.getCustomerPhone());
                    }
                }
                if (explainRecordDTOS != null && explainRecordDTOS.size() > 0) {
                    ExplainRecordDTO explainRecordDTO = explainRecordDTOS.parallelStream().filter(x -> x.getSchemeId().equals(deviseSchemeVO.getSchemeId())).findFirst().orElse(null);
                    if (explainRecordDTO != null) {
                        deviseSchemeVO.setExplainCount(explainRecordDTO.getCount());
                        deviseSchemeVO.setDurationTimes(changeTimeSecond2Minute(explainRecordDTO.getDurationTime()));
                    }
                }
                if (deviseSchemeVO.getDurationTime() != null) {
                    deviseSchemeVO.setSingleDurationTime(changeTimeSecond2Minute(deviseSchemeVO.getDurationTime()));
                }
                if (deviseSchemeVO.getExplainCluesId() != null) {
                    if (deviseSchemeVO.getDetailOrderAmount() != null) {
                        deviseSchemeVO.setOrderStatus("是");
                    } else {
                        deviseSchemeVO.setOrderStatus("否");
                    }
                }

                if (deviseSchemeVO.getCompanyId() != null) {
                    if (CollectionUtils.isNotEmpty(companyAreaByOrgIdsOutDtoList)) {
                        FindCompanyAreaByOrgIdsOutDto companyAreaByOrgIdsOutDto = companyAreaByOrgIdsOutDtoList.parallelStream().filter(x -> deviseSchemeVO.getCompanyId().equals(x.getOrgId())).findFirst().orElse(null);
                        if (companyAreaByOrgIdsOutDto != null) {
                            deviseSchemeVO.setRegionName(companyAreaByOrgIdsOutDto.getValueName());
                        }
                    }
                }

                if (StringUtils.isNotBlank(deviseSchemeVO.getProvinceName()) && StringUtils.isNotBlank(deviseSchemeVO.getCityName()) && StringUtils.isNotBlank(deviseSchemeVO.getCountyName())) {
                    deviseSchemeVO.setProvinceName(deviseSchemeVO.getProvinceName() + "-" + deviseSchemeVO.getCityName() + "-" + deviseSchemeVO.getCountyName());
                } else {
                    deviseSchemeVO.setProvinceName("/");
                }

                if (StringUtils.isBlank(deviseSchemeVO.getVillageName())) {
                    deviseSchemeVO.setVillageName("/");
                }

                if (deviseSchemeVO.getType() == 1) {
                    deviseSchemeVO.setTypeName("场景化通用方案");
                } else if (deviseSchemeVO.getType() == 2) {
                    deviseSchemeVO.setTypeName("小区通用方案");
                } else {
                    deviseSchemeVO.setTypeName("顾客个性化方案");
                }

                if (deviseSchemeVO.getSource() != null) {
                    if (deviseSchemeVO.getSource() == 1) {
                        deviseSchemeVO.setSourceName("电视演示-手输方案编码");
                    } else if (deviseSchemeVO.getSource() == 2) {
                        deviseSchemeVO.setSourceName("营销中台演示");
                    } else if (deviseSchemeVO.getSource() == 3) {
                        deviseSchemeVO.setSourceName("云管理APP演示");
                    }else if (deviseSchemeVO.getSource() == 4) {
                        deviseSchemeVO.setSourceName("电视演示-扫码投屏");
                    } else {
                        deviseSchemeVO.setSourceName("");
                    }
                }
                if(StringUtils.isNotBlank(deviseSchemeVO.getChargeCode()) ){
                    deviseSchemeVO.setChargeCode(deviseSchemeVO.getChargeCode() );
                }else{
                    deviseSchemeVO.setChargeCode("/");
                }

                if(StringUtils.isNotBlank(deviseSchemeVO.getChargeUserName()) ){
                    deviseSchemeVO.setChargeUserName(deviseSchemeVO.getChargeUserName());
                }else{
                    deviseSchemeVO.setChargeUserName("/");
                }
                if(StringUtils.isNotBlank(deviseSchemeVO.getStoreCode()) ){
                    deviseSchemeVO.setStoreCode(deviseSchemeVO.getStoreCode());
                }else{
                    deviseSchemeVO.setStoreCode("/");
                }
                if(StringUtils.isNotBlank(deviseSchemeVO.getStoreName()) ){
                    deviseSchemeVO.setStoreName(deviseSchemeVO.getStoreName());
                }else{
                    deviseSchemeVO.setStoreName("/");
                }

                if(StringUtils.isBlank(deviseSchemeVO.getRegionName())){
                    deviseSchemeVO.setRegionName("/");
                }


                if(StringUtils.isBlank(deviseSchemeVO.getOrderStatus())){
                    deviseSchemeVO.setOrderStatus("/");
                }

                if (StringUtils.isNotBlank(deviseSchemeVO.getCluesCustomerName())) {
                    if (deviseSchemeVO.getCluesCustomerName().length() == 1) {
                        deviseSchemeVO.setCluesCustomerName(deviseSchemeVO.getCluesCustomerName() + "***" + deviseSchemeVO.getCluesCustomerName());
                    } else {
                        String firstName = deviseSchemeVO.getCluesCustomerName().substring(0, 1);
                        String lastName = deviseSchemeVO.getCluesCustomerName().substring(deviseSchemeVO.getCluesCustomerName().length() - 1);
                        deviseSchemeVO.setCluesCustomerName(firstName + "***" + lastName);
                    }
                }

                if (StringUtils.isNotBlank(deviseSchemeVO.getCluesCustomerPhone())) {
                    deviseSchemeVO.setCluesCustomerPhone(setCostomerPhone(deviseSchemeVO.getCluesCustomerPhone()));
                }

                if (deviseSchemeVO.getCluesId() != null) {
                    deviseSchemeVO.setRelationClues(deviseSchemeVO.getCluesId() + "/" + deviseSchemeVO.getCluesCustomerName() + "/" + deviseSchemeVO.getCluesCustomerPhone());
                } else {
                    deviseSchemeVO.setRelationClues("/");
                }

                if (deviseSchemeVO.getStatus() == 0) {
                    deviseSchemeVO.setStatusName("禁用");
                } else {
                    deviseSchemeVO.setStatusName("启用");
                }

                if (deviseSchemeVO.getExplainCount() == null) {
                    deviseSchemeVO.setExplainCount(0);
                }

                if (deviseSchemeVO.getOrderAmount() == null) {
                    deviseSchemeVO.setOrderAmount(BigDecimal.ZERO);
                }

                if (deviseSchemeVO.getDurationTimes() == null) {
                    deviseSchemeVO.setDurationTimes("0");
                }

                if(deviseSchemeVO.getAbnormalEndType() != null && deviseSchemeVO.getAbnormalEndType() == 1){
                    deviseSchemeVO.setAbnormalEndTypeName("播放任务中断自动结束讲解");
                }else if (deviseSchemeVO.getAbnormalEndType() != null && deviseSchemeVO.getAbnormalEndType() == 2){
                    deviseSchemeVO.setAbnormalEndTypeName("长时间未操作自动结束讲解");
                }

                //设置讲解线索字段的值
                if(deviseSchemeVO.getNeedCluesFlag() != null){
                    if(deviseSchemeVO.getNeedCluesFlag() == 1){
                        if(deviseSchemeVO.getExplainCluesId() != null){
                            deviseSchemeVO.setExplainCluesResult(String.valueOf(deviseSchemeVO.getExplainCluesId()));
                        }
                    }else if(deviseSchemeVO.getNeedCluesFlag() == 2){
                        if(deviseSchemeVO.getNoCluesType() != null){
                            if(deviseSchemeVO.getNoCluesType() == 1){
                                deviseSchemeVO.setExplainCluesResult("顾客未留资");
                            }else if(deviseSchemeVO.getNoCluesType() ==2){
                                deviseSchemeVO.setExplainCluesResult("预览方案/培训演示生成");
                            }
                        }
                    }
                }
                if (!storeMap.isEmpty()&&storeMap.containsKey(deviseSchemeVO.getStoreOrgId())){
                    deviseSchemeVO.setStoreChannelName(storeMap.get(deviseSchemeVO.getStoreOrgId()).getStoreChannelName());
                    if(StringUtils.isNotBlank(storeMap.get(deviseSchemeVO.getStoreOrgId()).getStoreChannelCode())){
                        try {
                            String subChannelName = Optional.of(storeInfos.get(0).getStoreChannelCode())
                                    .filter(StringUtils::isNotBlank)
                                    .map(workflowClient::getSubChannelCodeList)
                                    .filter(Result::getSuccess)
                                    .map(Result::getData)
                                    .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                                    .map(l -> l.stream().filter(d -> StringUtils.equals(d.getValueCode(), storeInfos.get(0).getStoreSubChannelCode())).findFirst())
                                    .filter(Optional::isPresent)
                                    .map(Optional::get)
                                    .map(Dict::getValueName)
                                    .orElse("缺省");
                            deviseSchemeVO.setStoreSubChannelName(subChannelName);
                        }catch (Exception e){
                            log.error("查询StoreSubChannelCode报错",e);
                        }
                    }
                }
            }
        }

        //生成Excel，并上传oss
        String fileName = "", sheetName = "场景化方案讲解记录";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //写入excel文件流
        try {
            List<ExportDeviseSchemeExplainRecordVO> exportDtos = SchemeMapper.INSTANCE.explainRecordToExcelDto(resultList);
            EasyExcel.write(os, ExportDeviseSchemeExplainRecordVO.class).sheet(sheetName).doWrite(exportDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        } catch (Exception e) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            log.error(e.toString());
            dataClientService.failureTask(exportTask);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 根据业务员id获取业务员标签
     *
     * @param salesmanIds 业务员id
     * @return 业务员标签
     */
    private Map<Long, String> querySalesmanLabel(List<Long> salesmanIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(salesmanIds)) {
            return result;
        }

        //增加标签返回
        List<SalesmanLabel> salesmanLabelList = deviseSchemeExplainRecordMapper.querySalesmanLabel(salesmanIds);
        if (CollectionUtils.isNotEmpty(salesmanLabelList)) {
            for (SalesmanLabel salesmanLabel : salesmanLabelList) {
                Long salesmanId = Long.valueOf(salesmanLabel.getSalesmanId());
                if (result.containsKey(salesmanId)) {
                    if (StringUtils.isNotBlank(salesmanLabel.getLabelName())) {
                        result.compute(salesmanId, (k, value) -> value + "，" + salesmanLabel.getLabelName());
                    }
                } else {
                    if (StringUtils.isNotBlank(salesmanLabel.getLabelName())) {
                        result.put(salesmanId, salesmanLabel.getLabelName());
                    }
                }
            }
        }

        return result;
    }

    /**
     * 根据业务员id获取业务员岗位
     */
    private Map<Long, String> querySalesmanStation(List<Long> salesmanIds) {

        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(salesmanIds)) {
            return result;
        }
        List<KvLs> data = deviseSchemeExplainRecordMapper.querySalesmanStation(salesmanIds);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(x -> result.put(x.getKey(), x.getValue()));
        }
        return result;
    }

    /**
     * 根据能力标签获取业务员id
     */
    private List<Long> getSalesmanIdsByLabelValue(List<String> salesmanLabelValueList) {
        List<Long> salesmanIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(salesmanLabelValueList)) {
            return salesmanIds;
        }

        return deviseSchemeExplainRecordMapper.querySalesmanIdByLabelValue(salesmanLabelValueList);
    }

    private List<ExplainRecordDTO> getExplainListBySchemeIds(List<Long> ids) {
        List<ExplainRecordDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> idList = (List<Long>)list1;
                List<ExplainRecordDTO> explainRecordDTOS = deviseSchemeExplainRecordMapper.selectListBySchemeIds(idList);
                list.addAll(explainRecordDTOS);
            }
        }
        return list;
    }
    private List<FindCompanyAreaByOrgIdsOutDto> findCompanyByOrgIdList(List<Long> ids) {
        List<FindCompanyAreaByOrgIdsOutDto> companyAreaList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> companyIds = (List<Long>)list1;
                FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
                findCompanyByIdsInDto.setIdList(companyIds);
                List<FindCompanyAreaByOrgIdsOutDto> companyDtos =  orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
                companyAreaList.addAll(companyDtos);
            }
        }
        return companyAreaList;
    }

    private List<CluesCustomerInfoForSchemeDTO> queryCluesCustomerIndoByCluesIdsOpen(List<Long> ids) {
        List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<?>> lists = SplitUtils.splitList(ids, 1000);
            for (List<?> list1 : lists) {
                List<Long> clueIds = (List<Long>)list1;
                List<CluesCustomerInfoForSchemeDTO> cluesCustomerInfoForSchemeDTOS =  salesLeadsService.queryCluesCustomerIndoByCluesIdsOpen(clueIds);
                cluesCustomerInfoForSchemeDTOList.addAll(cluesCustomerInfoForSchemeDTOS);
            }
        }
        return cluesCustomerInfoForSchemeDTOList;
    }


    private List<CreateUerDTO> setCreateUserInfo(List<String> userIds){
        //去重
        HashSet userIdSet = new HashSet(userIds);
        List<String> userIdArray = new ArrayList<>(userIdSet);
        List<CreateUerDTO> createUerDTOList = new ArrayList<>();
        List<UserEntityExtend> userEntityExtendList = userEntityClient.findUserEntityExtendByUserIdsPost(userIdArray).getData();
        if (CollectionUtils.isNotEmpty(userEntityExtendList)) {
            List<Long> salesManIds = userEntityExtendList.parallelStream().filter(p -> p.getSalesmanId() != null).map(UserEntityExtend::getSalesmanId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salesManIds)) {
                List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(salesManIds).getData();
                if (CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)) {
                    CreateUerDTO createUerDTO = new CreateUerDTO();
                    for (UserEntityExtend userEntityExtend : userEntityExtendList) {
                        createUerDTO = new CreateUerDTO();
                        createUerDTO.setCreatedBy(userEntityExtend.getUserEntityId());
                        SalesmanInfoForQYWX salesmanInfoForQYWX = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(userEntityExtend.getSalesmanId())).findFirst().orElse(null);
                        if (salesmanInfoForQYWX != null) {
                            createUerDTO.setCreateStoreOrgId(salesmanInfoForQYWX.getStoreId());
                            createUerDTO.setCreateStoreCode(salesmanInfoForQYWX.getStoreCode());
                            createUerDTO.setCreateStoreName(salesmanInfoForQYWX.getStoreName());
                            createUerDTO.setCreateAbbreviation(salesmanInfoForQYWX.getAbbreviation());
                            createUerDTO.setCreateChargeUserId(salesmanInfoForQYWX.getId());
                            createUerDTO.setCreateChargeCode(salesmanInfoForQYWX.getCode());
                            createUerDTO.setCreateChargeUserName(salesmanInfoForQYWX.getName());
                            createUerDTO.setCreateChargePhone(salesmanInfoForQYWX.getPhone());
                        }
                        createUerDTOList.add(createUerDTO);
                    }
                }
            }
        }
        return createUerDTOList;
    }


    public static String changeTimeSecond2Minute(Integer second) {
        Integer seconds = Integer.valueOf(second);
        BigDecimal a = new BigDecimal(seconds);
        BigDecimal b = new BigDecimal(60);
        String min =  ((a).divide(b,2,BigDecimal.ROUND_HALF_UP )).toString();
        return min;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4);
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4);
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }


}
