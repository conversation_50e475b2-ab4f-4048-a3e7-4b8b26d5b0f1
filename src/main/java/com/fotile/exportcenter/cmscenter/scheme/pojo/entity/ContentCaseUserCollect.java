package com.fotile.exportcenter.cmscenter.scheme.pojo.entity;

import lombok.Data;

import java.util.Date;

/**
    * 用户案例收藏表
    */
@Data
public class ContentCaseUserCollect {
    /**
    * id
    */
    private Long id;

    /**
    * 用户id
    */
    private Long userId;

    /**
     * 类型 1：业务员 2：顾客id (customer_id)
     */
    private Long type;

    /**
    * 内容id
    */
    private Long contentId;

    private Integer contentType;

    /**
    * 是否收藏 0否 1是
    */
    private Integer isCollect;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;
}