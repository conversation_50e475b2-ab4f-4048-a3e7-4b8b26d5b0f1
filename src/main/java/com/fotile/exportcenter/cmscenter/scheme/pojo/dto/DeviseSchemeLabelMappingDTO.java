package com.fotile.exportcenter.cmscenter.scheme.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeviseSchemeLabelMappingDTO implements Serializable {
    private static final long serialVersionUID = 1L;


	private Long id;

	@ApiModelProperty(value = "方案id")
	private Long schemeId;


	/**
	 * 标签组名
	 */
	private String fieldId;
	
	@ApiModelProperty(value = "属性值id--关联property_parameter主键id")
	private String propertyParameterId;

	private String name;
	

	

}