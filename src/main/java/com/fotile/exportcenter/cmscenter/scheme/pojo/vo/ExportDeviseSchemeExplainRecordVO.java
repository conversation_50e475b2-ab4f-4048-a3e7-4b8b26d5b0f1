package com.fotile.exportcenter.cmscenter.scheme.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExportDeviseSchemeExplainRecordVO implements Serializable {

    @ColumnWidth(10)
    @ExcelProperty(value = {"方案编码"})
    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案标题"})
    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"省市区"})
    @ApiModelProperty(value = "省")
    private String provinceName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"小区名称"})
    @ApiModelProperty(value = "小区")
    private String villageName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案类型"})
    private String typeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"关联线索"})
    private String relationClues;


    /**
     * 关联线索时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"线索关联时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date relatedCluesTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解次数"})
    private Integer explainCount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解时长（分）"})
    private String durationTimes;

    @ColumnWidth(30)
    @ExcelProperty(value = {"状态"})
    private String statusName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"分公司"})
    private String createdCompanyName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建业务员编码"})
    @ApiModelProperty(value = "创建业务员编码")
    @FieldEncrypt
    private String createdCode;
    @ColumnWidth(30)
    @ExcelProperty(value = {"创建业务员"})
    @ApiModelProperty(value = "创建业务员")
    @FieldEncrypt
    private String createdName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"创建时间"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;


    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解账号"})
    @ApiModelProperty(value = "讲解账号")
    @FieldEncrypt
    private String createdName1;
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解业务员编码"})
    @ApiModelProperty(value = "讲解业务员编码")
    @FieldEncrypt
    private String chargeCode;
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解业务员"})
    @ApiModelProperty(value = "提交人业务员名称")
    @FieldEncrypt
    private String chargeUserName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人岗位"})
    private String chargeUserStationName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人能力标签"})
    private String chargeUserLabelValue;


    /**
     * 大区名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属大区"})
    private String regionName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属分公司"})
    @ApiModelProperty(value = "分公司名称")
    private String companyName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty(value = "门店渠道")
    private String storeChannelName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty(value = "门店渠道细分")
    private String storeSubChannelName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属门店编码"})
    @ApiModelProperty(value = "门店name")
    private String storeCode;
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属门店"})
    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解平台"})
    private String sourceName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索"})
    @ApiModelProperty(value = "线索id")
    private String explainCluesResult;

    /**
     * 订单成交状态：1.成交  0无
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索是否成交"})
    private String orderStatus;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索成交金额"})
    private BigDecimal orderAmount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"开始讲解时间"})
    private Date startTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"结束讲解时间"})
    private Date endTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"结束讲解方式"})
    private String abnormalEndTypeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"单次讲解时长（分）"})
    private String singleDurationTime;


}
