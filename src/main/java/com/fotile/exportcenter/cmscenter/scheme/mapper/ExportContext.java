package com.fotile.exportcenter.cmscenter.scheme.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ExportContext {

    @Autowired
    Map<String, ExportImpl> exportMap = new ConcurrentHashMap<>(4);


    public ExportImpl getExportImpl(String component) throws Exception{
        ExportImpl strategy = exportMap.get(component);
        if(strategy == null) {
            throw new RuntimeException("no strategy defined");
        }
        return strategy;
    }
}
