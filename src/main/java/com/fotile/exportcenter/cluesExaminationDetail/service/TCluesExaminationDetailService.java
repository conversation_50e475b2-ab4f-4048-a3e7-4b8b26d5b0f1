package com.fotile.exportcenter.cluesExaminationDetail.service;

import cn.hutool.core.util.DesensitizedUtil;
import com.fotile.exportcenter.client.UserAddressClientService;
import com.fotile.exportcenter.client.UserEntityClientService;
import com.fotile.exportcenter.client.pojo.UserAddressSimpleInDTO;
import com.fotile.exportcenter.client.pojo.UserAddressSimpleOutDTO;
import com.fotile.exportcenter.client.pojo.UserEntityExtend;
import com.fotile.exportcenter.cluesExaminationDetail.dao.TCluesExaminationDetailDao;
import com.fotile.exportcenter.cluesExaminationDetail.dbCommonService.AdbDbExaminationService;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationKitchenDetailOutDTO;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListInDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QuerySimpleExaminationListOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindAttributeValueOutDto;
import com.fotile.exportcenter.util.CommonUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 厨房健康体检从表(TCluesExaminationDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-11 09:56:53
 */
@Service
@Slf4j
public class TCluesExaminationDetailService {
    @Autowired
    private TCluesExaminationDetailDao tCluesExaminationDetailDao;
    @Autowired
    private UserAddressClientService userAddressClientService;
    @Autowired
    private UserEntityClientService userEntityClientService;
    @Autowired
    AdbDbExaminationService adbDbExaminationService;

    /**
     * 预处理查询条件
     */
    private void preprocessingQueryExaminationListInDto(QueryExaminationListInDto inDto) {
        // 不存在可直接查询的条件无需预处理
        if (CollectionUtils.isEmpty(inDto.getIds())
                && Objects.isNull(inDto.getStartTime()) && Objects.isNull(inDto.getEndTime())
                && Objects.isNull(inDto.getCluesId())) {
            return;
        }

        int maxSize = 200;

        // 简单查询的返回数量
        Integer count = adbDbExaminationService.querySimpleExaminationCountUseSalesman(inDto);
        if (count == 0 || count > maxSize) {
            return;
        }

        List<QuerySimpleExaminationListOutDto> data = adbDbExaminationService.querySimpleExaminationListUseSalesman(inDto);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        List<Long> ids = new ArrayList<>();
        List<Long> cluesIds = new ArrayList<>();
        List<String> createdByAccountIds = new ArrayList<>();

        for (QuerySimpleExaminationListOutDto dto : data) {

            if (Objects.nonNull(dto.getId())) {
                ids.add(dto.getId());
            }
            if (Objects.nonNull(dto.getCluesId())) {
                cluesIds.add(dto.getCluesId());
            }
            if (StringUtils.isNotBlank(dto.getCreatedByAccountId())) {
                createdByAccountIds.add(dto.getCreatedByAccountId());
            }
        }

        if (!CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(inDto.getIds())) {
            inDto.setIds(ids);
        }
        if (!CollectionUtils.isEmpty(cluesIds)) {
            inDto.setCluesIds(cluesIds);
        }
        if (!CollectionUtils.isEmpty(createdByAccountIds)) {
            inDto.setCreatedByAccountIds(createdByAccountIds);
        }

    }

    /**
     * 查询导出服务报告列表
     */
    public PageInfo<QueryExaminationListOutDto> queryExaminationExportList(QueryExaminationListInDto inDto) {
        PageInfo<QueryExaminationListOutDto> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());

        inDto.setSecretKey(MybatisMateConfig.getPassword());
        // 预处理查询请求条件
        preprocessingQueryExaminationListInDto(inDto);

        Long count = adbDbExaminationService.queryExaminationListCountUseSalesman(inDto);
        List<QueryExaminationListOutDto> examinationList = new ArrayList<>();
        if (count != null && count > 0) {
            examinationList = adbDbExaminationService.queryExaminationExportListUseSalesman(inDto, pageInfo);
            //房屋类型
            Set<Long> companyIds = examinationList.stream().map(e -> e.getCompanyId()).collect(Collectors.toSet());
            List<Long> companyIdList = new ArrayList<>(companyIds);
            List<FindAttributeValueOutDto> findAttributeValueOutDtos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(companyIdList)) {
                findAttributeValueOutDtos = tCluesExaminationDetailDao.findttributeByCompanyIds(companyIdList);
            }
            Set<Long> addressIds = examinationList.stream().map(e -> e.getAddressId()).collect(Collectors.toSet());
            List<Long> addressIdList = new ArrayList<>(addressIds);
            List<UserAddressSimpleOutDTO> userAddressOutDtos = new ArrayList<>();
            if (addressIdList != null && addressIdList.size() > 0) {
//                userAddressOutDtos = userAddressClientService.selectUserAddressByIdList2(addressIdList).getData();
                //分隔list防止大数据量影响sql性能
                List<List<Long>> addressIdListNest = CommonUtils.splitList(addressIdList, 10000);
                for (List<Long> idList : addressIdListNest) {
                    UserAddressSimpleInDTO userAddressSimpleInDTO = new UserAddressSimpleInDTO();
                    userAddressSimpleInDTO.setIdList(idList);
                    List<UserAddressSimpleOutDTO> userAddressOutDtoTemp = userAddressClientService.selectSimpleUserAddressByIdList(userAddressSimpleInDTO).getData();
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userAddressOutDtoTemp)) {
                        userAddressOutDtos.addAll(userAddressOutDtoTemp);
                    }
                }
            }

            //创建人
            /*Set<String> createdBys = examinationList.stream().map(e -> e.getCreatedBy()).collect(Collectors.toSet());
            List<String> createdByList = new ArrayList<>(createdBys);
            List<UserEntityExtend> userEntityExtendList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(createdByList)) {
                userEntityExtendList = userEntityClientService.findUserEntityExtendByUserIdsPost(createdByList).getData();
            }*/

            for (QueryExaminationListOutDto examination : examinationList) {
                if (examination.getTypeId() == 2 && examination.getRecheckFlag() != 1) {
                    examination.setRecheckFlagName("/");
                }

                //创建人
                /*UserEntityExtend userEntity = userEntityExtendList.stream().filter(u -> u.getUserEntityId().equals(examination.getCreatedBy())).findFirst().orElse(null);
                if (userEntity != null) {
                    if (StringUtils.isNotBlank(userEntity.getSalesmanCode())) {
                        examination.setCreatedPerson(userEntity.getSalesmanCode() + "/" + (StringUtils.isNotBlank(userEntity.getSalesmanName()) ? userEntity.getSalesmanName() : "(空)"));
                    }
                }*/

                //手机号脱敏
                if (inDto.getFlag() != null && inDto.getFlag() == 1) {
                    if (StringUtils.isNotBlank(examination.getCustomerPhone())) {
                        examination.setCustomerPhone(setCostomerPhone(examination.getCustomerPhone()));
                    }
                }

                //线索（线索id/手机号）
                if (Objects.nonNull(examination.getCluesId())) {
                    StringBuilder builder = new StringBuilder();
                    builder.append(examination.getCluesId());
                    if (StringUtils.isNotBlank(examination.getCluesCustomerPhone())) {
                        if (Objects.equals(inDto.getFlag(), 1)) {
                            builder.append("/").append(setCostomerPhone(examination.getCluesCustomerPhone()));
                        } else {
                            builder.append("/").append(examination.getCluesCustomerPhone());
                        }
                    }
                    examination.setCluesIdOrPhone(builder.toString());
                }

                //房屋类型
                examination.setHouseType("");
                if (examination.getAddressId() != null) {
                    Optional<UserAddressSimpleOutDTO> first = userAddressOutDtos.stream().filter(a -> a.getId().equals(examination.getAddressId())).findFirst();
                    if (first.isPresent()) {
                        UserAddressSimpleOutDTO userAddressOutDto = first.get();
                        if (userAddressOutDto != null && userAddressOutDto.getHouseType() != null) {
                            Optional<FindAttributeValueOutDto> attributeValueOutDto = findAttributeValueOutDtos.stream().filter(a
                                    -> a.getOrgId().equals(examination.getCompanyId()) && "house_type".equals(a.getFieldId())
                                    && a.getAttributeId().equals(userAddressOutDto.getHouseType().toString())).findFirst();

                            if (attributeValueOutDto.isPresent()) {
                                examination.setHouseType(attributeValueOutDto.get().getAttributeValue());
                            }
                        }
                    }
                }

            }
        }

        pageInfo.setTotal(count);
        pageInfo.setRecords(examinationList);
        return pageInfo;
    }

    /**
     * 查询导出服务报告列表
     */
    public PageInfo<QueryExaminationKitchenDetailOutDTO> queryExaminationKitchenDetailExport(QueryExaminationListInDto inDto) {
        PageInfo<QueryExaminationKitchenDetailOutDTO> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());

        inDto.setSecretKey(MybatisMateConfig.getPassword());
        // 预处理查询请求条件
        preprocessingQueryExaminationListInDto(inDto);

        Long count = adbDbExaminationService.queryExaminationListCountUseSalesman(inDto);
        List<QueryExaminationKitchenDetailOutDTO> examinationList = new ArrayList<>();
        if (count != null && count > 0) {
            examinationList = adbDbExaminationService.queryExaminationKitchenDetailExportUseSalesman(inDto, pageInfo);

            for (QueryExaminationKitchenDetailOutDTO examination : examinationList) {

                //顾客姓名脱敏
                if (StringUtils.isNotBlank(examination.getNickName())) {
                    examination.setTmNickName(DesensitizedUtil.chineseName(examination.getNickName()));
                }

                //手机号脱敏
                if (StringUtils.isNotBlank(examination.getPhone())) {
                    examination.setTmPhone(setCostomerPhone(examination.getPhone()));
                }

                //创建人手机号脱敏
                if (StringUtils.isNotBlank(examination.getCreatedPhone())) {
                    examination.setTmCreatedPhone(setCostomerPhone(examination.getCreatedPhone()));
                }

                //所属业务员手机号脱敏
                if (StringUtils.isNotBlank(examination.getBelongPhone())) {
                    examination.setTmBelongPhone(setCostomerPhone(examination.getBelongPhone()));
                }
            }
        }

        pageInfo.setTotal(count);
        pageInfo.setRecords(examinationList);
        return pageInfo;
    }

    //手机号脱敏方法
    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }
}