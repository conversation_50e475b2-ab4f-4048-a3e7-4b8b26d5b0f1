package com.fotile.exportcenter.cluesExaminationDetail.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 厨房健康体检从表(TCluesExaminationDetail)实体类
 *
 * <AUTHOR>
 * @since 2022-01-11 09:56:51
 */
@Data
public class TCluesExaminationDetail implements Serializable {

    private Long id;

    private Long isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人username
     */
    private String reviseName;
    /**
     * 最后修改时间
     */
    private Date reviseTime;
    /**
     * 线索id
     */
    private Long cluesId;
    /**
     * 检测次数
     */
    private Long checkCount;
    /**
     * 是否复检(0:否，1：是)
     */
    private Long recheckFlag;
    /**
     * 厨电类型
     */
    private String goodsType;
    /**
     * 超出质保年限
     */
    private String outPeriod;
    /**
     * 相位异常产品
     */
    private String abnormalGoods;
    /**
     * 是否有饮用水净水设备(0:否，1：是)
     */
    private Long waterEquipmentFlag;
    /**
     * 净水使用时间（年）
     */
    private String useEquipmentDuration;
    /**
     * 净水设备类型
     */
    private String equipmentType;
    /**
     * 饮用水tds检测（mg/l）
     */
    private BigDecimal tdsValue;
    /**
     * 甲醛检测（mg/m³）
     */
    private BigDecimal formaldehydeValue;
    /**
     * tvoc（mg/m³）
     */
    private BigDecimal tvocValue;
    /**
     * 煤气表是否存在泄漏(0:否，1：是)
     */
    private Long meterBlowFlag;
    /**
     * 燃气检测点
     */
    private String meterBlowPoint;
    /**
     * 灶具燃气管路是否存在泄漏(0:否，1：是)
     */
    private Long cookersBlowFlag;
    /**
     * 灶具排查泄漏点
     */
    private String cookersBlowPoint;
    /**
     * 燃热/壁挂炉燃气管路是否存在泄漏(0:否，1：是)
     */
    private Long heaterBlowFlag;
    /**
     * 燃热/壁挂炉排查泄漏点
     */
    private String heaterBlowPoint;
    /**
     * 建议
     */
    private String suggest;
    /**
     * 抽油烟机风速(m/s)
     */
    private BigDecimal rangeHoodWindSpeed;
    /**
     * 清洗前风速
     */
    private BigDecimal cleanHoodBeforeSpeed;
    /**
     * 清洗后风速
     */
    private BigDecimal cleanHoodAfterSpeed;
    /**
     * 烟机使用年限
     */
    private Integer hoodUsedAge;
    /**
     * 超出质保年限 0:否；1是
     */
    private Integer hoodOutPeriodFlag;
    /**
     * 清洗前照片
     */
    private String cleanHoodBeforePic;
    /**
     * 清洗后照片
     */
    private String cleanHoodAfterPic;
    /**
     * 类型 1：厨房健康检测，2烟机清洗检测
     */
    private Integer type;

    /**
     *查看次数
     */
    @TableField(value = "`view_count`")
    private Long viewCount;

    /**
     * 分享次数
     */
    @TableField(value = "`share_count`")
    private Long shareCount;


}