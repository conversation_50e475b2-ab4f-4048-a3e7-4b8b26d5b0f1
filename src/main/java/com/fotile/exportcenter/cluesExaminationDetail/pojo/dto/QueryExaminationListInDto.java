package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QueryExaminationListInDto {
    /**
     * page
     */
    private Integer page;
    /**
     *size
     */
    private Integer size;
    /**
     *ids
     */
    private List<Long> ids;

    /**
     *所属大区编码
     */
    private String districtCode;

    /**
     *分公司id
     */
    private Long companyId;
    /**
     *门店id
     */
    private Long storeOrgId;
    /**
     *业务员code
     */
    private String chargeCode;
    /**
     *创建业务员id
     */
    private Long chargeId;

    /**
     *所属业务员id
     */
    private Long belongSalesmanId;

    /**
     *类型
     */
    private Integer type;
    /**
     *手机号
     */
    private String phone;
    /**
     *名称
     */
    private String name;
    /**
     *开始时间
     */
    private Date startTime;
    /**
     *结束时间
     */
    private Date endTime;
    /**
     *1:脱敏 2:非脱敏
     */
    private Integer flag = 1;
    /**
     * authorCompanyIds
     */
    private List<Long> authorCompanyIds;

    /**
     * 统计数据开始时间（格式：yyyyMMdd）
     */
    private Integer calcStartDate;

    /**
     * 统计数据结束时间（格式：yyyyMMdd）
     */
    private Integer calcEndDate;

    /**
     * 线索（支持线索id及手机号模糊查询）
     */
    private Long cluesId;

    /**
     * 是否新店 null-全部 1-是 2-否
     */
    private Integer isNewStore;

    /**
     * [前端传参门店渠道编码，多个逗号拼接，如storeChannelCode=L044,L043]门店所属渠道编码(channel_category.code type_code=store_type)
     */
    private String storeChannelCode;

    /**
     * 渠道大类ID(多个半角逗号分隔)
     */
    private String channelCategoryIds;

    /**
     * 渠道细分ID(多个半角逗号分隔)
     */
    private String channelSubdivideIds;

    /**
     * 线索审核状态 -1 空 40 未提交 1待审核 2审核通过 3审核不通过
     */
    private String cluesAuditStatus;

    /**
     * 是否关联工单 null-全部 1-是 0-否
     */
    private Integer isAssociateServiceOrder;

    /**
     * 是否关联好友 null-全部 1-是 0-否
     */
    private Integer isAssociateCustomer;

    /**
     * 【后端使用】加密密钥
     */
    private String secretKey;

    /**
     * 【后端使用】是否导出 0->否 1->是
     */
    private Integer izExport = 0;

    /**
     * 【后端使用】是否健康报告详情导出 0->否 1->是
     */
    private Integer izKitchenDetailExport = 0;

    /**
     * 【仅导出使用】文件名
     */
    private String fileName;

    /**
     * 【性能优化】线索id集合
     */
    private List<Long> cluesIds;
    /**
     * 【性能优化】创建人账号id集合
     */
    private List<String> createdByAccountIds;
}
