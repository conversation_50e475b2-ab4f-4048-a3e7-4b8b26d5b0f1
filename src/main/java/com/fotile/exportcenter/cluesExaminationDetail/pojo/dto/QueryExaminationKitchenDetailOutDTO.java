package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 健康报告详情导出类
 */
@Data
public class QueryExaminationKitchenDetailOutDTO {

    /**
     * 报告id
     */
    private Long reportId;

    /**
     * 问题项数
     */
    private String problemNum;

    /**
     * 问题点
     */
    private String problemList;

    /**
     * 顾客姓名
     */
    private String nickName;

    /**
     * 顾客姓名（脱敏）
     */
    private String tmNickName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号（脱敏）
     */
    private String tmPhone;

    /**
     * 地区
     */
    private String areaComb;

    /**
     * 小区名称
     */
    private String communityComb;

    /**
     * 创建时间
     */
    private String createdDate;

    /**
     * 1、火盖状态是否腐蚀、变形、火孔有堵塞？
     */
    private String fq1;

    /**
     * 2、火焰状态是否有火孔未出火、是否红黄火？
     */
    private String fq2;

    /**
     * 3、燃气检测仪器是否报警？
     */
    private String fq3;

    /**
     * 4、灶具使用年限
     */
    private String fq4;

    /**
     * 5、灶具与燃气管接口处是否泄漏？
     */
    private String fq5;

    /**
     * 6、灶具炉头处是否泄露？
     */
    private String fq6;

    /**
     * 7、热水器使用年限
     */
    private String fq7;

    /**
     * 8、热水器与燃气管接口处是否泄漏？
     */
    private String fq8;

    /**
     * 9、热水器是否点火不顺畅？
     */
    private String fq9;

    /**
     * 10、厨房插座是否存在电路问题？
     */
    private String fq10;

    /**
     * 11、烟管排烟是否顺畅？
     */
    private String fq11;

    /**
     * 12、油烟机使用年限
     */
    private String fq12;

    /**
     * 13、吸烟效果检测
     */
    private String fq13;

    /**
     * 14、油烟机机型
     */
    private String fq14;

    /**
     * 14、油烟机形状
     */
    private String fq101;

    /**
     * 14、挂机高度
     */
    private String fq102;

    /**
     * 14、油烟机风速检测数值（m/s）
     */
    private String fq103;

    /**
     * 15、内部脏污程度检测
     */
    private String fq15;

    /**
     * 16、异音检测
     */
    private String fq16;

    /**
     * 17、是否安装净水器？
     */
    private String fq17;

    /**
     * 18、净水TDS检测数值（mg/l)
     */
    private String fq18;

    /**
     * 18、自来水TDS检测数值（mg/l)
     */
    private String fq201;

    /**
     * 19、净水铜离子检测结果？
     */
    private String fq19;

    /**
     * 19、自来水铜离子检测结果？
     */
    private String fq301;

    /**
     * 20、净水氯离子检测结果？
     */
    private String fq20;

    /**
     * 20、自来水氯离子检测结果？
     */
    private String fq401;

    /**
     * 21、冰箱是否存在明显异味？
     */
    private String fq21;

    /**
     * 22、冰箱是否存在积水、结冰等健康隐患？
     */
    private String fq22;

    /**
     * 其他安全隐患
     */
    private String otherSecurityRisks;

    /**
     * 综合诊断建议
     */
    private String comprehensiveDiagnosticAdvice;

    /**
     * 创建业务员大区
     */
    private String createdAreaName;

    /**
     * 创建业务员分公司
     */
    private String createdCompanyName;

    /**
     * 创建业务员门店渠道
     */
    private String createdStoreChannelName;

    /**
     * 创建业务员门店渠道细分
     */
    private String createdStoreSubChannelCode;

    /**
     * 创建业务员客户渠道
     */
    private String createdChannelSubdivideName;

    /**
     * 创建业务员客户
     */
    private String createdDistributorName;

    /**
     * 创建业务员门店编码
     */
    private String createdStoreCode;

    /**
     * 创建业务员门店
     */
    private String createdStoreName;

    /**
     * 创建业务员编码
     */
    private String createdCode;

    /**
     * 创建业务员姓名
     */
    private String createdName;

    /**
     * 创建人手机号
     */
    private String createdPhone;

    /**
     * 创建人手机号（脱敏）
     */
    private String tmCreatedPhone;

    /**
     * 创建人岗位
     */
    private String createdPosition;

    /**
     * 所属业务员编码
     */
    private String belongCode;

    /**
     * 所属业务员姓名
     */
    private String belongName;

    /**
     * 所属业务员手机号
     */
    private String belongPhone;

    /**
     * 所属业务员手机号（脱敏）
     */
    private String tmBelongPhone;
}
