package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fotile.exportcenter.common.constant.CommonConstant;
import lombok.Data;

import java.util.Objects;

/**
 * 健康报告详情导出类
 */
@Data
public class QueryExaminationKitchenDetailExportDTO {

    @ExcelProperty(value = {"报告ID"}, index = 0)
    private Long reportId;

    @ExcelProperty(value = {"问题项数"}, index = 1)
    private String problemNum;

    @ExcelProperty(value = {"问题点"}, index = 2)
    private String problemList;

    @ExcelProperty(value = {"顾客姓名（脱敏）"}, index = 3)
    private String tmNickName;

    @ExcelProperty(value = {"手机号（脱敏）"}, index = 4)
    private String tmPhone;

    @ExcelProperty(value = {"地区"}, index = 5)
    private String areaComb;

    @ExcelProperty(value = {"小区名称"}, index = 6)
    private String communityComb;

    @ExcelProperty(value = {"创建时间"}, index = 7)
    private String createdDate;

    @ExcelProperty(value = {"1、火盖状态是否腐蚀、变形、火孔有堵塞？"}, index = 8)
    private String fq1;

    @ExcelProperty(value = {"2、火焰状态是否有火孔未出火、是否红黄火？"}, index = 9)
    private String fq2;

    @ExcelProperty(value = {"3、燃气检测仪器是否报警？"}, index = 10)
    private String fq3;

    @ExcelProperty(value = {"4、灶具使用年限"}, index = 11)
    private String fq4;

    @ExcelProperty(value = {"5、灶具与燃气管接口处是否泄漏？"}, index = 12)
    private String fq5;

    @ExcelProperty(value = {"6、灶具炉头处是否泄露？"}, index = 13)
    private String fq6;

    @ExcelProperty(value = {"7、热水器使用年限"}, index = 14)
    private String fq7;

    @ExcelProperty(value = {"8、热水器与燃气管接口处是否泄漏？"}, index = 15)
    private String fq8;

    @ExcelProperty(value = {"9、热水器是否点火不顺畅？"}, index = 16)
    private String fq9;

    @ExcelProperty(value = {"10、厨房插座是否存在电路问题？"}, index = 17)
    private String fq10;

    @ExcelProperty(value = {"11、烟管排烟是否顺畅？"}, index = 18)
    private String fq11;

    @ExcelProperty(value = {"12、油烟机使用年限"}, index = 19)
    private String fq12;

    @ExcelProperty(value = {"13、吸烟效果检测"}, index = 20)
    private String fq13;

    @ExcelProperty(value = {"14、油烟机机型"}, index = 21)
    private String fq14;

    @ExcelProperty(value = {"油烟机形状"}, index = 22)
    private String fq101;

    @ExcelProperty(value = {"挂机高度"}, index = 23)
    private String fq102;

    @ExcelProperty(value = {"油烟机风速检测数值（m/s）"}, index = 24)
    private String fq103;

    @ExcelProperty(value = {"15、内部脏污程度检测"}, index = 25)
    private String fq15;

    @ExcelProperty(value = {"16、异音检测"}, index = 26)
    private String fq16;

    @ExcelProperty(value = {"17、是否安装净水器？"}, index = 27)
    private String fq17;

    @ExcelProperty(value = {"18、净水TDS检测数值（mg/l)"}, index = 28)
    private String fq18;

    @ExcelProperty(value = {"自来水TDS检测数值（mg/l)"}, index = 29)
    private String fq201;

    @ExcelProperty(value = {"19、净水铜离子检测结果？"}, index = 30)
    private String fq19;

    @ExcelProperty(value = {"自来水铜离子检测结果？"}, index = 31)
    private String fq301;

    @ExcelProperty(value = {"20、净水氯离子检测结果？"}, index = 32)
    private String fq20;

    @ExcelProperty(value = {"自来水氯离子检测结果？"}, index = 33)
    private String fq401;

    @ExcelProperty(value = {"21、冰箱是否存在明显异味？"}, index = 34)
    private String fq21;

    @ExcelProperty(value = {"22、冰箱是否存在积水、结冰等健康隐患？"}, index = 35)
    private String fq22;

    @ExcelProperty(value = {"其他安全隐患"}, index = 36)
    private String otherSecurityRisks;

    @ExcelProperty(value = {"综合诊断建议"}, index = 37)
    private String comprehensiveDiagnosticAdvice;

    @ExcelProperty(value = {"创建业务员大区"}, index = 38)
    private String createdAreaName;

    @ExcelProperty(value = {"创建业务员分公司"}, index = 39)
    private String createdCompanyName;

    @ExcelProperty(value = {"创建业务员门店渠道"}, index = 40)
    private String createdStoreChannelName;

    @ExcelProperty(value = {"创建业务员门店渠道细分"}, index = 41)
    private String createdStoreSubChannelCode;

    @ExcelProperty(value = {"创建业务员客户渠道"}, index = 42)
    private String createdChannelSubdivideName;

    @ExcelProperty(value = {"创建业务员客户"}, index = 43)
    private String createdDistributorName;

    @ExcelProperty(value = {"创建业务员门店编码"}, index = 44)
    private String createdStoreCode;

    @ExcelProperty(value = {"创建业务员门店"}, index = 45)
    private String createdStoreName;

    @ExcelProperty(value = {"创建业务员编码"}, index = 46)
    private String createdCode;

    @ExcelProperty(value = {"创建业务员姓名"}, index = 47)
    private String createdName;

    @ExcelProperty(value = {"创建人手机号（脱敏）"}, index = 48)
    private String tmCreatedPhone;

    @ExcelProperty(value = {"创建人岗位"}, index = 49)
    private String createdPosition;

    @ExcelProperty(value = {"所属业务员编码"}, index = 50)
    private String belongCode;

    @ExcelProperty(value = {"所属业务员姓名"}, index = 51)
    private String belongName;

    @ExcelProperty(value = {"所属业务员手机号（脱敏）"}, index = 52)
    private String tmBelongPhone;

    public static QueryExaminationKitchenDetailExportDTO toExportVO(QueryExaminationKitchenDetailOutDTO po) {
        QueryExaminationKitchenDetailExportDTO vo = new QueryExaminationKitchenDetailExportDTO();

        vo.setReportId(po.getReportId());
        vo.setProblemNum(po.getProblemNum());
        vo.setProblemList(po.getProblemList());
        vo.setTmNickName(po.getTmNickName());
        vo.setTmPhone(po.getTmPhone());
        vo.setAreaComb(po.getAreaComb());
        vo.setCommunityComb(po.getCommunityComb());
        vo.setCreatedDate(po.getCreatedDate());
        vo.setFq1(po.getFq1());
        vo.setFq2(po.getFq2());
        vo.setFq3(po.getFq3());
        vo.setFq4(po.getFq4());
        vo.setFq5(po.getFq5());
        vo.setFq6(po.getFq6());
        vo.setFq7(po.getFq7());
        vo.setFq8(po.getFq8());
        vo.setFq9(po.getFq9());
        vo.setFq10(po.getFq10());
        vo.setFq11(po.getFq11());
        vo.setFq12(po.getFq12());
        vo.setFq13(po.getFq13());
        vo.setFq14(po.getFq14());
        vo.setFq101(po.getFq101());
        vo.setFq102(po.getFq102());
        vo.setFq103(po.getFq103());
        vo.setFq15(po.getFq15());
        vo.setFq16(po.getFq16());
        vo.setFq17(po.getFq17());
        vo.setFq18(po.getFq18());
        vo.setFq201(po.getFq201());
        vo.setFq19(po.getFq19());
        vo.setFq301(po.getFq301());
        vo.setFq20(po.getFq20());
        vo.setFq401(po.getFq401());
        vo.setFq21(po.getFq21());
        vo.setFq22(po.getFq22());
        vo.setOtherSecurityRisks(po.getOtherSecurityRisks());
        vo.setComprehensiveDiagnosticAdvice(po.getComprehensiveDiagnosticAdvice());
        vo.setCreatedAreaName(po.getCreatedAreaName());
        vo.setCreatedCompanyName(po.getCreatedCompanyName());
        vo.setCreatedStoreChannelName(po.getCreatedStoreChannelName());
        vo.setCreatedStoreSubChannelCode(po.getCreatedStoreSubChannelCode());
        vo.setCreatedChannelSubdivideName(po.getCreatedChannelSubdivideName());
        vo.setCreatedDistributorName(po.getCreatedDistributorName());
        vo.setCreatedStoreCode(po.getCreatedStoreCode());
        vo.setCreatedStoreName(po.getCreatedStoreName());
        vo.setCreatedCode(po.getCreatedCode());
        vo.setCreatedName(po.getCreatedName());
        vo.setTmCreatedPhone(po.getTmCreatedPhone());
        vo.setCreatedPosition(po.getCreatedPosition());
        vo.setBelongCode(po.getBelongCode());
        vo.setBelongName(po.getBelongName());
        vo.setTmBelongPhone(po.getTmBelongPhone());
        return vo;
    }
}
