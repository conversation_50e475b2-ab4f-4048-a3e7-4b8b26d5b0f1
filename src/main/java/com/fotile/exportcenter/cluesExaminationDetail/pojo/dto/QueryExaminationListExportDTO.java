package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fotile.exportcenter.common.constant.CommonConstant;
import lombok.Data;

import java.util.Objects;

/**
 * 服务报告导出类
 */
@Data
public class QueryExaminationListExportDTO {

    @ExcelProperty(value = {"报告ID"}, index = 0)
    private Long id;

    @ExcelProperty(value = {"大区"}, index = 1)
    private String districtValue;

    @ExcelProperty(value = {"报告类型"}, index = 2)
    private String typeName;

    @ExcelProperty(value = {"分公司"}, index = 3)
    private String companyName;

    @ExcelProperty(value = {"客户名称"}, index = 4)
    private String distributorName;

    @ExcelProperty(value = {"客户渠道大类"}, index = 5)
    private String distributorChannelCategoryName;

    @ExcelProperty(value = {"门店"}, index = 6)
    private String storeCombName;

    @ExcelProperty(value = {"门店编码"}, index = 7)
    private String stroeCode;

    /**
     * 是否新店 1-是 2-否
     */
    @ExcelProperty(value = {"是否新店"}, index = 8)
    private String isNewStoreName;

    @ExcelProperty(value = {"创建业务员"}, index = 9)
    private String createdPerson;

    @ExcelProperty(value = {"创建业务员岗位"}, index = 10)
    private String createdPersonStationName;

    @ExcelProperty(value = {"所属业务员"}, index = 11)
    private String belongPerson;

    @ExcelProperty(value = {"手机号"}, index = 12)
    private String customerPhone;

    @ExcelProperty(value = {"姓名"}, index = 13)
    private String customerName;

    @ExcelProperty(value = {"省"}, index = 14)
    private String province;

    @ExcelProperty(value = {"市"}, index = 15)
    private String city;

    @ExcelProperty(value = {"区"}, index = 16)
    private String area;

    @ExcelProperty(value = {"小区ID"}, index = 17)
    private Long villageId;

    @ExcelProperty(value = {"小区"}, index = 18)
    private String villageName;

    /**
     * 线索/手机号（脱敏、非脱敏）
     */
    @ExcelProperty(value = {"线索"}, index = 19)
    private String cluesIdOrPhone;

    /**
     * 线索审核状态 40 未提交 1待审核 2审核通过 3审核不通过
     */
    @ExcelProperty(value = {"线索审核状态"}, index = 20)
    private String cluesAuditStatusName;

    @ExcelProperty(value = {"性别"}, index = 21)
    private String genderName;

    @ExcelProperty(value = {"房屋类型"}, index = 22)
    private String houseType;

    @ExcelProperty(value = {"用户来源"}, index = 23)
    private String cluesSourceValue;

    @ExcelProperty(value = {"关联工单号"}, index = 24)
    private String serviceOrderId;

    @ExcelProperty(value = {"关联好友"}, index = 25)
    private String customerNickname;

    @ExcelProperty(value = {"查看人数"}, index = 26)
    private int viewCount;

    @ExcelProperty(value = {"分享次数"}, index = 27)
    private int shareCount;

    @ExcelProperty(value = {"是否复检"}, index = 28)
    private String recheckFlagName;

    @ExcelProperty(value = {"报告生成时间"}, index = 29)
    private String createdDateStr;

    @ExcelProperty(value = {"烟机风量"}, index = 30)
    private String hoodBlowingRate;

    @ExcelProperty(value = {"tds值"}, index = 31)
    private String tds;

    @ExcelProperty(value = {"燃气是否漏气"}, index = 32)
    private String gasBlow;

    @ExcelProperty(value = {"灶具是否超龄使用"}, index = 33)
    private String gasOutPeriod;

    @ExcelProperty(value = {"热水器是否漏气"}, index = 34)
    private String heaterBlow;

    @ExcelProperty(value = {"热水器是否超龄使用"}, index = 35)
    private String heaterOutPeriod;

    @ExcelProperty(value = {"是否成为会员"}, index = 36)
    private String isVip;

    @ExcelProperty(value = {"是否领取权益"}, index = 37)
    private String isEquity;

    @ExcelProperty(value = {"是否成交"}, index = 38)
    private String isDeal;

    @ExcelProperty(value = {"成交时间"}, index = 39)
    private String auditDateStr;

    @ExcelProperty(value = {"门店渠道"}, index = 40)
    private String storeChannelName;

    @ExcelProperty(value = {"客户渠道"}, index = 41)
    private String distributorChannelCombName;

    public static QueryExaminationListExportDTO toExportVO(QueryExaminationListOutDto po) {
        QueryExaminationListExportDTO vo = new QueryExaminationListExportDTO();
        vo.setId(po.getId());
        vo.setDistrictValue(po.getDistrictValue());
        vo.setTypeName(po.getTypeName());
        vo.setCompanyName(po.getCompanyName());
        vo.setDistributorName(po.getDistributorName());
        vo.setDistributorChannelCategoryName(po.getDistributorChannelCategoryName());
        vo.setStoreCombName(po.getStoreCombName());
        vo.setStroeCode(po.getStroeCode());
        vo.setIsNewStoreName(po.getIsNewStoreName());
        vo.setCreatedPerson(po.getCreatedPerson());
        vo.setBelongPerson(po.getBelongPerson());
        vo.setCreatedPersonStationName(po.getCreatedPersonStationName());
        vo.setCustomerPhone(po.getCustomerPhone());
        vo.setCustomerName(po.getCustomerName());
        vo.setProvince(po.getProvince());
        vo.setCity(po.getCity());
        vo.setArea(po.getArea());
        vo.setVillageId(po.getVillageId());
        vo.setVillageName(po.getVillageName());
        vo.setCluesIdOrPhone(po.getCluesIdOrPhone());
        vo.setCluesAuditStatusName(po.getCluesAuditStatusName());
        vo.setGenderName(po.getGenderName());
        vo.setHouseType(po.getHouseType());
        vo.setCluesSourceValue(po.getCluesSourceValue());
        vo.setServiceOrderId(po.getServiceOrderId());
        vo.setCustomerNickname(po.getCustomerNickname());
        vo.setViewCount(po.getViewCount());
        vo.setShareCount(po.getShareCount());
        vo.setRecheckFlagName(po.getRecheckFlagName());
        vo.setCreatedDateStr(Objects.nonNull(po.getCreatedDate()) ? DateUtil.format(po.getCreatedDate(), CommonConstant.DateFormat.Y_M_D_HMS) : null);
        vo.setHoodBlowingRate(po.getHoodBlowingRate());
        vo.setTds(po.getTds());
        vo.setGasBlow(po.getGasBlow());
        vo.setGasOutPeriod(po.getGasOutPeriod());
        vo.setHeaterBlow(po.getHeaterBlow());
        vo.setHeaterOutPeriod(po.getHeaterOutPeriod());
        vo.setIsVip(po.getIsVip());
        vo.setIsEquity(po.getIsEquity());
        vo.setIsDeal(po.getIsDeal());
        vo.setAuditDateStr(Objects.nonNull(po.getAuditDate()) ? DateUtil.format(po.getAuditDate(), CommonConstant.DateFormat.Y_M_D_HMS) : null);
        vo.setStoreChannelName(po.getStoreChannelName());
        vo.setDistributorChannelCombName(po.getDistributorChannelCombName());

        return vo;
    }

}
