package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@Data
public class QueryExaminationListOutDto {
    private Long id;
    private Integer typeId;
    private String typeName;
    private String createdBy;
    private String chargeCode;

    @FieldEncrypt
    private String chargeUserName;
    @FieldEncrypt
    private String createdPerson;

    private String belongSalesmanId;

    @FieldEncrypt
    private String belongPerson;

    private Long cluesId;

    /**
     * 线索手机号
     */
    @FieldEncrypt
    private String cluesCustomerPhone;

    /**
     *所属大区编码
     */
    private String districtCode;
    /**
     *所属大区名称
     */
    private String districtValue;

    private Long companyId;
    private String companyName;
    private Long stroeId;
    private String stroeCode;
    private String stroeName;
    private String stroeFullName;
    /**
     * 门店简称
     */
    private String storeAbbreName;
    /**
     * 门店名称（优先简称，没有取名称）
     */
    private String storeCombName;
    /**
     * 是否新店 1-是 2-否
     */
    private String isNewStoreName;
    @FieldEncrypt
    private String customerPhone;
    @FieldEncrypt
    private String customerName;
    private String gender;
    private String genderName;
    private String houseType;
    private Long addressId;
    private String cluesSource;
    private String cluesSourceValue;
    private String checkCount;
    private Integer recheckFlag;
    private String recheckFlagName;
    private Date createdDate;

    /**
     * 新版厨房健康检测报告id
     */
    private Long kitchenHealthReportId;

    private int viewCount;

    private int shareCount;

    private String cluesIdOrPhone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 小区id
     */
    private Long villageId;

    /**
     * 小区
     */
    private String villageName;

    /**
     * 烟机风量
     */
    private String hoodBlowingRate;

    /**
     * tds值
     */
    private String tds;

    /**
     * 燃气是否漏气  （Ps：取灶具燃气是否漏气）
     */
    private String gasBlow;

    /**
     * 灶具是否超龄使用
     */
    private String gasOutPeriod;

    /**
     * 热水器是否漏气
     */
    private String heaterBlow;

    /**
     * 热水器是否超龄使用
     */
    private String heaterOutPeriod;

    /**
     * 是否成为会员
     */
    private String isVip;

    /**
     * 是否成交
     */
    private String isDeal;

    /**
     *  成交时间
     */
    private Date auditDate;

    /**
     * 是否领取权益
     */
    private String isEquity;

    /**
     * 门店渠道
     */
    private String storeChannelName;

    /**
     * 客户渠道
     */
    private String distributorChannelCombName;
    /**
     * 线索审核状态 40 未提交 1待审核 2审核通过 3审核不通过
     */
    private String cluesAuditStatus;

    /**
     * 线索审核状态 40 未提交 1待审核 2审核通过 3审核不通过
     */
    private String cluesAuditStatusName;

    /**
     * 创建人岗位
     */
    private String createdPersonStationName;

    /**
     * 关联工单号
     */
    private String serviceOrderId;

    /**
     * 关联好友昵称
     */
    private String customerNickname;

    /**
     * 客户名称
     */
    private String distributorName;

    /**
     * 客户渠道大类
     */
    private String distributorChannelCategoryName;
}
