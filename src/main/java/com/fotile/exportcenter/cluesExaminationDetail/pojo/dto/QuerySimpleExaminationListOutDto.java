package com.fotile.exportcenter.cluesExaminationDetail.pojo.dto;


import lombok.Data;

import java.io.Serializable;

/**
 * 性能优化：查询简单的报告列表，存在直接查询主表的条件时优先查询主表的id，线索id，创建人账号id，创建人业务员id
 *
 * <AUTHOR>
 */
@Data
public class QuerySimpleExaminationListOutDto implements Serializable {

    /**
     * 报告id
     */
    private Long id;

    /**
     * 线索id
     */
    private Long cluesId;

    /**
     * 创建人账号id，type = 1,2,3 报告为厨房健康报告、烟机清洗报告时有值
     */
    private String createdByAccountId;

    /**
     * 创建人业务员id，type = 4,报告为导购指南时有值
     */
    private Long createdBySalesmanId;

}
