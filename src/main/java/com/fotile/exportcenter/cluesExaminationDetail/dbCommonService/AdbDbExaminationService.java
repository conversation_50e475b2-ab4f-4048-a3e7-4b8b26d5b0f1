package com.fotile.exportcenter.cluesExaminationDetail.dbCommonService;

import com.fotile.exportcenter.cluesExaminationDetail.dao.TCluesExaminationDetailDao;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationKitchenDetailOutDTO;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListInDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QuerySimpleExaminationListOutDto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * adb-通用Service
 *
 * <AUTHOR>
 * @since 2022/9/5 005 10:28
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "adb")
//修改为6库
public class AdbDbExaminationService {

    @Autowired
    private TCluesExaminationDetailDao tCluesExaminationDetailDao;

    /**
     * 根据创建人所属业务员的分公司、门店、大区来查询
     *
     * @param inDto
     *     入参
     * @return 结果
     */
    public Long queryExaminationListCountUseSalesman(QueryExaminationListInDto inDto) {
        return tCluesExaminationDetailDao.queryExaminationListCountUseSalesman(inDto);
    }

    /**
     * 根据创建人所属业务员的分公司、门店、大区来查询
     *
     * @param inDto
     *     入参
     * @return 结果
     */
    public List<QueryExaminationListOutDto> queryExaminationExportListUseSalesman(QueryExaminationListInDto inDto, PageInfo pageInfo) {
        return tCluesExaminationDetailDao.queryExaminationExportListUseSalesman(inDto, pageInfo);
    }

    public List<QuerySimpleExaminationListOutDto> querySimpleExaminationListUseSalesman(QueryExaminationListInDto inDto) {
        return tCluesExaminationDetailDao.querySimpleExaminationListUseSalesman(inDto);
    }

    public Integer querySimpleExaminationCountUseSalesman(QueryExaminationListInDto inDto) {
        return tCluesExaminationDetailDao.querySimpleExaminationCountUseSalesman(inDto);
    }

    public List<QueryExaminationKitchenDetailOutDTO> queryExaminationKitchenDetailExportUseSalesman(QueryExaminationListInDto inDto, PageInfo pageInfo) {
        return tCluesExaminationDetailDao.queryExaminationKitchenDetailExportUseSalesman(inDto, pageInfo);
    }
}