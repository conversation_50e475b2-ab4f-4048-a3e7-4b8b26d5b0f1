package com.fotile.exportcenter.cluesExaminationDetail.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * 服务报告异步导出
 */
@Component
public interface CluesExaminationDetailExportChannel {
    /**
     * 异步导出消费
     */
    String CLUES_EXAMINATION_DETAIL_INPUT = "clues_examination_detail_input";

    /**
     * 异步导出生产
     */
//    String CLUES_EXAMINATION_DETAIL_OUTPUT = "clues_examination_detail_output";

    /**
     * 发送消息
     */
//    @Output(CLUES_EXAMINATION_DETAIL_OUTPUT)
//    MessageChannel sendExportMassage();

    /**
     * 消费消息
     */
    @Input(CLUES_EXAMINATION_DETAIL_INPUT)
    SubscribableChannel disposeExportInput();
}
