package com.fotile.exportcenter.cluesExaminationDetail.dao;

import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationKitchenDetailOutDTO;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListInDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QuerySimpleExaminationListOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindAttributeValueOutDto;
import com.fotile.framework.core.common.PageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 厨房健康体检从表(TCluesExaminationDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-11 09:56:53
 */
@Mapper
public interface TCluesExaminationDetailDao {

    //根据分公司orgid查询运营数据s
    List<FindAttributeValueOutDto> findttributeByCompanyIds(@Param("companyIds") List<Long> companyIds);

    Long queryExaminationListCountUseSalesman(@Param("inDto") QueryExaminationListInDto inDto);

    //查询服务报告导出列表
    List<QueryExaminationListOutDto> queryExaminationExportListUseSalesman(@Param("inDto") QueryExaminationListInDto inDto,
                                                                           @Param("pageInfo") PageInfo pageInfo);

    //查询健康服务详情导出列表
    List<QueryExaminationKitchenDetailOutDTO> queryExaminationKitchenDetailExportUseSalesman(@Param("inDto") QueryExaminationListInDto inDto,
                                                                                             @Param("pageInfo") PageInfo pageInfo);

    List<QuerySimpleExaminationListOutDto> querySimpleExaminationListUseSalesman(@Param("inDto") QueryExaminationListInDto inDto);

    Integer querySimpleExaminationCountUseSalesman(@Param("inDto") QueryExaminationListInDto inDto);
}