package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.exportcenter.client.*;
import com.fotile.exportcenter.marketing.dao.*;
import com.fotile.exportcenter.marketing.mapper.CluesDetailMapper;
import com.fotile.exportcenter.marketing.pojo.Dict;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.pojo.entity.*;
import com.fotile.exportcenter.marketing.pojo.vo.BaseAttributeVo;
import com.fotile.exportcenter.marketing.pojo.vo.QywxCustomerRelationVo;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import com.fotile.exportcenter.marketing.pojo.vo.SelectCluesFollowUpByCluesIdsVO;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.marketing.service.TAttributeLevelConfigService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.exportcenter.util.TimeUtils;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.Result;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("101")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesDetailExportImplService implements CluesExportImpl {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    private UserCluesDao userCluesDao;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserAddressClientService userAddressClientService;
    @Autowired
    private VillageClientService villageClientService;
    @Autowired
    private BaseAttributeMapper baseAttributeMapper;
    @Autowired
    private LabelMappingDao labelMappingDao;
    @Autowired
    private StoreClientService storeClientService;
    @Autowired
    private TAttributeLevelConfigService attributeLevelConfigService;
    @Autowired
    private CluesFollowUpBusinessDao cluesFollowUpBusinessDao;

    @Autowired
    private UserCluesAssistMappingMapper userCluesAssistMappingMapper;

    @Autowired
    private TQywxCustomerCluesRelationDao tQywxCustomerCluesRelationDao;

    @Autowired
    private TCluesDesignInfoDao cluesDesignInfoDao;

    @Autowired
    private SystemClient systemClient;

    @Autowired
    private TCluesLabelMappingMapper tCluesLabelMappingMapper;
    @Autowired
    WorkflowClient workflowClient;

    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        //log.error("type=101");
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count1 = 50000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count1;
        Integer lastCount = totalCount % count1;
        Integer size = 0;

        List<NewExportUserClusesOutDto> resultList = new ArrayList<>();
        List<Long> cluesIds = new ArrayList<>();
        log.error("查询线索详情导出时间开始-----"+System.currentTimeMillis());

        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count1;
            size = count1;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            List<Long> cluesDetail = userCluesDao.newQuerySalesLeadsIdListForExport(inDto);

            cluesIds.addAll(cluesDetail);
//            List<Long> ids = new ArrayList<>();
//            cluesDetail.stream().forEach(c -> ids.add(c.getId()));
//            List<NewExportUserClusesOutDto> ClusesOutDtos = userCluesDao.selectCluesCounts(ids);
//            countList.addAll(ClusesOutDtos);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);
        }
        if (CollectionUtils.isEmpty(cluesIds)) {
            throw new RuntimeException("未能查询到结果！");
        }
        List<SelectCluesFollowUpByCluesIdsVO> cluesFollowUpMaps = new CopyOnWriteArrayList<>();
        List<CluesFollowUp> cluesFollowUps = new CopyOnWriteArrayList<>();
        List<CluesServiceInfoDto> cluesServiceList = new CopyOnWriteArrayList<>();


        List<CompletableFuture> completableFutureList = new CopyOnWriteArrayList<>();
        List<CompletableFuture> followFutureList = new CopyOnWriteArrayList<>();
        List<CompletableFuture> cluesFollowFutureList = new CopyOnWriteArrayList<>();
        List<CompletableFuture> cluesServiceFutureList = new CopyOnWriteArrayList<>();


        List<UserCluesAdvertiseSource> advertiseSourceList = new CopyOnWriteArrayList<>();
        List<CompletableFuture> advertiseSourceFutureList = new CopyOnWriteArrayList<>();
        Integer size1 = cluesIds.size();

        log.error("查询线索详情导出时间结束-----" + System.currentTimeMillis());
        Integer total = size1 / 1000;
        Integer remainder = size1 % 1000;
        Integer pageSize = 1000;

        for (Integer cluesIdx = 0; cluesIdx <= total; cluesIdx++) {


            List<Long> collect = null;
            if (Objects.equals(cluesIdx, total)) {
                if (remainder == 0) {
                    continue;
                }
                collect = cluesIds.stream().skip((cluesIdx) * pageSize).limit(remainder).collect(Collectors.toList());
            } else {
                collect = cluesIds.stream().skip((cluesIdx) * pageSize).limit(pageSize).collect(Collectors.toList());
            }
            List<Long> finalCollect = collect;
            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return userCluesDao.newQuerySalesLeadsListForExportByIds(finalCollect);
                } catch (Exception e) {
                    log.error("错误日志导出详情,{}",e.getMessage(),e);
                    return null;
                }

            }).handle((result, exception) -> {
                if (result != null) {
                    resultList.addAll(result);
                } else if (exception != null) {
                    // 这里可以处理异常，如果你需要的话
                    log.error("在处理结果时遇到异常", exception);
                }
                // 返回 null 或其他适当的值，表示这个 handle 操作的结果
                // 由于我们不需要进一步的结果处理，所以返回 null 是可以的
                return null;
            }));
            followFutureList.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return userCluesDao.selectCluesFollowUpByCluesIds(finalCollect);
                } catch (Exception e) {
                    log.error("错误日志导出详情,{}",e.getMessage(),e);
                    return null;
                }

            }).handle((result, exception) -> {
                if (result != null) {
                    cluesFollowUpMaps.addAll(result);
                } else if (exception != null) {
                    // 这里可以处理异常，如果你需要的话
                    log.error("在处理结果时遇到异常", exception);
                }
                // 返回 null 或其他适当的值，表示这个 handle 操作的结果
                // 由于我们不需要进一步的结果处理，所以返回 null 是可以的
                return null;
            }));

            cluesServiceFutureList.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return userCluesDao.getCluesServiceByCluesIds(finalCollect);
                } catch (Exception e) {
                    log.error("错误日志导出详情,{}",e.getMessage(),e);
                    return null;
                }


            }).handle((result, exception) -> {
                if (result != null) {
                    cluesServiceList.addAll(result);
                } else if (exception != null) {
                    // 这里可以处理异常，如果你需要的话
                    log.error("在处理结果时遇到异常", exception);
                }
                // 返回 null 或其他适当的值，表示这个 handle 操作的结果
                // 由于我们不需要进一步的结果处理，所以返回 null 是可以的
                return null;
            }));


            advertiseSourceFutureList.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return userCluesDao.getUserCluesAdvertiseSourceByCluesIds(finalCollect);
                } catch (Exception e) {
                    log.error("错误日志导出详情,{}",e.getMessage(),e);
                    return null;
                }


            }).handle((result, exception) -> {
                if (result != null) {
                    advertiseSourceList.addAll(result);
                } else if (exception != null) {
                    // 这里可以处理异常，如果你需要的话
                    log.error("在处理结果时遇到异常", exception);
                }
                // 返回 null 或其他适当的值，表示这个 handle 操作的结果
                // 由于我们不需要进一步的结果处理，所以返回 null 是可以的
                return null;
            }));

        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutureList.toArray(CompletableFuture[]::new));
        CompletableFuture<Void> followFuture = CompletableFuture.allOf(followFutureList.toArray(CompletableFuture[]::new));
        CompletableFuture<Void> cluesFuture = CompletableFuture.allOf(cluesServiceFutureList.toArray(CompletableFuture[]::new));

        CompletableFuture<Void> advertiseFuture = CompletableFuture.allOf(advertiseSourceFutureList.toArray(CompletableFuture[]::new));

        allFutures.join();
        followFuture.join();
        cluesFuture.join();
        advertiseFuture.join();

        if (resultList == null || resultList.size() <= 0) {
            throw new RuntimeException("未能查询到结果！");
        }
            if (resultList.size() != cluesIds.size()){
                List<Long> missingCluesId = new CopyOnWriteArrayList<>();
                //查找cluesFollowFutureList中不存在的数据
                for (Long c : cluesIds) {
                    if (resultList.stream().map(NewExportUserClusesOutDto::getId).noneMatch(c::equals)) {
                        missingCluesId.add(c);
                    }
                }
                log.error("执行线索失败缺失数据missingCluesId:{}",missingCluesId);
                List<NewExportUserClusesOutDto> newExportUserClusesOutDto = userCluesDao.newQuerySalesLeadsListForExportByIds(missingCluesId);
                if (CollectionUtils.isNotEmpty(newExportUserClusesOutDto)){

                    resultList.addAll(newExportUserClusesOutDto);
                }
                if (CollectionUtils.isNotEmpty(cluesServiceList)) {
                    missingCluesId = new CopyOnWriteArrayList<>();
                    for (Long c : cluesIds) {
                        if (cluesServiceList.stream().map(CluesServiceInfoDto::getId).noneMatch(c::equals)) {
                            missingCluesId.add(c);
                        }
                    }
                    log.error("执行线索服务失败缺失数据missingCluesId:{}", missingCluesId);
                    if (CollectionUtils.isNotEmpty(missingCluesId)) {
                        List<CluesServiceInfoDto> cluesServiceByCluesIds = userCluesDao.getCluesServiceByCluesIds(missingCluesId);
                        if (CollectionUtils.isNotEmpty(cluesServiceByCluesIds)) {
                            cluesServiceList.addAll(cluesServiceByCluesIds);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(cluesFollowUpMaps)) {
                    missingCluesId = new CopyOnWriteArrayList<>();
                    for (Long c : cluesIds) {
                        if (cluesFollowUpMaps.stream().map(SelectCluesFollowUpByCluesIdsVO::getClueRelatedId).noneMatch(c::equals)) {
                            missingCluesId.add(c);
                        }
                    }
                    log.error("执行跟进失败缺失数据missingCluesId:{}", missingCluesId);
                    if(CollectionUtils.isNotEmpty(missingCluesId)) {
                        List<SelectCluesFollowUpByCluesIdsVO> selectCluesFollowUpByCluesIdsVOS = userCluesDao.selectCluesFollowUpByCluesIds(missingCluesId);
                        if (CollectionUtils.isNotEmpty(selectCluesFollowUpByCluesIdsVOS)) {
                            cluesFollowUpMaps.addAll(selectCluesFollowUpByCluesIdsVOS);
                        }
                    }
                }
            }


        int size2 = resultList.size();
        Integer relatedTotal = size2 / 1000;
        int relatedRemainder = size2 % 1000;
        Integer relatedPageSize = 1000;

        for (Integer cluesIdx = 0; cluesIdx <= total; cluesIdx++) {

            List<Long> collect = null;
            if (Objects.equals(cluesIdx, relatedTotal)) {
                if (remainder == 0) {
                    continue;
                }
                collect = resultList.stream().skip((cluesIdx) * relatedPageSize).limit(relatedRemainder).map(NewExportUserClusesOutDto::getId).collect(Collectors.toList());
            } else {
                collect = resultList.stream().skip((cluesIdx) * relatedPageSize).limit(relatedPageSize).map(NewExportUserClusesOutDto::getId).collect(Collectors.toList());
            }
            List<Long> finalCollect = collect;
            if (CollectionUtils.isEmpty(finalCollect)) {
                continue;
            }
            cluesFollowFutureList.add(CompletableFuture.supplyAsync(() -> {
                QueryWrapper<CluesFollowUp> cluesFollowUpQueryWrapper
                        = new QueryWrapper<CluesFollowUp>()
                        .in("clue_related_id", finalCollect)
                        .eq("audit_status", 20)
                        .eq("is_deleted", 0)
                        .and(q ->
                                q.ne("title", "自然进店").or()
                                        .isNull("title")
                        );

                return cluesFollowUpBusinessDao.selectList(cluesFollowUpQueryWrapper);
            }).thenAcceptAsync(cluesFollowUps::addAll));
        }
        CompletableFuture<Void> cluesserviceList = CompletableFuture.allOf(cluesServiceFutureList.toArray(CompletableFuture[]::new));
        cluesserviceList.join();


        //排序根据sourceId 正序 再createdDate 倒叙
        if (StringUtils.isNotBlank(inDto.getVisitSort())) {
            if ("desc".equals(inDto.getVisitSort())) {
                resultList.sort(Comparator.comparing(NewExportUserClusesOutDto::getVisitTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()
                        .thenComparing(NewExportUserClusesOutDto::getFundingTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
            } else {
                resultList.sort(Comparator.comparing(NewExportUserClusesOutDto::getVisitTime, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(NewExportUserClusesOutDto::getFundingTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
            }
        }else{
            resultList.sort(Comparator.comparing(NewExportUserClusesOutDto::getFundingTime).reversed());
        }


        //循环线索，获取需要的数据，并去重
        Set<Long> idsSet = new HashSet<>();
        Set<Long> companyIds1Set = new HashSet<>();
        Set<String> channelCodesSet = new HashSet<>();
        Set<String> radioCodesSet = new HashSet<>();
        Set<Long> addressIdsSet = new HashSet<>();
        Set<Long> villageIdsSet = new HashSet<>();
        HashSet<Long> storeIdSet = new HashSet<Long>();
        Set<Long> oldUserSet = new HashSet<>();
        List<DecorateAndDesignerDto> decorateList = new ArrayList<>();
        List<QueryByParamsOutDto> queryByParamsOutDtoList = systemClient.queryByType("diffIndustry_company_class", 1, 100).getData().getRecords();
        List<Dict> allSubChannels = Optional.ofNullable(workflowClient.typeCode("sub_channel_code"))
                .filter(Result::getSuccess)
                .map(Result::getData)
                .orElse(Lists.newArrayListWithExpectedSize(0));
        Map<Long, Dict> subChannelIdMap = allSubChannels.stream().collect(Collectors.toMap(Dict::getId, Function.identity()));
        Dict defaultDict = allSubChannels.stream()
                .filter(d -> org.apache.commons.lang3.StringUtils.isBlank(d.getValueCode()))
                .findAny()
                .orElseGet(() -> {
                    Dict dict = new Dict();
                    dict.setValueCode("");
                    dict.setValueName("缺省");
                    return dict;
                });
        ImmutableMap<String, Map<String, Dict>> channelSubMap = getChannelSubMap();
        List<QueryByParamsOutDto> designer_industry_level = systemClient.queryByType("designer_industry_level", 1, 100).getData().getRecords();

        for (NewExportUserClusesOutDto clusesOutDto : resultList) {
            if (StringUtils.isNotEmpty(clusesOutDto.getDesignerClubMemberGrade())){
                if (clusesOutDto.getDesignerClubMemberGrade().equals("1")) {
                    clusesOutDto.setDesignerClubMemberGradeName("注册设计师");
                }else{
                    clusesOutDto.setDesignerClubMemberGradeName("认证设计师");
                }
            }
            if (CollectionUtils.isNotEmpty(designer_industry_level)) {
                if (StringUtils.isNotEmpty(clusesOutDto.getDesignerIndustryLevel())){
                    QueryByParamsOutDto queryByParamsOutDto = designer_industry_level.stream().filter(t -> t.getValueCode().equals(clusesOutDto.getDesignerIndustryLevel())).findFirst().orElse(null);
                    if (queryByParamsOutDto != null) {
                        clusesOutDto.setDesignerIndustryLevelName(queryByParamsOutDto.getValueName());
                    }
                }
            }

            Map<String, Dict> subChannelMap = channelSubMap.getOrDefault(clusesOutDto.getStoreChannelCode(),Map.of("",defaultDict));

            clusesOutDto.setStoreSubChannelName(getFullSubStoreChannelName(subChannelIdMap,subChannelMap,clusesOutDto.getStoreSubChannelCode()));
            if (StringUtils.isNotEmpty(clusesOutDto.getSalesman())){
                clusesOutDto.setFrontSalesman(null);
            }
            if (!ObjectUtils.isEmpty(clusesOutDto.getKitchenDesignFlag())){
                clusesOutDto.setKitchenDesignFlagName(clusesOutDto.getKitchenDesignFlag() == 0 ?"否":"是");
            }
            if (!ObjectUtils.isEmpty(clusesOutDto.getCostumeChangeReportFlag())){
                clusesOutDto.setCostumeChangeReportFlagName(clusesOutDto.getCostumeChangeReportFlag() == 0 ?"否":"是");
            }
            if (!ObjectUtils.isEmpty(clusesOutDto.getFreehandSketchingFlag())){
                clusesOutDto.setFreehandSketchingFlagName(clusesOutDto.getFreehandSketchingFlag() == 0 ?"否":"是");
            }
            //未跟进时间 -- 结束
            //issue28427 修改此部分代码
            String decorateDesignerJson = clusesOutDto.getDecorateDesignerJson();
            //decorateDesignerJson = getDecorateDesignerJson(decorateDesignerJson);
            DecorateAndDesignerDto decorateAndDesignerDto = getDecorateDTO(decorateDesignerJson);
            if (decorateAndDesignerDto != null) {
                clusesOutDto.setDecorateAndDesignerType(decorateAndDesignerDto.getType());
                if (decorateAndDesignerDto.getType() == 3) {
                    clusesOutDto.setDecorateAndDesignerTypeName("家装设计师");
                    clusesOutDto.setDecorateAndDesignerCode(decorateAndDesignerDto.getDesignerCode());
                } else {
                    if (decorateAndDesignerDto.getType() == 1) {
                        clusesOutDto.setDecorateAndDesignerTypeName("家装公司");
                    } else {
                        if (StringUtils.isNotBlank(decorateAndDesignerDto.getDecorateCategory())) {
                            clusesOutDto.setDecorateAndDesignerTypeName(getCategoryName(decorateAndDesignerDto.getDecorateCategory(), queryByParamsOutDtoList));
                        }
                    }
                    clusesOutDto.setDecorateAndDesignerCode(decorateAndDesignerDto.getCode());
                }
                decorateList.add(decorateAndDesignerDto);
            }
            if (StringUtils.isEmpty(clusesOutDto.getSalesman())
                    && !ObjectUtils.isEmpty(clusesOutDto.getStoreId())
                    && clusesOutDto.getStoreId() != 0L
                    && !ObjectUtils.isEmpty(clusesOutDto.getCompanyId())
                    && clusesOutDto.getCompanyId() != 0L
                    && !StringUtils.isEmpty( clusesOutDto.getAuditStatus())
                    && "审核通过".equals(clusesOutDto.getAuditStatus())
                    && !StringUtils.isEmpty(clusesOutDto.getStatus())
                    && ("已成交".equals(clusesOutDto.getStatus())
                    || "已分配".equals(clusesOutDto.getStatus())
                    || "已跟进".equals(clusesOutDto.getStatus()))
            ) {
                clusesOutDto.setOpenSeaflag("是");
            } else {
                clusesOutDto.setOpenSeaflag("否");
            }
            if (!ObjectUtils.isEmpty(clusesOutDto.getCustomerBringType())) {
                /**
                 * 客情带单类型 1.门店员工亲戚朋友
                 * 2.老板亲戚朋友
                 * 3.办事处员工亲戚朋友
                 * 4.总部优惠券（仅慈溪、宁波）
                 */
                if (clusesOutDto.getCustomerBringType() == 1) {
                    clusesOutDto.setCustomerBringTypeValue("门店员工亲戚朋友");
                } else if (clusesOutDto.getCustomerBringType() == 2) {
                    clusesOutDto.setCustomerBringTypeValue("老板亲戚朋友");
                } else if (clusesOutDto.getCustomerBringType() == 3) {
                    clusesOutDto.setCustomerBringTypeValue("办事处员工亲戚朋友");
                } else if (clusesOutDto.getCustomerBringType() == 4) {
                    clusesOutDto.setCustomerBringTypeValue("总部优惠券（仅慈溪、宁波）");
                }
            }
            //clusesOutDto.setDecorateDesignerJson(decorateDesignerJson);
            String communityMemberJson = clusesOutDto.getCommunityMemberJson();
            communityMemberJson = getCommunityMemberJson(communityMemberJson);
            clusesOutDto.setCommunityMemberJson(communityMemberJson);
            if (clusesOutDto.getId() != null) {
                idsSet.add(clusesOutDto.getId());
            }
            if (clusesOutDto.getCompanyId() != null) {
                companyIds1Set.add(clusesOutDto.getCompanyId());
            }
            if (StringUtils.isNotEmpty(clusesOutDto.getChannelCode())) {
                channelCodesSet.add(clusesOutDto.getChannelCode());
            }
            if (StringUtils.isNotEmpty(clusesOutDto.getRadioCode())) {
                radioCodesSet.add(clusesOutDto.getRadioCode());
            }
            if (clusesOutDto.getAddressId() != null) {
                addressIdsSet.add(clusesOutDto.getAddressId());
            }
            if (clusesOutDto.getVillageId() != null) {
                villageIdsSet.add(clusesOutDto.getVillageId());
            }
            if (clusesOutDto.getStoreId() != null) {
                storeIdSet.add(clusesOutDto.getStoreId());
            }
            if (clusesOutDto.getWithSingleOldUserId() != null) {
                oldUserSet.add(clusesOutDto.getWithSingleOldUserId());
            }
        }

        //set转换list
        List<Long> ids = new ArrayList<>(idsSet);
        List<Long> companyIds1 = new ArrayList<>(companyIds1Set);
        List<String> channelCodes = new ArrayList<>(channelCodesSet);
        List<String> radioCodes = new ArrayList<>(radioCodesSet);
        List<Long> addressIds = new ArrayList<>(addressIdsSet);
        List<Long> villageIds = new ArrayList<>(villageIdsSet);
        List<Long> storeIdList = new ArrayList<>(storeIdSet);
        List<Long> oldUserList = new ArrayList<>(oldUserSet);

        //根据线索 id 统计跟进条数
        //   ('1','2','3','4','5')  followUpCount, type = '1' teleConnectionCount type = '2' weChatConnectionCount type = '3' visitConnectionCount type = '5' intoShopConnectionCount
//        log.error("查询线索详情导出-日志时间开始-----",System.currentTimeMillis());
//

        log.error("查询线索详情导出-日志时间结束-----",System.currentTimeMillis());
        //issue28427 修改此部分代码



//        Map<Long, List<CluesFollowUp>> cluesFollowUpMap = cluesFollowUpList.stream().collect(Collectors.groupingBy(CluesFollowUp::getClueRelatedId));
        //issue28427 修改此部分代码
//        for (NewExportUserClusesOutDto clusesOutDto : resultList) {
//            List<CluesFollowUp> cluesFollowUps = cluesFollowUpMap.get(clusesOutDto.getId());
//            if (CollectionUtils.isNotEmpty(cluesFollowUps)) {
//                clusesOutDto.setFollowUpCount(cluesFollowUps.stream().filter(x -> x.getType().equals("1")
//                        || x.getType().equals("2")
//                        || x.getType().equals("3")
//                        || x.getType().equals("4")
//                        || x.getType().equals("5")).count());
//                clusesOutDto.setTeleConnectionCount(cluesFollowUps.stream().filter(x -> x.getType().equals("1")).count());
//                clusesOutDto.setWeChatConnectionCount(cluesFollowUps.stream().filter(x -> x.getType().equals("2")).count());
//                clusesOutDto.setVisitConnectionCount(cluesFollowUps.stream().filter(x -> x.getType().equals("3")).count());
//                clusesOutDto.setIntoShopConnectionCount(cluesFollowUps.stream().filter(x -> x.getType().equals("5")).count());
//            }
//        }

//        ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id
//        and cfu.type in ('1','2','3','4','5'))
//        followUpCount,
//                ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND
//        cfu.type = '1' ) teleConnectionCount,
//                ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND
//        cfu.type = '2' ) weChatConnectionCount,
//                ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND
//        cfu.type = '3' ) visitConnectionCount,
//                ( SELECT count( 1 ) FROM marketingcenter.clues_follow_up cfu WHERE cfu.clue_related_id = sl.id AND
//        cfu.type = '5' ) intoShopConnectionCount,


        if (ids != null && ids.size() > 0) {
            List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = new ArrayList<>();
            //获取分公司未跟进时间
            FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
            findCompanyByIdsInDto.setIdList(companyIds1);
            try {
                findCompanyAreaByOrgIdsOutDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
            } catch (Exception e) {
                log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
            }
            //查询门店信息
            List<FindStoreByOrgIdOutDto> storeList = new ArrayList<>();
            if (storeIdList != null && storeIdList.size() > 0) {
                storeList = findByOrgIds(storeIdList);
            }
            //issue28427 增加查询
            List<DecorateAndDesignerDto> decorateResultList = new ArrayList<>();
            if (decorateList != null && decorateList.size() > 0) {
                decorateResultList = getDecorateList(decorateList);
            }

            //定义返回参数的变量
            List<ChannelEntity> channelList = null;
            //渠道名称
            if (channelCodes != null && channelCodes.size() > 0) {
                channelList = orgClient.findByChannelCodes(channelCodes).getData();
            }
            //频道
            List<FindRadioAndChannelByCodesOutDto> radioList = null;
            if (radioCodes != null && radioCodes.size() > 0) {
                radioList = orgClient.findByRadioCodes2(radioCodes).getData();
            }
            List<DiffIndustryEntity> entityList = null;
            if (oldUserList != null && oldUserList.size() > 0) {
                entityList = orgClient.findAllByIds(oldUserList).getData();
            }


            //地址信息
            List<UserAddressOutDto> addressList = null;
            if (addressIds != null && addressIds.size() > 0) {
                addressList = selectUserAddressByIdList(addressIds);
//                addressList = userAddressClientService.selectUserAddressByIdList2(addressIds).getData();
            }
            //查询所有小区名称
            List<FindVillageByIdOutDto> villageList = null;
            if (villageIds != null && villageIds.size() > 0) {
                villageList = findVillageByVillageIds(villageIds);
//                FindVillageByVillageIdsInDto findVillageByVillageIdsInDto = new FindVillageByVillageIdsInDto();
//                findVillageByVillageIdsInDto.setVillageIds(villageIds);
//                villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto).getData();
                log.info("villageIds+++++++++++++++++++:" + villageList.toString());
            }
            //查询备注标签
            List<TCluesLabelMappingEntity> tCluesLabelMappingEntities = tCluesLabelMappingMapper.selectListByCluesIds(ids);
            Map<Long, List<TCluesLabelMappingEntity>> cluesLabelMap = tCluesLabelMappingEntities.stream().collect(Collectors.groupingBy(TCluesLabelMappingEntity::getCluesId));
            //查询所有线索标签
            List<FindLabelBySourceIdOutDto> labelList = findLabelNameByIds(ids, "user_clues");
//            List<FindLabelBySourceIdOutDto> labelList = labelMappingDao.findLabelNameByIds(ids, "user_clues");
            // 获取所有ttributeValues
            List<String> fields = setFieldIdsForClues();
            List<BaseAttributeVo> findAttributeValueOutDtos = baseAttributeMapper.getAttributeByFieldIds(fields);

            List<String> configFieldIds = setFieldIds();
            List<SelectAllByFiledIdVO> selectAllByFiledIdVOS = attributeLevelConfigService.findByFiledIds(configFieldIds);

            //20243:线索列表-导出字段新增 added by zhongdian, 2022/5/30
//            Set<Long> assistUserIdSet = resultList.stream()
//                    .filter(x -> StringUtils.isNotBlank(x.getAssistUserId()))
//                    .map(x -> Long.valueOf(x.getAssistUserId()))
//                    .collect(Collectors.toSet());
//            //线索协助人信息列表
//            List<FindSalesmanSimpleInfoOutDto> assistUserList = null;
//            if (CollectionUtils.isNotEmpty(assistUserIdSet)) {
//                assistUserList = orgClient.getSalesmanSimpleInfoList(new FindSalesmanSimpleInfoInDto().setIdList(assistUserIdSet)).getData();
//            }
//            if (CollectionUtils.isEmpty(assistUserList)) {
//                assistUserList = new ArrayList<>();
//            }
//            FindSalesmanSimpleInfoOutDto assistUser = null;

            //0301迭代，28734，协助人多选，同步修改此处
            List<UserCluesAssistMapping> assistMappings = userCluesAssistMappingMapper.selectListByCluesIds(ids);
            //根据线索id分组
            Map<Long, List<UserCluesAssistMapping>> groupedMap = new HashMap<>();
            if (assistMappings != null && assistMappings.size() > 0) {
                groupedMap = assistMappings.stream().collect(Collectors.groupingBy(UserCluesAssistMapping::getCluesId));
            }

            //0426迭代，issue：31676
            List<TQywxCustomerCluesRelation> relationList = tQywxCustomerCluesRelationDao.getCustomerCluesBycluesIds(ids);
            //23916 查询企微用户微信昵称
            List<String> externalUserList = relationList.parallelStream()
                    .map(TQywxCustomerCluesRelation::getExternalUserid)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<String,String> qywxCustomerRelationMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(externalUserList)){
                List<QywxCustomerRelationVo> qywxCustomerRelations = tQywxCustomerCluesRelationDao.queryQywxCustomerRelationByexternalUserid(externalUserList);
                if (CollectionUtils.isNotEmpty(qywxCustomerRelations)){
                    qywxCustomerRelationMap= qywxCustomerRelations.parallelStream()
                            .collect(Collectors.toMap(QywxCustomerRelationVo::getExternalUserid, QywxCustomerRelationVo::getName));
                }
            }
            List<TCluesDesignInfo> tCluesDesignInfoList = cluesDesignInfoDao.queryByCluesIds(ids);


            List<SelectAllByFiledIdVO> selectAllByFiledIdVOs = attributeLevelConfigService.findByFiledIds(Arrays.asList("follow_decorate_progres", "review_status"));

            Map<Long, List<UserCluesAdvertiseSource>> groupedAdvertiseMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(advertiseSourceList)) {
                groupedAdvertiseMap = advertiseSourceList.stream().collect(Collectors.groupingBy(UserCluesAdvertiseSource::getUserCluesId));
            }


            for (int r = 0; r < resultList.size(); r++) {

                NewExportUserClusesOutDto userCluesDto = resultList.get(r);
                if (CollectionUtils.isNotEmpty(cluesFollowUpMaps)) {
                    SelectCluesFollowUpByCluesIdsVO cluesFollowUpMap = cluesFollowUpMaps.stream().filter(map -> userCluesDto.getId().equals(map.getClueRelatedId())).findFirst().orElse(null);
                    if (cluesFollowUpMap != null) {
                        userCluesDto.setFollowUpCount(cluesFollowUpMap.getFollowUpCount());
                        userCluesDto.setTeleConnectionCount(cluesFollowUpMap.getTeleConnectionCount() );
                        userCluesDto.setWeChatConnectionCount(cluesFollowUpMap.getWeChatConnectionCount());
                        userCluesDto.setVisitConnectionCount(cluesFollowUpMap.getVisitConnectionCount());
                        userCluesDto.setIntoShopConnectionCount(cluesFollowUpMap.getIntoShopConnectionCount());
                    }
                }
                userCluesDto.setIndex(r + 1);
                //计算人工写跟进条数
                if (CollectionUtils.isNotEmpty(cluesFollowUps)) {
                    Set<CluesFollowUp> collect = cluesFollowUps.stream().filter(s -> s.getClueRelatedId().equals(userCluesDto.getId())).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        if (!collect.isEmpty()) {
                            userCluesDto.setArtificialFollowUpCount(collect.size());
                            userCluesDto.setArtificialFollowUpFlag("是");
                        }
                    }
                }
                try {
                    //统计数
//                    if (countList != null && countList.size() > 0){
//                        NewExportUserClusesOutDto countOut = countList.stream().filter(c -> c.getId().equals(userCluesDto.getId())).findFirst().orElse(null);
//                        userCluesDto.setFollowUpCount(countOut != null ? countOut.getFollowUpCount() : 0);
//                        userCluesDto.setTeleConnectionCount(countOut != null ? countOut.getTeleConnectionCount() : 0);
//                        userCluesDto.setWeChatConnectionCount(countOut != null ? countOut.getWeChatConnectionCount() : 0);
//                        userCluesDto.setVisitConnectionCount(countOut != null ? countOut.getVisitConnectionCount() : 0);
//                        userCluesDto.setIntoShopConnectionCount(countOut != null ? countOut.getIntoShopConnectionCount() : 0);
//                    }
                    //未跟进时间 -- 开始
                    FindCompanyAreaByOrgIdsOutDto findCompanyAreaByOrgIdsOutDto = findCompanyAreaByOrgIdsOutDtos.stream().filter(s -> s.getOrgId() != null && s.getOrgId().equals(userCluesDto.getCompanyId())).findAny().orElse(null);
                    if (findCompanyAreaByOrgIdsOutDto != null) {
                        userCluesDto.setDistrictCode(findCompanyAreaByOrgIdsOutDto.getValueCode());
                        userCluesDto.setDistrictValue(findCompanyAreaByOrgIdsOutDto.getValueName());
                    }
                    if (userCluesDto.getReviseTime() != null) {
                        String start = sdf.format(new Date());
                        String end = sdf.format(userCluesDto.getReviseTime() == null ? new Date() : userCluesDto.getReviseTime());
                        Long number = (sdf.parse(start).getTime() - sdf.parse(end).getTime()) / (1000 * 3600 * 24);
                        userCluesDto.setFollowUpNoDate(number.toString());
                    }
                    //未跟进时间
                    if (userCluesDto.getLastFollowTime() != null || userCluesDto.getAuditTime() != null) {
                        SimpleDateFormat sdfhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                        String start = sdfhms.format(new Date());
                        String end = sdfhms.format(userCluesDto.getLastFollowTime() == null ? userCluesDto.getFundingTime() : userCluesDto.getLastFollowTime());
                        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
                            userCluesDto.setUnFollowDate("0");
                        } else {
                            try {
                                Long number = (sdfhms.parse(start).getTime() - sdfhms.parse(end).getTime()) / (1000 * 3600 * 24);

//                                Long count = 0L;
//                                if (findCompanyAreaByOrgIdsOutDto != null) {
//                                    count = findCompanyAreaByOrgIdsOutDto.getCluesRemind() != null ? Long.valueOf(findCompanyAreaByOrgIdsOutDto.getCluesRemind()) : 0L;
//                                }
//                                if (count > number) {
//                                    userCluesDto.setUnFollowDate("0");
//                                } else {
                                userCluesDto.setUnFollowDate(number.toString());
//                                }
                            } catch (Exception e) {
                                userCluesDto.setUnFollowDate("0");
                                log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
                            }
                        }
                    } else {
                        userCluesDto.setUnFollowDate("0");
                    }

                    if (userCluesDto.getActivityId() != null) {
                        userCluesDto.setActivityId1("D" + userCluesDto.getActivityId());
                    }
                    log.debug("线索id:" + userCluesDto.getId());
                    //根据渠道编码获取渠道名称
                    if (StringUtils.isNotEmpty(userCluesDto.getChannelCode())) {
                        ChannelEntity target = channelList.stream().filter(bean -> bean.getCode().equals(userCluesDto.getChannelCode())).findFirst().orElse(null);
                        userCluesDto.setChannelName(target != null ? target.getName() : null);
                    }
                    if (StringUtils.isNotEmpty(userCluesDto.getRadioCode())) {
                        FindRadioAndChannelByCodesOutDto target = radioList.stream().filter(bean -> bean.getRadioCode().equals(userCluesDto.getRadioCode())).findFirst().orElse(null);
                        userCluesDto.setRadioName(target != null ? target.getRadioName() : null);
                    }
                    if (userCluesDto.getWithSingleOldUserId() != null) {
                        DiffIndustryEntity diffIndustryEntity = entityList.stream().filter(bean -> bean.getId().equals(userCluesDto.getWithSingleOldUserId())).findFirst().orElse(null);
                        userCluesDto.setWithSingleOldUserCode(diffIndustryEntity != null ? diffIndustryEntity.getCode() : null);
                    }
                    if (userCluesDto.getVillageId() != null) {
                        FindVillageByIdOutDto target = villageList.stream().filter(bean -> bean.getId().equals(userCluesDto.getVillageId())).findFirst().orElse(null);
                        userCluesDto.setVillage(target != null ? target.getName() : null);
                    }
                    if (userCluesDto.getAddressId() != null) {
                        UserAddressOutDto target = addressList.stream().filter(bean -> bean.getId().equals(userCluesDto.getAddressId())).findFirst().orElse(null);
                        if (target != null) {
                            //脱敏隐藏详细地址
                            userCluesDto.setAddress("1".equals(inDto.getFlag()) ? "" : target.getAddress());
                            userCluesDto.setUnit(target.getUnit());
                            userCluesDto.setHouseNumber(target.getHouseNumber());
                            userCluesDto.setBuilding(target.getBuilding());
                            if (target.getCityName() != null && target.getProvinceName() != null && target.getCountyName() != null) {
                                //省市区
                                userCluesDto.setCpc(target.getProvinceName() + target.getCityName() + target.getCountyName());
                            }
                            if (target.getHouseType() != null) {
                                if ("-1".equals(target.getHouseType().toString())) {
                                    userCluesDto.setHouseModel(userCluesDto.getHouseType());
                                    userCluesDto.setHouseType(null);
                                } else {
                                    userCluesDto.setHouseModel(userCluesDto.getHouseType());
                                    userCluesDto.setHouseType(target.getHouseType().toString());
                                }
                            } else {
                                userCluesDto.setHouseModel(userCluesDto.getHouseType());
                            }
                        }
                    }
                    //设置动态的值
                    //备注
                    if (cluesLabelMap != null && CollectionUtils.isNotEmpty(cluesLabelMap.get(userCluesDto.getId()))) {
                        String clueslabel = cluesLabelMap.get(userCluesDto.getId()).stream().map(s -> "#" + s.getLabelValue()).collect(Collectors.joining(","));
                        if (StringUtils.isNotBlank(userCluesDto.getRemark())) {
                            userCluesDto.setRemark(userCluesDto.getRemark() +","+ clueslabel);
                        }else {
                            userCluesDto.setRemark(clueslabel);
                        }
                    }
                    //房屋类型
                    if (userCluesDto.getHouseType() != null && !"".equals(userCluesDto.getHouseType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getHouseType(), userCluesDto.getCompanyId(), "house_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setHouseType(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setHouseType(null);
                        }
                    }

                    if (userCluesDto.getHouseRenovationType() != null && !"".equals(userCluesDto.getHouseRenovationType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getHouseRenovationType(), userCluesDto.getCompanyId(), "house_renovation_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setHouseRenovationType(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setHouseRenovationType(null);
                        }
                    }
                    //电话回访状态
                    if (StringUtils.isNotEmpty(userCluesDto.getVisitStatus())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getVisitStatus(), userCluesDto.getCompanyId(), "visit_status", findAttributeValueOutDtos);
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setVisitStatus(StringUtils.isNotEmpty(getAttributeNameByList(targetList)) ? getAttributeNameByList(targetList) : userCluesDto.getVisitStatus());
                        } else {
                        }
                    }
                    //户型house_model
                    if (userCluesDto.getHouseModel() != null && !"".equals(userCluesDto.getHouseModel())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getHouseModel(), userCluesDto.getCompanyId(), "house_model", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setHouseModel(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setHouseModel(null);
                        }
                    }
                    //装修类型
                    if (userCluesDto.getDecorateType() != null && !"".equals(userCluesDto.getDecorateType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getDecorateType(), userCluesDto.getCompanyId(), "decorate_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setDecorateType(getAttributeNameByList(targetList));

                        } else {
                            userCluesDto.setDecorateType(null);
                        }
                    }
                    //专修进度属性值名称
                    if (userCluesDto.getDecorateProgres() != null && !"".equals(userCluesDto.getDecorateProgres())) {
                        List<SelectAllByFiledIdVO> targetList = getAttributeLevelValueList(userCluesDto.getDecorateProgres(), userCluesDto.getCompanyId(), "follow_decorate_progres", selectAllByFiledIdVOS);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
                            String parentAttributeId = targetList.get(0).getParentAttributeId();

                            if (!CollectionUtils.isEmpty(selectAllByFiledIdVOs)) {
                                SelectAllByFiledIdVO selectAllByFiledIdVO1 = selectAllByFiledIdVOs.stream().filter(selectAllByFiledIdVO ->
                                        selectAllByFiledIdVO.getFieldId().equals("follow_decorate_progres") && parentAttributeId.equals(selectAllByFiledIdVO.getAttributeId())).findFirst().orElse(null);
                                if (selectAllByFiledIdVO1 != null) {
                                    userCluesDto.setDecorateProgres(selectAllByFiledIdVO1.getAttributeValue() + "-" + getAttributeLevelNameByList(targetList));
                                }
                            }
                        } else {
                            userCluesDto.setDecorateProgres(null);
                        }
                    }
                    //线索等级
                    if (userCluesDto.getCluesLevel() != null && !"".equals(userCluesDto.getCluesLevel())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesLevel(), userCluesDto.getCompanyId(), "clues_level", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesLevel(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesLevel(null);
                        }
                    }

                    //线索来源
                    if (userCluesDto.getCluesSource() != null && !"".equals(userCluesDto.getCluesSource())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesSource(), userCluesDto.getCompanyId(), "clues_source", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesSource(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesSource(null);
                        }
                    }
                    //线索分类
                    if (userCluesDto.getCluesType() != null && !"".equals(userCluesDto.getCluesType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesType(), userCluesDto.getCompanyId(), "clues_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesType(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesType(null);
                        }
                    }
                    //意向产品intention_product
//                    if (userCluesDto.getIntentionProduct() != null && !"".equals(userCluesDto.getIntentionProduct())) {
//                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getIntentionProduct(), userCluesDto.getCompanyId(), "intention_product", findAttributeValueOutDtos);
//                        //不为空则设置名称
//                        if (targetList != null && targetList.size() > 0) {
//                            userCluesDto.setIntentionProduct(getAttributeNameByList(targetList));
//                        } else {
//                            userCluesDto.setIntentionProduct(null);
//                        }
//                    }
                    //房屋面积house_area
                    if (userCluesDto.getHouseArea() != null && !"".equals(userCluesDto.getHouseArea())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getHouseArea(), userCluesDto.getCompanyId(), "house_area", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setHouseArea(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setHouseArea(null);
                        }
                    }
                    //厨房形状kitchen_type
                    if (userCluesDto.getKitchenType() != null && !"".equals(userCluesDto.getKitchenType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getKitchenType(), userCluesDto.getCompanyId(), "kitchen_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setKitchenType(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setKitchenType(null);
                        }
                    }
                    //厨房面积kitchen_area
                    if (userCluesDto.getKitchenArea() != null && !"".equals(userCluesDto.getKitchenArea()) && userCluesDto.getCompanyId() != null) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getKitchenArea(), userCluesDto.getCompanyId(), "kitchen_area", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setKitchenArea(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setKitchenArea(null);
                        }
                    }
                    //成交产品
                    if (userCluesDto.getMakeBargainProduct() != null && !"".equals(userCluesDto.getMakeBargainProduct())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getMakeBargainProduct(), userCluesDto.getCompanyId(), "make_bargain_product", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setMakeBargainProduct(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setMakeBargainProduct(null);
                        }
                    }
                    //成交套系
                    if (userCluesDto.getMakeBargainSystem() != null && !"".equals(userCluesDto.getMakeBargainSystem())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getMakeBargainSystem(), userCluesDto.getCompanyId(), "make_bargain_system", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setMakeBargainSystem(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setMakeBargainSystem(null);
                        }
                    }
                    //是否成交
                    if (userCluesDto.getIsMakeBargain() != null && !"".equals(userCluesDto.getIsMakeBargain())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getIsMakeBargain(), userCluesDto.getCompanyId(), "is_make_bargain", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setIsMakeBargain(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setIsMakeBargain(null);
                        }
                    }
                    //交易状态
                    if (userCluesDto.getPayStatus() != null && !"".equals(userCluesDto.getPayStatus())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getPayStatus(), userCluesDto.getCompanyId(), "pay_status", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setPayStatus(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setPayStatus(null);
                        }
                    }
                    //回访状态
                    if (userCluesDto.getReviewStatus() != null && !"".equals(userCluesDto.getReviewStatus())) {
                        List<SelectAllByFiledIdVO> targetList = getAttributeLevelValueList(userCluesDto.getReviewStatus(), userCluesDto.getCompanyId(), "review_status", selectAllByFiledIdVOS);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
                            SelectAttributeLevelInDto inDto1 = new SelectAttributeLevelInDto();
                            String parentAttributeId = targetList.get(0).getParentAttributeId();

                            if (!CollectionUtils.isEmpty(selectAllByFiledIdVOs)) {
                                SelectAllByFiledIdVO selectAllByFiledIdVO1 = selectAllByFiledIdVOs.stream().filter(selectAllByFiledIdVO ->
                                        selectAllByFiledIdVO.getFieldId().equals("review_status") && parentAttributeId.equals(selectAllByFiledIdVO.getAttributeId())).findFirst().orElse(null);
                                if (selectAllByFiledIdVO1 != null) {
                                    userCluesDto.setReviewStatus(selectAllByFiledIdVO1.getAttributeValue() + "-" + getAttributeLevelNameByList(targetList));
                                }
                            }
                        } else {
                            userCluesDto.setReviewStatus(null);
                        }
                    }
                    //脱敏操作影藏电话
                    if ("1".equals(inDto.getFlag())) {
                        if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                            userCluesDto.setCustomerPhone(setCostomerPhone(userCluesDto.getCustomerPhone()));
                        }
                        if (StringUtils.isNotEmpty(userCluesDto.getIntroducerPhone())) {
                            userCluesDto.setIntroducerPhone(setCostomerPhone(userCluesDto.getIntroducerPhone()));
                        }
                        if (StringUtils.isNotBlank(userCluesDto.getCustomerName())) {
                            if (userCluesDto.getCustomerName().length() == 1) {
                                userCluesDto.setCustomerName(userCluesDto.getCustomerName() + "***" + userCluesDto.getCustomerName());
                            } else {
                                String firstName = userCluesDto.getCustomerName().substring(0, 1);
                                String lastName = userCluesDto.getCustomerName().substring(userCluesDto.getCustomerName().length() - 1);
                                userCluesDto.setCustomerName(firstName + "***" + lastName);
                            }
                        }
                    }
                    //拆分联系方式
                    if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                        if (Character.isDigit(userCluesDto.getCustomerPhone().charAt(0)) == false || userCluesDto.getCustomerPhone().length() != 11) {
                            userCluesDto.setWechatno(userCluesDto.getCustomerPhone());
                            userCluesDto.setCustomerPhone("");
                        }
                    }
                    //设置标签
                    userCluesDto.setKeyWords(setKeyWords(userCluesDto.getId(), labelList));
                    //门店所属部门
                    if (storeList != null && storeList.size() > 0) {
                        FindStoreByOrgIdOutDto store = storeList.stream().filter(s -> s.getOrgId().equals(userCluesDto.getStoreId())).findFirst().orElse(null);
                        if (store != null) {
                            //TODO
                            //setting门店简称
                            userCluesDto.setAbbreviation(store.getAbbreviation());
                            userCluesDto.setStroeName(store.getName());
                            userCluesDto.setDistributorName(store.getDistributorName());
                            if (store.getParentId() != null && store.getParentId() != 0) {
                                int index = store.getFullPathName().lastIndexOf("-");
                                if (index > 0) {
                                    userCluesDto.setFullPathName(store.getFullPathName().substring(0, index));
                                } else {
                                    userCluesDto.setFullPathName(store.getFullPathName());
                                }
                            }
                        }
                    }
                    //跟进时效
                    if (!ObjectUtils.isEmpty(userCluesDto.getFollowEfficiencyTime())) {
                        if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.minutesConversionSeconds(30)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("30分钟内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.minutesConversionSeconds(30)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.minutesConversionSeconds(60)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("30分钟-1个小时内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.hoursConversionSeconds(1)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.hoursConversionSeconds(6)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("1个小时-6小时内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.hoursConversionSeconds(6)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.hoursConversionSeconds(24)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("6小时-24小时内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(1)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(2)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("1天-2天内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(2)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(3)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("2天-3天内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(3)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(7)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("3天-7天内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(7)) > 0
                                && userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(30)) <= 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("7天-30天内");
                        } else if (userCluesDto.getFollowEfficiencyTime().compareTo(TimeUtils.daysConversionSeconds(30)) > 0) {
                            userCluesDto.setFollowEfficiencyCategoryValue("30天以上");
                        }
                    }


                    //20243:线索列表-导出字段新增 added by zhongdian, 2022/5/30
//                    if (StringUtils.isNotBlank(userCluesDto.getAssistUserId())) {
//                        //填充线索协助人信息
//                        assistUser = assistUserList.stream().filter(s -> userCluesDto.getAssistUserId().equals(s.getId().toString())).findFirst().orElse(null);
//                        if (assistUser != null) {
//                            userCluesDto.setAssistUserCode(assistUser.getCode());
//                        }
//                    }
                    if (groupedMap != null) {
                        List<UserCluesAssistMapping> assistMappings1 = groupedMap.get(userCluesDto.getId());
                        if (assistMappings1 != null && assistMappings1.size() > 0) {
                            String code = assistMappings1.stream().map(UserCluesAssistMapping::getAssistUserCode).collect(Collectors.joining(","));
                            String name = assistMappings1.stream().map(UserCluesAssistMapping::getAssistUserName).collect(Collectors.joining(","));
                            userCluesDto.setAssistUserCode(code);
                            userCluesDto.setAssistUserName(name);
                        }
                    }


                    if (StringUtils.isNotBlank(userCluesDto.getUrgencyFollowUp())) {
                        //填充跟进紧急度(值集)
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getUrgencyFollowUp(), userCluesDto.getCompanyId(), "urgency_follow_up", findAttributeValueOutDtos);
                        if (CollectionUtils.isNotEmpty(targetList)) {
                            userCluesDto.setUrgencyFollowUp(getAttributeNameByList(targetList));
                        }
                    }
                    if (StringUtils.isNotBlank(userCluesDto.getUrgencyPurchase())) {
                        //填充购买紧急度(值集)
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getUrgencyPurchase(), userCluesDto.getCompanyId(), "urgency_purchase", findAttributeValueOutDtos);
                        if (CollectionUtils.isNotEmpty(targetList)) {
                            userCluesDto.setUrgencyPurchase(getAttributeNameByList(targetList));
                        }
                    }
                    if (userCluesDto.getInstallDate() != null) {
                        //格式化安装时间 yyyy-MM-dd
                        userCluesDto.setInstallDateValue(sdf.format(userCluesDto.getInstallDate()));
                    }
                    if (userCluesDto.getMoveinDate() != null) {
                        //格式化入户时间 yyyy-MM-dd
                        userCluesDto.setMoveinDateValue(sdf.format(userCluesDto.getMoveinDate()));
                    }
                    //进店方式
                    if (userCluesDto.getInStoreType() != null && !"".equals(userCluesDto.getInStoreType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getInStoreType(), userCluesDto.getCompanyId(), "in_store_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setInStoreTypeValue(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setInStoreTypeValue("");
                        }
                    }
                    //进店时间
                    if (userCluesDto.getInStoreTime() != null && !"".equals(userCluesDto.getInStoreTime())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getInStoreTime(), userCluesDto.getCompanyId(), "in_store_time", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setInStoreTimeValue(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setInStoreTimeValue("");
                        }
                    }
                    //留资方式
                    if (userCluesDto.getRetentionMethod() != null && !"".equals(userCluesDto.getRetentionMethod())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getRetentionMethod(), userCluesDto.getCompanyId(), "retention_method", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setRetentionMethodValue(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setRetentionMethodValue("");
                        }
                    }
                    //成交周期
                    if (userCluesDto.getDealOrderTime() != null) {
                        userCluesDto.setMakeBargainCycle((new BigDecimal((userCluesDto.getDealOrderTime().getTime() - userCluesDto.getFundingTime().getTime())).divide(new BigDecimal(1000 * 60 * 60), BigDecimal.ROUND_UP).setScale(0, BigDecimal.ROUND_UP).longValue()));
                    }

                    //引流渠道设置值
                    if (userCluesDto.getDecorateAndDesignerType() != null && userCluesDto.getDecorateAndDesignerCode() != null) {
                        if (decorateResultList != null && decorateResultList.size() > 0) {
                            if (userCluesDto.getDecorateAndDesignerType() == 3) {
                                DecorateAndDesignerDto decorateAndDesignerDto = decorateResultList.parallelStream().filter(x -> x.getType().equals(userCluesDto.getDecorateAndDesignerType()) && x.getDesignerCode().equals(userCluesDto.getDecorateAndDesignerCode())).findFirst().orElse(null);
                                if (decorateAndDesignerDto != null) {
                                    userCluesDto.setDecorateAndDesignerName(decorateAndDesignerDto.getDesignerName());
                                    userCluesDto.setDecorateAndDesignerPhone(decorateAndDesignerDto.getDesignerPhone());
                                }
                            } else {
                                DecorateAndDesignerDto decorateAndDesignerDto = decorateResultList.parallelStream().filter(x ->  x.getType().equals(userCluesDto.getDecorateAndDesignerType()) && x.getCode().equals(userCluesDto.getDecorateAndDesignerCode())).findFirst().orElse(null);
                                if (decorateAndDesignerDto != null && StringUtils.isNotBlank(decorateAndDesignerDto.getName())) {
                                    userCluesDto.setDecorateAndDesignerName(decorateAndDesignerDto.getName());
                                    userCluesDto.setDecorateAndDesignerPhone(decorateAndDesignerDto.getPhone());
                                }
                            }
                        }
                    }

                    //设置企微好友关系标志
                    if (CollectionUtils.isNotEmpty(relationList)) {
                        TQywxCustomerCluesRelation relation = relationList.parallelStream().filter(x -> x.getCluesId().equals(userCluesDto.getId())).findFirst().orElse(null);
                        if (relation != null) {
                            userCluesDto.setQywxRelationFlag("是");
                            userCluesDto.setUnionId(relation.getUnionId());
                            if (!qywxCustomerRelationMap.isEmpty()&&qywxCustomerRelationMap.containsKey(relation.getExternalUserid())){
                                userCluesDto.setWxName(qywxCustomerRelationMap.get(relation.getExternalUserid()));
                            }
                        } else {
                            userCluesDto.setQywxRelationFlag("否");
                        }
                    }

                    //预约上门时间
                    if (CollectionUtils.isNotEmpty(tCluesDesignInfoList)) {
                        TCluesDesignInfo cluesDesignInfo = tCluesDesignInfoList.stream().filter(d -> userCluesDto.getId().equals(d.getCluesId())).findFirst().orElse(null);
                        if (cluesDesignInfo != null && cluesDesignInfo.getAppointmentTime() != null) {
                            if (StringUtils.isNotBlank(cluesDesignInfo.getAppointmentQuantum())){
                                userCluesDto.setAppointmentTime(sdf.format(cluesDesignInfo.getAppointmentTime()) + " " + cluesDesignInfo.getAppointmentQuantum());
                            }else {
                                userCluesDto.setAppointmentTime(sdf.format(cluesDesignInfo.getAppointmentTime()));
                            }

                        }
                    }
                    //是否上门设计状态
                    if ("0".equals(userCluesDto.getIsComeDevise())) {//否
                        if (userCluesDto.getComeDeviseStatus() == null || 0 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("否-空");
                        } else if (1 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("否-待上门");
                        } else if (2 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("否-已测量");

                        } else if (3 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("否-已出方案");
                        }
                    } else if ("1".equals(userCluesDto.getIsComeDevise())) {//是
                        if (userCluesDto.getComeDeviseStatus() == null || 0 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("是-空");
                        } else if (1 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("是-待上门");
                        } else if (2 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("是-已测量");
                        } else if (3 == userCluesDto.getComeDeviseStatus()) {
                            userCluesDto.setIsComeDevise("是-已出方案");
                        }
                    }
                    if (Objects.equals(userCluesDto.getIsSceneInteraction(),1)) {
                        userCluesDto.setIsSceneInteractionValue("是");
                    }else{
                        userCluesDto.setIsSceneInteractionValue("否");
                    }
                    //工单id
                    if (CollectionUtils.isNotEmpty(cluesServiceList)){
                        Optional<CluesServiceInfoDto> first = cluesServiceList.stream().filter(x -> x.getCluesId().equals(userCluesDto.getId())).findFirst();
                        if (first.isPresent()) {
                            CluesServiceInfoDto cluesService = first.get();
                            if (StringUtils.isNotBlank(cluesService.getCsmOrderCode())) {
                                userCluesDto.setCsmOrderCode(cluesService.getCsmOrderCode());
                            }
                            if (StringUtils.isNotBlank(cluesService.getServiceEngineerCsn())) {
                                userCluesDto.setServiceEngineerCsn(cluesService.getServiceEngineerCsn());
                            }
                        }
                    }

                    if (userCluesDto.getCreateSourcePlatform() != null){
                        if (userCluesDto.getCreateSourcePlatform() == 7 || userCluesDto.getCreateSourcePlatform() == 8 || userCluesDto.getCreateSourcePlatform() == 3){
                            userCluesDto.setCreateSourcePlatformValue("企微内嵌工具");
                        } else if (userCluesDto.getCreateSourcePlatform() == 1) {
                            userCluesDto.setCreateSourcePlatformValue("云管理APP");
                        } else if (userCluesDto.getCreateSourcePlatform() == 2) {
                            userCluesDto.setCreateSourcePlatformValue("方太合作社");
                        } else if (userCluesDto.getCreateSourcePlatform() == 9) {
                            userCluesDto.setCreateSourcePlatformValue("电视端讲解");
                        } else {
                            userCluesDto.setCreateSourcePlatformValue("空");
                        }
                    }
                    if(groupedAdvertiseMap != null){
                        List<UserCluesAdvertiseSource> singleAdvertiseList = groupedAdvertiseMap.get(userCluesDto.getId());
                        if (CollectionUtils.isNotEmpty(singleAdvertiseList)) {
                            String localLifestyleChannel = singleAdvertiseList.stream().map(UserCluesAdvertiseSource::getType).distinct().map(t -> {
                                return t == 18 ? "抖音" : "美团";
                            }).collect(Collectors.joining(","));
                            String nickName = singleAdvertiseList.stream().map( x-> StringUtils.isBlank(x.getAdvertiseSourceAccountName()) ? "空" : x.getAdvertiseSourceAccountName()).collect(Collectors.joining(","));
                            String sourceCluesId = singleAdvertiseList.stream().map( x-> StringUtils.isBlank(x.getAdvertiseSourceClueId()) ? "空" : x.getAdvertiseSourceClueId()).collect(Collectors.joining(","));;
                            String sourceOrderId = singleAdvertiseList.stream().map( x-> StringUtils.isBlank(x.getAdvertiseSourceOrderId()) ? "空" : x.getAdvertiseSourceOrderId()).collect(Collectors.joining(","));;
                            userCluesDto.setDyAdvertiseNickName(nickName);
                            userCluesDto.setDyCluesIds(sourceCluesId);
                            userCluesDto.setDyOrderIds(sourceOrderId);
                            userCluesDto.setLocalLifestyleChannel(localLifestyleChannel);
                        }
                    }


                } catch (Exception e) {
                    log.error("错误数据：{}" + e);
                }
            }
        }
        //生成Excel，并上传oss
        String fileName = "", sheetName = "线索详情报表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //写入excel文件流
        try {
            if ("1".equals(inDto.getFlag())) {
                //脱敏转换
                List<CluesDetailSensitiveExportOutDtos> excelData =
                        CluesDetailMapper.INSTANCE.newExportToCluesDetailSensitiveOutDto(resultList);
                EasyExcel.write(os, CluesDetailSensitiveExportOutDtos.class).sheet(sheetName).doWrite(excelData);
            } else {
                List<CluesDetailExportOutDtos> excelData =
                        CluesDetailMapper.INSTANCE.newExportToCluesDetailOutDto(resultList);
                EasyExcel.write(os, CluesDetailExportOutDtos.class).sheet(sheetName).doWrite(excelData);
            }

            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        } catch (Exception e) {
            log.error("线索详情导出异常：{}",e);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private String getCategoryName(String decorateCategory, List<QueryByParamsOutDto> list) {
        QueryByParamsOutDto dto = list.parallelStream().filter(x -> x.getValueCode().equals(decorateCategory)).findFirst().orElse(null);
        if (dto != null) {
            return dto.getValueName();
        } else {
            return "";
        }
    }

    private List<FindLabelBySourceIdOutDto> findLabelNameByIds(List<Long> ids, String userClues) {
        int remainder = ids.size() % 2000;
        int size = (ids.size() / 2000);
        List<FindLabelBySourceIdOutDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = ids.subList(i * 2000, (i + 1) * 2000);
            List<FindLabelBySourceIdOutDto> labelList = labelMappingDao.findLabelNameByIds(subset, userClues);
            result.addAll(labelList);
        }
        if (remainder > 0) {
            List<Long> subset = ids.subList(size * 2000, size * 2000 + remainder);
            List<FindLabelBySourceIdOutDto> labelList = labelMappingDao.findLabelNameByIds(subset, userClues);
            result.addAll(labelList);
        }
        return result;
    }

    private List<FindVillageByIdOutDto> findVillageByVillageIds(List<Long> villageIds) {
        int remainder = villageIds.size() % 2000;
        int size = (villageIds.size() / 2000);
        List<FindVillageByIdOutDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = villageIds.subList(i * 2000, (i + 1) * 2000);
            FindVillageByVillageIdsInDto findVillageByVillageIdsInDto1 = new FindVillageByVillageIdsInDto();
            findVillageByVillageIdsInDto1.setVillageIds(subset);
            List<FindVillageByIdOutDto> villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto1).getData();
            result.addAll(villageList);
        }
        if (remainder > 0) {
            List<Long> subset = villageIds.subList(size * 2000, size * 2000 + remainder);
            FindVillageByVillageIdsInDto findVillageByVillageIdsInDto1 = new FindVillageByVillageIdsInDto();
            findVillageByVillageIdsInDto1.setVillageIds(subset);
            List<FindVillageByIdOutDto> villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto1).getData();
            result.addAll(villageList);
        }
        return result;
    }

    private List<UserAddressOutDto> selectUserAddressByIdList(List<Long> addressIds) {
        int remainder = addressIds.size() % 2000;
        int size = (addressIds.size() / 2000);

        List<UserAddressOutDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = addressIds.subList(i * 2000, (i + 1) * 2000);
            List<UserAddressOutDto> data = userAddressClientService.selectUserAddressByIdList2(subset).getData();
            result.addAll(data);
        }
        if (remainder > 0) {
            List<Long> subset = addressIds.subList(size * 2000, size * 2000 + remainder);
            List<UserAddressOutDto> data = userAddressClientService.selectUserAddressByIdList2(subset).getData();
            result.addAll(data);
        }
        return result;
    }

    //获取社区会员
    private String getCommunityMemberJson(String communityMemberJson) {
        if (StringUtils.isNotEmpty(communityMemberJson)) {
            FindCommunityMembersOutDto designerAppListDto = JSONObject.parseObject(communityMemberJson, FindCommunityMembersOutDto.class);
            communityMemberJson = designerAppListDto.getNoteName()
                    + "/" + designerAppListDto.getPhone();
        }
        return communityMemberJson;
    }

    //获取设计师
    private String getDecorateDesignerJson(String decorateDesignerJson) {
        //处理家装设计师
        if (StringUtils.isNotEmpty(decorateDesignerJson)) {
            DesignerAppListDto designerAppListDto = JSONObject.parseObject(decorateDesignerJson, DesignerAppListDto.class);
            if (StringUtils.isNotEmpty(designerAppListDto.getDesignerName())) {
                String phone = "";
                if (StringUtils.isNotEmpty(designerAppListDto.getDesignerPhone())) {
                    phone = designerAppListDto.getDesignerPhone().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                }
                decorateDesignerJson = designerAppListDto.getDecorateCompanyName() + "\n" + designerAppListDto.getDesignerName() + "/" + phone;
            } else {
                decorateDesignerJson = designerAppListDto.getDecorateCompanyName();
            }
        }
        return decorateDesignerJson;
    }


    private DecorateAndDesignerDto getDecorateDTO(String decorateDesignerJson) {
        //处理家装设计师
        if (StringUtils.isNotEmpty(decorateDesignerJson)) {
            DesignerAppListDto designerAppListDto = JSONObject.parseObject(decorateDesignerJson, DesignerAppListDto.class);
            if (designerAppListDto.getDecorateCompanyType() != null) {
                DecorateAndDesignerDto dto = new DecorateAndDesignerDto();
                dto.setCode(designerAppListDto.getDecorateCompanyCode());
                dto.setDesignerCode(designerAppListDto.getDesignerCode());
                dto.setType(designerAppListDto.getDecorateCompanyType().intValue());
                if(dto.getType() != null && dto.getType() == 4){
                    dto.setType(2);
                }
                dto.setDecorateCategory(designerAppListDto.getDecorateCategory());
                return dto;
            }
        }
        return null;
    }

    //分批次查询门店信息
    private List<FindStoreByOrgIdOutDto> findByOrgIds(List<Long> ids) {
        List<List<?>> lists = splitList(ids, 5000);
        List<FindStoreByOrgIdOutDto> result = new ArrayList<>();
        for (List<?> idList : lists) {
            List<Long> subset = (List<Long>) idList;
            List<FindStoreByOrgIdOutDto> storeList = storeClientService.findByOrgIds(new FindStoreByOrgIdsInDto(subset)).getData();
            result.addAll(storeList);
        }
        return result;
    }


    //分批次查询门店信息
    private List<DecorateAndDesignerDto> getDecorateList(List<DecorateAndDesignerDto> list) {
        List<List<?>> lists = splitList(list, 5000);
        List<DecorateAndDesignerDto> result = new ArrayList<>();
        for (List<?> idList : lists) {
            List<DecorateAndDesignerDto> subset = (List<DecorateAndDesignerDto>) idList;
            List<DecorateAndDesignerDto> storeList = orgClient.getDecorateInfoForExportByCodeList(subset).getData();
            result.addAll(storeList);
        }
        return result;
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<List<?>>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    private List<String> setFieldIdsForClues() {
        List<String> list = new ArrayList<>();
        list.add("house_type");
        list.add("house_model");
        list.add("follow_decorate_progres");
        list.add("clues_level");
        list.add("clues_source");
        list.add("clues_type");
        list.add("intention_product");
        list.add("house_area");
        list.add("kitchen_type");
        list.add("kitchen_area");
        list.add("make_bargain_product");
        list.add("make_bargain_system");
        list.add("review_status");
        list.add("pay_status");
        list.add("is_make_bargain");
        list.add("is_make_bargain");
        list.add("decorate_type");
        list.add("in_store_time");
        list.add("retention_method");
        list.add("in_store_type");
        list.add("visit_status");
        //20243:线索列表-导出字段新增 added by zhongdian, 2022/5/30
        //跟进紧急度
        list.add("urgency_follow_up");
        //购买紧急度
        list.add("urgency_purchase");
        list.add("house_renovation_type");

        return list;
    }

    private List<String> setFieldIds() {
        List<String> list = new ArrayList<>();
        list.add("follow_decorate_progres");
        list.add("follow_server");
        list.add("lost_order");
        list.add("review_status");
        return list;
    }


    private List<BaseAttributeVo> getAttributeValueList(String targetIds, Long companyId, String fieldName, List<BaseAttributeVo> findAttributeValueOutDtos) {
        List<BaseAttributeVo> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + findAttributeValueOutDtos);
        if (findAttributeValueOutDtos != null && findAttributeValueOutDtos.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            findAttributeValueOutDtos.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }

    //新的值集层级
    private List<SelectAllByFiledIdVO> getAttributeLevelValueList(String targetIds, Long companyId, String fieldName, List<SelectAllByFiledIdVO> selectAllByFiledIdVOS) {
        List<SelectAllByFiledIdVO> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + selectAllByFiledIdVOS);
        if (selectAllByFiledIdVOS != null && selectAllByFiledIdVOS.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            selectAllByFiledIdVOS.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }

    private String getAttributeLevelNameByList(List<SelectAllByFiledIdVO> targetList) {
        String buf = "";
        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
            log.info("错误数据：" + targetList.toString());
            buf = targetList.stream().map(
                    bean -> bean != null && StringUtils.isNotEmpty(bean.getAttributeValue()) ?
                            bean.getAttributeValue() : "").collect(Collectors.joining(","));
        }
        return buf;
    }

    private String getAttributeNameByList(List<BaseAttributeVo> targetList) {
        String buf = "";
        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
            log.info("错误数据：" + targetList.toString());
            buf = targetList.stream().map(
                    bean -> bean != null && StringUtils.isNotEmpty(bean.getAttributeValue()) ?
                            bean.getAttributeValue() : "").collect(Collectors.joining(","));
        }
        return buf;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4);
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4);
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }

    private String setKeyWords(Long id, List<FindLabelBySourceIdOutDto> labelList) {
        String buf = "";
        if (labelList != null && labelList.size() > 0) {
            buf = labelList.stream().filter(bean -> bean.getSourceId().equals(id)).map(FindLabelBySourceIdOutDto::getName).collect(Collectors.joining(","));
        }
        return buf;
    }

    public static String getFullSubStoreChannelName(Map<Long, Dict> subChannelIdMap,Map<String, Dict> subChannelCodeMap,String storeSubChannelCode){
        StringBuffer subStoreChannelName=new StringBuffer("");
        if(Objects.isNull(storeSubChannelCode)){
            //兼容空字符串
            storeSubChannelCode="";
        }
        if(subChannelCodeMap.containsKey(storeSubChannelCode)){
            Dict dict = subChannelCodeMap.get(storeSubChannelCode);
            subStoreChannelName.insert(0,dict.getValueName());
            Dict parentDict = subChannelIdMap.get(dict.getParentId());
            if(parentDict!=null){
                subStoreChannelName.insert(0,"/");
                subStoreChannelName.insert(0,parentDict.getValueName());
            }
        }
        return subStoreChannelName.toString();
    }
    /**
     * 获取门店渠道下的渠道细分，因为有不同渠道下有细分编码重复的，所以只能按渠道来区分
     * @return
     */
    public ImmutableMap<String, Map<String, Dict>> getChannelSubMap(){
        ImmutableMap<String, Map<String, Dict>> channelSubMap = ImmutableMap.of(
                "L018", Optional.ofNullable(workflowClient.getSubChannelCodeList("L018"))
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .map(l->l.stream().collect(Collectors.toMap(d-> Optional.ofNullable(d.getValueCode()).orElse(""), Function.identity())))
                        .orElse(Maps.newHashMapWithExpectedSize(0) ),

                "L025",Optional.ofNullable(workflowClient.getSubChannelCodeList("L025"))
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .map(l->l.stream().collect(Collectors.toMap(Dict::getValueCode, Function.identity())))
                        .orElse(Maps.newHashMapWithExpectedSize(0)),

                "L028",Optional.ofNullable(workflowClient.getSubChannelCodeList("L028"))
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .map(l->l.stream().collect(Collectors.toMap(Dict::getValueCode, Function.identity())))
                        .orElse(Maps.newHashMapWithExpectedSize(0) )
        );
        return channelSubMap;
    }

}
