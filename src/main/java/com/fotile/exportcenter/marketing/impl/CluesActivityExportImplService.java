package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.client.StoreClientService;
import com.fotile.exportcenter.marketing.dao.UserCluesDao;
import com.fotile.exportcenter.marketing.mapper.CluesActivityMapper;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("105")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesActivityExportImplService implements CluesExportImpl {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    private UserCluesDao userCluesDao;

    @Autowired
    private StoreClientService storeClientService;


    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)){
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())){
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {

            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count1 = 2000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count1;
        Integer lastCount = totalCount % count1;
        Integer size = 0;

        List<CluesActivityOutDto> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count1;
            size = count1;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            List<CluesActivityOutDto> cluesActivity = userCluesDao.newCluesActivityForExport(inDto);
            resultList.addAll(cluesActivity);

            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);
        }

        if (resultList == null || resultList.size() <= 0) {
            throw new RuntimeException("未能查询到结果！");
        }

        //循环线索，获取需要的数据，并去重
        Set<Long> idsSet = new HashSet<>();
        HashSet<Long> storeIdSet = new HashSet<Long>();

        for (CluesActivityOutDto clusesOutDto : resultList) {
            //未跟进时间 -- 结束
            if (clusesOutDto.getId() != null) {
                idsSet.add(clusesOutDto.getId());
            }
            if (clusesOutDto.getStoreId() != null) {
                storeIdSet.add(clusesOutDto.getStoreId());
            }
        }

        //set转换list
        List<Long> ids = new ArrayList<>(idsSet);
        List<Long> storeIdList = new ArrayList<>(storeIdSet);

        if (ids != null && ids.size() > 0) {
            //查询门店信息
            List<FindStoreByOrgIdOutDto> storeList = new ArrayList<>();
            if (storeIdList != null && storeIdList.size() > 0) {
                storeList = findByOrgIds(storeIdList);
            }
            for (int r = 0; r < resultList.size(); r++) {
                CluesActivityOutDto cluesActivity = resultList.get(r);
                cluesActivity.setIndex(r + 1);
                try {

                    if (cluesActivity.getActivityId() != null) {
                        cluesActivity.setActivityId("D" + cluesActivity.getActivityId());
                    }
                    log.debug("线索id:" + cluesActivity.getId());
                    //脱敏操作影藏电话
                    if (StringUtils.isNotBlank(cluesActivity.getCustomerName())){
                        if (cluesActivity.getCustomerName().length() == 1){
                            cluesActivity.setCustomerName(cluesActivity.getCustomerName()+"***"+cluesActivity.getCustomerName());
                        }else {
                            String firstName = cluesActivity.getCustomerName().substring(0, 1);
                            String lastName = cluesActivity.getCustomerName().substring(cluesActivity.getCustomerName().length() - 1);
                            cluesActivity.setCustomerName(firstName + "***" + lastName);
                        }
                    }
//                    if ("1".equals(inDto.getFlag())) {
                        if (StringUtils.isNotEmpty(cluesActivity.getCustomerPhone())) {
                            cluesActivity.setCustomerPhone(setCostomerPhone(cluesActivity.getCustomerPhone()));
                        }
//                    }
                    //门店所属部门
                    if (storeList != null && storeList.size() > 0) {
                        FindStoreByOrgIdOutDto store = storeList.stream().filter(s -> s.getOrgId().equals(cluesActivity.getStoreId())).findFirst().orElse(null);
                        if (store != null) {
                            //setting门店简称
                            cluesActivity.setAbbreviation(store.getAbbreviation());
                            cluesActivity.setStroeName(store.getName());
                            cluesActivity.setDistributorName(store.getDistributorName());
                            if (store.getParentId() != null && store.getParentId() != 0) {
                                int index = store.getFullPathName().lastIndexOf("-");
                                if (index > 0) {
                                    cluesActivity.setFullPathName(store.getFullPathName().substring(0, index));
                                } else {
                                    cluesActivity.setFullPathName(store.getFullPathName());
                                }
                            }
                        }
                    }

                } catch (Exception e) {
                    log.error("错误数据：" + e.getMessage());
                }
            }
        }
        //生成Excel，并上传oss
        String fileName = "", sheetName = "线索参与活动表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //写入excel文件流
        try {
            List<CluesActivityExportOutDto> cluesActivityExportOutDtos = CluesActivityMapper.INSTANCE.cluesActivityToExport(resultList);
            EasyExcel.write(os, CluesActivityExportOutDto.class).sheet(sheetName).doWrite(cluesActivityExportOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        } catch (Exception e) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }









    //分批次查询门店信息
    private List<FindStoreByOrgIdOutDto> findByOrgIds(List<Long> ids) {
        List<List<?>> lists = splitList(ids, 5000);
        List<FindStoreByOrgIdOutDto> result = new ArrayList<>();
        for (List<?> idList : lists) {
            List<Long> subset = (List<Long>) idList;
            List<FindStoreByOrgIdOutDto> storeList = storeClientService.findByOrgIds(new FindStoreByOrgIdsInDto(subset)).getData();
            result.addAll(storeList);
        }
        return result;
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<List<?>>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4);
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4);
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }



}
