package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.client.OrgClient;
import com.fotile.exportcenter.marketing.dao.BaseAttributeMapper;
import com.fotile.exportcenter.marketing.dao.UserCluesDao;
import com.fotile.exportcenter.marketing.mapper.CluesPortionMapper;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.pojo.vo.BaseAttributeVo;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.marketing.service.TAttributeLevelConfigService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("102")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesPortionExportImplService implements CluesExportImpl {
    @Autowired
    private UserCluesDao userCluesDao;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private BaseAttributeMapper baseAttributeMapper;
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    TAttributeLevelConfigService attributeLevelConfigService;

    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        //log.error("type=102");
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)){
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())){
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        /**
         * 根据条件查询线索
         */
        Integer count = 2000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        List<NewExportUserClusesOutDto> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count;
            size = count;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            List<NewExportUserClusesOutDto> newQuerySalesLeadsList = userCluesDao.cluesPortionExport(inDto);
            resultList.addAll(newQuerySalesLeadsList);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);
        }
        if (resultList == null || resultList.size() <= 0) {
            throw new RuntimeException("未能查询到结果！");
        }

        //循环线索，获取需要的数据，并去重
        Set<Long> idsSet = new HashSet<>();
        Set<Long> companyIds1Set = new HashSet<>();
        Set<String> channelCodesSet = new HashSet<>();
        Set<String> radioCodesSet = new HashSet<>();
        for (NewExportUserClusesOutDto clusesOutDto : resultList) {
            String decorateDesignerJson = clusesOutDto.getDecorateDesignerJson();
            decorateDesignerJson = getDecorateDesignerJson(decorateDesignerJson);
            clusesOutDto.setDecorateDesignerJson(decorateDesignerJson);
            String communityMemberJson = clusesOutDto.getCommunityMemberJson();
            communityMemberJson = getCommunityMemberJson(communityMemberJson);
            clusesOutDto.setCommunityMemberJson(communityMemberJson);
            if (clusesOutDto.getId() != null) {
                idsSet.add(clusesOutDto.getId());
            }
            if (clusesOutDto.getCompanyId() != null) {
                companyIds1Set.add(clusesOutDto.getCompanyId());
            }
            if (StringUtils.isNotEmpty(clusesOutDto.getChannelCode())) {
                channelCodesSet.add(clusesOutDto.getChannelCode());
            }
            if (StringUtils.isNotEmpty(clusesOutDto.getRadioCode())) {
                radioCodesSet.add(clusesOutDto.getRadioCode());
            }
        }
        //set转换list
        List<Long> ids = new ArrayList<>(idsSet);
        List<String> channelCodes = new ArrayList<>(channelCodesSet);
        List<String> radioCodes = new ArrayList<>(radioCodesSet);
        List<Long> companyIdList = new ArrayList<>(companyIds1Set);
        List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = new ArrayList<>();
        FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
        if (resultList != null && resultList.size() > 0) {
            findCompanyByIdsInDto.setIdList(companyIdList);
        }
        try {
            findCompanyAreaByOrgIdsOutDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
        } catch (Exception e) {
            log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
        }

        List<CluesPortionExportOutDto> cluesPortionExportOutDtos = new ArrayList<>();
        if (ids != null && ids.size() > 0) {
            //定义返回参数的变量
            List<ChannelEntity> channelList = null;
            //渠道名称
            if (channelCodes != null && channelCodes.size() > 0) {
                channelList = orgClient.findByChannelCodes(channelCodes).getData();
            }
            //频道
            List<FindRadioAndChannelByCodesOutDto> radioList = null;
            if (radioCodes != null && radioCodes.size() > 0) {
                radioList = orgClient.findByRadioCodes2(radioCodes).getData();
            }
            // 获取所有ttributeValues
            List<String> fields = setPortionFieldIdsForClues();
            List<BaseAttributeVo> findAttributeValueOutDtos = baseAttributeMapper.getAttributeByFieldIds(fields);

            for (int r=0; r<resultList.size(); r++) {
                NewExportUserClusesOutDto userCluesDto = resultList.get(r);
                userCluesDto.setIndex(r+1);
                try {
                    FindCompanyAreaByOrgIdsOutDto findCompanyAreaByOrgIdsOutDto = findCompanyAreaByOrgIdsOutDtos.stream().filter(s -> s.getOrgId() != null && s.getOrgId().equals(userCluesDto.getCompanyId())).findAny().orElse(null);
                        if (findCompanyAreaByOrgIdsOutDto != null) {
                            userCluesDto.setDistrictCode(findCompanyAreaByOrgIdsOutDto.getValueCode());
                            userCluesDto.setDistrictValue(findCompanyAreaByOrgIdsOutDto.getValueName());
                        }
                    if (userCluesDto.getActivityId() != null) {
                        userCluesDto.setActivityId1("D" + userCluesDto.getActivityId());
                    }
                    log.debug("线索id:" + userCluesDto.getId());
                    //根据渠道编码获取渠道名称
                    if (StringUtils.isNotEmpty(userCluesDto.getChannelCode())) {
                        ChannelEntity target = channelList.stream().filter(bean -> bean.getCode().equals(userCluesDto.getChannelCode())).findFirst().orElse(null);
                        userCluesDto.setChannelName(target != null ? target.getName() : null);
                    }
                    if (StringUtils.isNotEmpty(userCluesDto.getRadioCode())) {
                        FindRadioAndChannelByCodesOutDto target = radioList.stream().filter(bean -> bean.getRadioCode().equals(userCluesDto.getRadioCode())).findFirst().orElse(null);
                        userCluesDto.setRadioName(target != null ? target.getRadioName() : null);
                    }
                    if (userCluesDto.getCluesLevel() != null && !"".equals(userCluesDto.getCluesLevel())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesLevel(), userCluesDto.getCompanyId(), "clues_level", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesLevel(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesLevel(null);
                        }

                    }
                    //电话回访状态
                    if (StringUtils.isNotEmpty(userCluesDto.getVisitStatus())){
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getVisitStatus(), userCluesDto.getCompanyId(), "visit_status", findAttributeValueOutDtos);
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setVisitStatus(StringUtils.isNotEmpty(getAttributeNameByList(targetList)) ? getAttributeNameByList(targetList) : userCluesDto.getVisitStatus());
                        } else {
                        }
                    }
                    //线索来源
                    if (userCluesDto.getCluesSource() != null && !"".equals(userCluesDto.getCluesSource())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesSource(), userCluesDto.getCompanyId(), "clues_source", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesSource(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesSource(null);
                        }
                    }
                    //线索分类
                    if (userCluesDto.getCluesType() != null && !"".equals(userCluesDto.getCluesType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getCluesType(), userCluesDto.getCompanyId(), "clues_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setCluesType(getAttributeNameByList(targetList));
                        } else {
                            userCluesDto.setCluesType(null);
                        }
                    }
                    List<String> configFieldIds = setFieldIds();
                    List<SelectAllByFiledIdVO> selectAllByFiledIdVOS = attributeLevelConfigService.findByFiledIds(configFieldIds);
                    List<SelectAllByFiledIdVO> selectAllByFiledIdVOs = attributeLevelConfigService.findByFiledIds(Arrays.asList("follow_decorate_progres", "review_status"));

                    //回访状态
                    if (userCluesDto.getReviewStatus() != null && !"".equals(userCluesDto.getReviewStatus())) {
                        List<SelectAllByFiledIdVO> targetList = getAttributeLevelValueList(userCluesDto.getReviewStatus(), userCluesDto.getCompanyId(), "review_status", selectAllByFiledIdVOS);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
                            SelectAttributeLevelInDto inDto1 = new SelectAttributeLevelInDto();
                            String parentAttributeId = targetList.get(0).getParentAttributeId();

                            if (!CollectionUtils.isEmpty(selectAllByFiledIdVOs)) {
                                SelectAllByFiledIdVO selectAllByFiledIdVO1 = selectAllByFiledIdVOs.stream().filter(selectAllByFiledIdVO ->
                                        selectAllByFiledIdVO.getFieldId().equals("review_status") && parentAttributeId.equals(selectAllByFiledIdVO.getAttributeId())).findFirst().orElse(null);
                                if (selectAllByFiledIdVO1 != null) {
                                    userCluesDto.setReviewStatus(selectAllByFiledIdVO1.getAttributeValue() + "-" + getAttributeLevelNameByList(targetList));
                                }
                            }
                        } else {
                            userCluesDto.setReviewStatus(null);
                        }
                    }
                    //装修类型
                    if (userCluesDto.getDecorateType() != null && !"".equals(userCluesDto.getDecorateType())) {
                        List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getDecorateType(), userCluesDto.getCompanyId(), "decorate_type", findAttributeValueOutDtos);
                        //不为空则设置名称
                        if (targetList != null && targetList.size() > 0) {
                            userCluesDto.setDecorateType(getAttributeNameByList(targetList));

                        } else {
                            userCluesDto.setDecorateType(null);
                        }
                    }
                    //脱敏操作影藏地址和电话
                    if ("1".equals(inDto.getFlag())) {
                        if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                            userCluesDto.setCustomerPhone(setCostomerPhone(userCluesDto.getCustomerPhone()));
                        }
                        if (StringUtils.isNotBlank(userCluesDto.getCustomerName())){
                            if (userCluesDto.getCustomerName().length() == 1){
                                userCluesDto.setCustomerName(userCluesDto.getCustomerName()+"***"+userCluesDto.getCustomerName());
                            }else {
                                String firstName = userCluesDto.getCustomerName().substring(0, 1);
                                String lastName = userCluesDto.getCustomerName().substring(userCluesDto.getCustomerName().length() - 1);
                                userCluesDto.setCustomerName(firstName + "***" + lastName);
                            }
                        }
                    }
                    //拆分联系方式
                    if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                        if (Character.isDigit(userCluesDto.getCustomerPhone().charAt(0)) == false || userCluesDto.getCustomerPhone().length() != 11){
                            userCluesDto.setWechatno(userCluesDto.getCustomerPhone());
                            userCluesDto.setCustomerPhone("");
                        }
                    }
                } catch (Exception e) {
                    log.error("错误数据：" + e.getMessage());
                }
                if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                    userCluesDto.setCustomerPhone(setCostomerPhone(userCluesDto.getCustomerPhone()));
                }
            }

            //转换导出实体类
            cluesPortionExportOutDtos = CluesPortionMapper.INSTANCE.newExportToCluesPortionOutDto(resultList);
        }
        //生成Excel，并上传oss
        String fileName = "", sheetName = "线索日志报表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            //写入excel文件流
            EasyExcel.write(os, CluesPortionExportOutDto.class).sheet(sheetName).doWrite(cluesPortionExportOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        }catch (Exception e){
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        }finally {
            try {
                if (os != null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private List<String> setPortionFieldIdsForClues() {
        List<String> list = new ArrayList<>();
        list.add("clues_level");
        list.add("clues_source");
        list.add("clues_type");
        list.add("review_status");
        list.add("visit_status");
        return list;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }

    private List<BaseAttributeVo> getAttributeValueList(String targetIds, Long companyId, String fieldName, List<BaseAttributeVo> findAttributeValueOutDtos) {
        List<BaseAttributeVo> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + findAttributeValueOutDtos);
        if (findAttributeValueOutDtos != null && findAttributeValueOutDtos.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            findAttributeValueOutDtos.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }

    private String getAttributeNameByList(List<BaseAttributeVo> targetList) {
        String buf = new String();
        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
            log.info("错误数据：" + targetList.toString());
            buf = targetList.stream().map(
                    bean -> bean != null && StringUtils.isNotEmpty(bean.getAttributeValue()) ?
                            bean.getAttributeValue() : "").collect(Collectors.joining(","));
        }
        return buf;
    }

    //获取社区会员
    private String getCommunityMemberJson(String communityMemberJson) {
        if (StringUtils.isNotEmpty(communityMemberJson)) {
            FindCommunityMembersOutDto designerAppListDto = JSONObject.parseObject(communityMemberJson, FindCommunityMembersOutDto.class);
            communityMemberJson = designerAppListDto.getNoteName()
                    + "/" + designerAppListDto.getPhone();
        }
        return communityMemberJson;
    }

    //获取设计师
    private String getDecorateDesignerJson(String decorateDesignerJson) {
        //处理家装设计师
        if (StringUtils.isNotEmpty(decorateDesignerJson)) {
            DesignerAppListDto designerAppListDto = JSONObject.parseObject(decorateDesignerJson, DesignerAppListDto.class);
            if (StringUtils.isNotEmpty(designerAppListDto.getDesignerName())) {
                decorateDesignerJson = designerAppListDto.getDecorateCompanyName() + "\n" + designerAppListDto.getDesignerName() + "/" + designerAppListDto.getDesignerPhone().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
            } else {
                decorateDesignerJson = designerAppListDto.getDecorateCompanyName();
            }
        }
        return decorateDesignerJson;
    }

    private List<SelectAllByFiledIdVO> getAttributeLevelValueList(String targetIds, Long companyId, String fieldName, List<SelectAllByFiledIdVO> selectAllByFiledIdVOS) {
        List<SelectAllByFiledIdVO> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + selectAllByFiledIdVOS);
        if (selectAllByFiledIdVOS != null && selectAllByFiledIdVOS.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            selectAllByFiledIdVOS.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }
    private String getAttributeLevelNameByList(List<SelectAllByFiledIdVO> targetList) {
        String buf = "";
        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
            log.info("错误数据：" + targetList.toString());
            buf = targetList.stream().map(
                    bean -> bean != null && StringUtils.isNotEmpty(bean.getAttributeValue()) ?
                            bean.getAttributeValue() : "").collect(Collectors.joining(","));
        }
        return buf;
    }

    private List<String> setFieldIds() {
        List<String> list = new ArrayList<>();
        list.add("follow_decorate_progres");
        list.add("follow_server");
        list.add("lost_order");
        list.add("review_status");
        return list;
    }
}
