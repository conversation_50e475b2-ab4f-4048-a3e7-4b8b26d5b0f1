package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.fotile.exportcenter.client.*;
import com.fotile.exportcenter.marketing.constant.CategoryCodeEnum;
import com.fotile.exportcenter.marketing.dao.ActivityImgDao;
import com.fotile.exportcenter.marketing.dao.BaseAttributeMapper;
import com.fotile.exportcenter.marketing.dao.OperatorLogDao;
import com.fotile.exportcenter.marketing.dao.UserCluesDao;
import com.fotile.exportcenter.marketing.mapper.CluesLogMapper;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.pojo.vo.BaseAttributeVo;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.marketing.service.TAttributeLevelConfigService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.common.config.AliConfig;
import com.fotile.framework.data.common.constant.AliConstant;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("103")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesLogExportImplService implements CluesExportImpl {
    @Autowired
    private OperatorLogDao operatorLogDao;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserAddressClientService userAddressClientService;
    @Autowired
    private VillageClientService villageClientService;
    @Autowired
    private BaseAttributeMapper baseAttributeMapper;
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    private StoreClientService storeClientService;
    @Autowired
    private ActivityImgDao activityImgDao;
    @Autowired
    private TAttributeLevelConfigService attributeLevelConfigService;
    @Autowired
    UserCluesDao userCluesDao;

    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        //log.error("type=103");
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)){
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())){
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count = 50000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

//
        List<Long> cluesIds = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count;
            size = count;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            //查询线索集合
            List<Long> logCluesExport = userCluesDao.findLogCluesExport(inDto);
            cluesIds.addAll(logCluesExport);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);

            //线程休眠2秒
            try {
                log.error("***线程休眠开始***");
                Thread.sleep(2000);
                log.error("***线程休眠结束***");
            } catch (Exception e) {
                Thread.currentThread().interrupt();
            }
        }
        if (CollectionUtils.isEmpty(cluesIds)) {
            throw new RuntimeException("未能查询到结果！");
        }

        Integer size1 = cluesIds.size();
        List<SelectOperatorLogListOutDto> resultList = new CopyOnWriteArrayList<>();

        List<CompletableFuture> completableFutureList = new LinkedList<>();
        Integer total = size1 / 2000;
        Integer remainder = size1 % 2000;
        Integer pageSize = 2000;
        for (Integer cluesIdx = 0; cluesIdx <= total; cluesIdx++) {

            List<Long> collect = null;
            if (cluesIdx == total ) {
                if (remainder == 0) {
                    continue;
                }
                collect =  cluesIds.stream().skip((cluesIdx) * pageSize).limit(remainder).collect(Collectors.toList());
            } else {
                collect = cluesIds.stream().skip((cluesIdx) * pageSize).limit(pageSize).collect(Collectors.toList());
            }
            List<Long> finalCollect = collect;
            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                return operatorLogDao.newExportOperatorLogByCluesIds(finalCollect);
            }).thenAcceptAsync((result) -> {
                resultList.addAll(result);
            }));
            //查询写跟进
            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                return operatorLogDao.newFollowUpByCluesIds(finalCollect);
            }).thenAcceptAsync((result) -> {
                resultList.addAll(result);
            }));


        }
        completableFutureList.stream().forEach(CompletableFuture::join);
        //使用forkJoinPool线程池--根据 cluesIds查询写跟进和查询日志写入 reslutList 里面
//        ForkJoinPool forkJoinPool = new ForkJoinPool(16);
//        int start = 1;
//        resultList = forkJoinPool.submit(new CluesLogForkAction(start, cluesIds.size(), operatorLogDao, cluesIds)).join();
//        forkJoinPool.shutdown();
//        try {
//            forkJoinPool.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }


        //分组查询日志


        if (CollectionUtils.isEmpty(resultList)) {
            throw new RuntimeException("未能查询到结果！");
        }
        //排序根据sourceId 正序 再createdDate倒叙 空排最后
        resultList
                .sort(Comparator.comparing(SelectOperatorLogListOutDto::getSourceId, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(SelectOperatorLogListOutDto::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())).reversed());


        //设置参数
        Set<Long> idsSet = new HashSet<>();
        Set<Long> companyIds1Set = new HashSet<>();
        Set<Long> addressIdsSet = new HashSet<>();
        Set<Long> villageIdsSet = new HashSet<>();
        HashSet<Long> storeIdSet = new HashSet<Long>();
        HashSet<Long> logIdSet = new HashSet<Long>();
        HashSet<Long> followIdSet = new HashSet<Long>();
        for (SelectOperatorLogListOutDto clusesOutDto : resultList) {
            if (clusesOutDto.getId() != null) {
                idsSet.add(clusesOutDto.getId());
            }
            if (clusesOutDto.getCompanyId() != null) {
                companyIds1Set.add(clusesOutDto.getCompanyId());
            }
            if (clusesOutDto.getAddressId() != null) {
                addressIdsSet.add(clusesOutDto.getAddressId());
            }
            if (clusesOutDto.getVillageId() != null) {
                villageIdsSet.add(clusesOutDto.getVillageId());
            }
            if (clusesOutDto.getStoreId() != null) {
                storeIdSet.add(clusesOutDto.getStoreId());
            }
            if("operator_marketing_log".equals(clusesOutDto.getSourceTableName())){
                logIdSet.add(Long.valueOf(clusesOutDto.getId()));
            }else if ("clues_follow_up".equals(clusesOutDto.getSourceTableName())){
                followIdSet.add(Long.valueOf(clusesOutDto.getId()));
            }
        }
        //set转换list
        List<Long> ids = new ArrayList<>(idsSet);
        List<Long> companyIds1 = new ArrayList<>(companyIds1Set);
        List<Long> addressIds = new ArrayList<>(addressIdsSet);
        List<Long> villageIds = new ArrayList<>(villageIdsSet);
        List<Long> storeIdList = new ArrayList<>(storeIdSet);
        List<Long> logIds = new ArrayList<>(logIdSet);
        List<Long> followIds = new ArrayList<>(followIdSet);

        FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
        findCompanyByIdsInDto.setIdList(companyIds1);
        List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = new ArrayList<>();
        try {
            findCompanyAreaByOrgIdsOutDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
        } catch (Exception e) {
            log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
        }

        List<OperatorLogListOutDto> operatorLogListOutDtos = new ArrayList<>();
        if (ids != null && ids.size() > 0) {
            //查询门店信息
            List<FindStoreByOrgIdOutDto> storeList = new ArrayList<>();
            if (storeIdList != null && storeIdList.size() > 0) {
                storeList = findByOrgIds(storeIdList);
            }
            //地址信息
            List<UserAddressOutDto> addressList = null;
            if (addressIds != null && addressIds.size() > 0) {
                addressList = selectUserAddressByIdList(addressIds);
//                addressList = userAddressClientService.selectUserAddressByIdList2(addressIds).getData();
            }
            //查询所有小区名称
            List<FindVillageByIdOutDto> villageList = null;
            if (villageIds != null && villageIds.size() > 0) {
                villageList = findVillageByVillageIds(villageIds);
//                FindVillageByVillageIdsInDto findVillageByVillageIdsInDto = new FindVillageByVillageIdsInDto();
//                findVillageByVillageIdsInDto.setVillageIds(villageIds);
//                villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto).getData();
                log.info("villageIds+++++++++++++++++++:" + villageList.toString());
            }
            // 获取所有ttributeValues
            List<String> fieldIds = setFieldIdsForLog();
            List<BaseAttributeVo> findAttributeValueOutDtos = baseAttributeMapper.getAttributeByFieldIds(fieldIds);

            //查询新的值集层级
            List<String> fieldIdList =setFieldIds();
            List<SelectAllByFiledIdVO> selectAllByFiledIdVOS = attributeLevelConfigService.findByFiledIds(fieldIdList);


            //获取日志图片
            List<LogPictureDto> logPictureDtos = null;
            List<LogPictureDto> followPictureDtos = null;
            if (logIds != null && logIds.size() > 0){
                logPictureDtos = findLogPicture(logIds,"operator_marketing_log");
            }
            //获取跟进图片
            if (followIds != null && followIds.size() > 0){
                followPictureDtos = findLogPicture(followIds, "clues_follow_up");
            }
            log.info("日志图片："+logPictureDtos);
            log.info("跟进图片："+followPictureDtos);

            for (int r=0; r<resultList.size(); r++) {
                SelectOperatorLogListOutDto userCluesDto = resultList.get(r);
                userCluesDto.setIndex(r+1);
                //脱敏操作影藏地址和电话
                if ("1".equals(inDto.getFlag())) {
//                    userCluesDto.setAddress("");
                    userCluesDto.setCustomerPhone(setCostomerPhone(userCluesDto.getCustomerPhone()));
                    if (StringUtils.isNotBlank(userCluesDto.getCustomerName())){
                        if (userCluesDto.getCustomerName().length() == 1){
                            userCluesDto.setCustomerName(userCluesDto.getCustomerName()+"***"+userCluesDto.getCustomerName());
                        }else {
                            String firstName = userCluesDto.getCustomerName().substring(0, 1);
                            String lastName = userCluesDto.getCustomerName().substring(userCluesDto.getCustomerName().length() - 1);
                            userCluesDto.setCustomerName(firstName + "***" + lastName);
                        }
                    }
                }
                //拆分联系方式
                if (StringUtils.isNotEmpty(userCluesDto.getCustomerPhone())) {
                    if (Character.isDigit(userCluesDto.getCustomerPhone().charAt(0)) == false || userCluesDto.getCustomerPhone().length() != 11){
                        userCluesDto.setWechatno(userCluesDto.getCustomerPhone());
                        userCluesDto.setCustomerPhone("");
                    }
                }
                if (userCluesDto.getVillageId() != null) {
                    FindVillageByIdOutDto target = villageList.stream().filter(bean -> bean.getId().equals(userCluesDto.getVillageId())).findFirst().orElse(null);
                    userCluesDto.setVillage(target != null ? target.getName() : null);
                }
                if (userCluesDto.getAddressId() != null) {
                    UserAddressOutDto target = addressList.stream().filter(bean -> bean.getId().equals(userCluesDto.getAddressId())).findFirst().orElse(null);
                    if (target != null) {
                        if (target.getHouseType() != null) {
                            if ("-1".equals(target.getHouseType().toString())) {
                                userCluesDto.setHouseType(null);
                            } else {
                                userCluesDto.setHouseType(target.getHouseType().toString());
                            }
                        }
                    }
                }
                if (userCluesDto.getAppType() != null && !"".equals(userCluesDto.getAppType())) {
                    List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getAppType(), userCluesDto.getCompanyId(), "follow_type", findAttributeValueOutDtos);
                    //不为空则设置名称
                    if (targetList != null && targetList.size() > 0) {
                        userCluesDto.setAppType(getAttributeNameByList(targetList));
                    } else {
                        userCluesDto.setAppType(null);
                    }
                }
                //follow_decorate_progres
                if (userCluesDto.getDecorateProgres() != null && !"".equals(userCluesDto.getDecorateProgres())) {
                    List<SelectAllByFiledIdVO> targetList = getAttributeLevelValueList(userCluesDto.getDecorateProgres(), userCluesDto.getCompanyId(), "follow_decorate_progres", selectAllByFiledIdVOS);
                    //不为空则设置名称
                    if (targetList != null && targetList.size() > 0) {
                        for (SelectAllByFiledIdVO idVO:targetList) {
                            if (idVO != null) {
                                SelectAttributeLevelInDto inDto1 = new SelectAttributeLevelInDto();
                                String parentAttributeId = targetList.get(0).getParentAttributeId();
                                inDto1.setFieldId("follow_decorate_progres");
                                inDto1.setAttributeId(parentAttributeId);
                                SelectAllByFiledIdVO selectAllByFiledIdVO = attributeLevelConfigService.selectAllByFiledIdAndAttributeId(inDto1);
                                if (selectAllByFiledIdVO != null) {
                                    userCluesDto.setDecorateProgres(selectAllByFiledIdVO.getAttributeValue() + "-" + idVO.getAttributeValue());
                                }
                            }
                        }
                    } else {
                        userCluesDto.setDecorateProgres(null);
                    }
                }
                //follow_server
                if (userCluesDto.getServiceAction() != null && !"".equals(userCluesDto.getServiceAction())) {
                    List<SelectAllByFiledIdVO> targetList = getAttributeLevelValueList(userCluesDto.getServiceAction(), userCluesDto.getCompanyId(), "follow_server", selectAllByFiledIdVOS);
                    //不为空则设置名称
                    if (targetList != null && targetList.size() > 0) {
                        //拼三级
                        SelectAttributeLevelInDto inDto1=new SelectAttributeLevelInDto();
                        StringBuffer sb=new StringBuffer();
                        for (SelectAllByFiledIdVO idVO:targetList) {
                            if (idVO != null) {
                                String parentAttributeId = idVO.getParentAttributeId();
                                inDto1.setFieldId("follow_server");
                                inDto1.setAttributeId(parentAttributeId);
                                SelectAllByFiledIdVO selectAllByFiledIdVO = attributeLevelConfigService.selectAllByFiledIdAndAttributeId(inDto1);
                                if (selectAllByFiledIdVO != null) {
                                    String source = CategoryCodeEnum.getCategory(selectAllByFiledIdVO.getCategoryCode());
                                     sb.append(source + "-" + selectAllByFiledIdVO.getAttributeValue() + "-" + idVO.getAttributeValue()).append(",");
                                }
                            }
                        }
                        if (sb.length() > 0 && ',' == sb.charAt(sb.length() - 1)) {
                            sb = sb.deleteCharAt(sb.length() - 1);
                        }
                        userCluesDto.setServiceAction(sb.toString());
                    } else {
                        userCluesDto.setServiceAction(null);
                    }
                }
                //house_type
                if (userCluesDto.getHouseType() != null && !"".equals(userCluesDto.getHouseType())) {
                    List<BaseAttributeVo> targetList = getAttributeValueList(userCluesDto.getHouseType(), userCluesDto.getCompanyId(), "house_type", findAttributeValueOutDtos);
                    //不为空则设置名称
                    if (targetList != null && targetList.size() > 0) {
                        userCluesDto.setHouseType(getAttributeNameByList(targetList));
                    } else {
                        userCluesDto.setHouseType(null);
                    }
                }
                //setContentText
                if ("appUserClues".equals(userCluesDto.getGenre())) {
                    userCluesDto.setDescription(setContentTextValue(userCluesDto));
//                    if ("4".equals(userCluesDto.getAppType())){
//                        userCluesDto.setContentText("");
//                    }
                }else {
                    userCluesDto.setContentText("");
                }
                FindCompanyAreaByOrgIdsOutDto findCompanyAreaByOrgIdsOutDto = findCompanyAreaByOrgIdsOutDtos.stream().filter(s -> s.getOrgId() != null && s.getOrgId().equals(userCluesDto.getCompanyId())).findAny().orElse(null);
                if (findCompanyAreaByOrgIdsOutDto != null) {
                    userCluesDto.setDistrictCode(findCompanyAreaByOrgIdsOutDto.getValueCode());
                    userCluesDto.setDistrictValue(findCompanyAreaByOrgIdsOutDto.getValueName());
                }
                //门店所属部门
                if (storeList != null && storeList.size() > 0){
                    FindStoreByOrgIdOutDto store = storeList.stream().filter(s -> s.getOrgId().equals(userCluesDto.getStoreId())).findFirst().orElse(null);
                    if (store != null ){
                        //setting门店简称
                        userCluesDto.setAbbreviation(store.getAbbreviation());
                        userCluesDto.setStoreName(store.getName());
                        if (store.getParentId() != null && store.getParentId() != 0) {
                            int index = store.getFullPathName().lastIndexOf("-");
                            if (index > 0) {
                                userCluesDto.setFullPathName(store.getFullPathName().substring(0, index));
                            } else {
                                userCluesDto.setFullPathName(store.getFullPathName());
                            }
                        }
                    }
                }
                //日志图片
                if("operator_marketing_log".equals(userCluesDto.getSourceTableName())){
                    if (logPictureDtos != null && logPictureDtos.size() > 0){
                        List<String> urls = new ArrayList<>();
                        logPictureDtos.stream().forEach(p -> {
                            if(userCluesDto.getId().equals(p.getSourceId())){
                                if (StringUtils.isNotEmpty(p.getCoverUrl()) && p.getCoverUrl().contains("https://encr-prod.oss-cn-shanghai.aliyuncs.com/")) {
                                    urls.add(AliConfig.getstsDTOMap(AliConstant.STS_ALI_CLOUD_UPLOAD).getOpenUrl() + p.getCoverUrl().replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/", ""));
                                } else {
                                    urls.add(p.getCoverUrl());
                                }
                            }
                        });
                        if (urls != null && urls.size() > 0){
                            getCoverUrl(userCluesDto, urls);
                        }
                    }
                }else if ("clues_follow_up".equals(userCluesDto.getSourceTableName())){
                    if (followPictureDtos != null && followPictureDtos.size() > 0){
                        List<String> urls = new ArrayList<>();
                        List<String> comeUrls = new ArrayList<>();
                        followPictureDtos.stream().forEach(p -> {
                            if (userCluesDto.getId().equals(p.getSourceId()) && !ObjectUtils.notEqual(p.getPictureType(), 1)) {
                                if (StringUtils.isNotEmpty(p.getCoverUrl()) && p.getCoverUrl().contains("https://encr-prod.oss-cn-shanghai.aliyuncs.com/")) {
                                    urls.add(AliConfig.getstsDTOMap(AliConstant.STS_ALI_CLOUD_UPLOAD).getOpenUrl() + p.getCoverUrl().replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/", ""));
                                } else {
                                    urls.add(p.getCoverUrl());
                                }

                            } else if (userCluesDto.getId().equals(p.getSourceId()) && ObjectUtils.notEqual(p.getPictureType(), 1)) {
                                if (StringUtils.isNotEmpty(p.getCoverUrl()) && p.getCoverUrl().contains("https://encr-prod.oss-cn-shanghai.aliyuncs.com/")) {
                                    comeUrls.add(AliConfig.getstsDTOMap(AliConstant.STS_ALI_CLOUD_UPLOAD).getOpenUrl() + p.getCoverUrl().replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/", ""));
                                } else {
                                    comeUrls.add(p.getCoverUrl());
                                }
//                                /**
//                                 * 厨房设计结果列表 type =2
//                                 */
//                                private String kitchenDesignImgs;
//                                /**
//                                 * 手绘图 type =3
//                                 */
//                                private String freehandSketchingImgs;
//                                /**
//                                 * 换装报告 type =4
//                                 */
//                                private String costumeChangeReportImgs;
                                if (ObjectUtils.notEqual(p.getPictureType(), 2)) {
                                    userCluesDto.setKitchenDesignFlagName("是");
                                } else {
                                    userCluesDto.setKitchenDesignFlagName("否");
                                }
                                if (ObjectUtils.notEqual(p.getPictureType(), 3)) {
                                    userCluesDto.setFreehandSketchingFlagName("是");
                                } else {
                                    userCluesDto.setFreehandSketchingFlagName("否");
                                }
                                if (ObjectUtils.notEqual(p.getPictureType(), 4)) {
                                    userCluesDto.setCostumeChangeReportFlagName("是");
                                } else {
                                    userCluesDto.setCostumeChangeReportFlagName("否");
                                }
                            }
                        });
                        if (urls != null && urls.size() > 0){
                            getCoverUrl(userCluesDto, urls);
                        }
                        if (CollectionUtils.isNotEmpty(comeUrls)) {
                            userCluesDto.setComeUrls(String.join(",", comeUrls));
                        }
                    }
                }

                //是否上门设计状态
                if ("0".equals(userCluesDto.getIsComeDevise())) {//否
                    if (userCluesDto.getComeDeviseStatus() == null || 0 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("否-空");
                    } else if (1 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("否-待上门");
                    }
                } else if ("1".equals(userCluesDto.getIsComeDevise())) {//是
                    if (userCluesDto.getComeDeviseStatus() == null || 0 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("是-空");
                    } else if (1 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("是-待上门");
                    } else if (2 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("是-已测量");
                    } else if (3 == userCluesDto.getComeDeviseStatus()) {
                        userCluesDto.setIsComeDevise("是-已出方案");
                    }
                }

            }

            //转换导出实体类
            operatorLogListOutDtos = CluesLogMapper.INSTANCE.operatorLogToCluesLogOutDto(resultList);
        }

        //生成Excel，并上传oss
        String fileName = "", sheetName = "线索日志报表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try{
            //写入excel文件流
            EasyExcel.write(os, OperatorLogListOutDto.class).sheet(sheetName).doWrite(operatorLogListOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        }catch (Exception e){
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());

            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        }finally {
            try {
                if (os != null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void getCoverUrl(SelectOperatorLogListOutDto userCluesDto, List<String> urls) {
        Integer i;
        for (i=0; i < urls.size(); i++){
            if (i == 0) {
                userCluesDto.setCoverUrl1(urls.get(i));
            }else if (i == 1) {
                userCluesDto.setCoverUrl2(urls.get(i));
            }else if (i == 2) {
                userCluesDto.setCoverUrl3(urls.get(i));
            }else if (i == 3) {
                userCluesDto.setCoverUrl4(urls.get(i));
            }else if (i == 4) {
                userCluesDto.setCoverUrl5(urls.get(i));
            }else if (i == 5) {
                userCluesDto.setCoverUrl6(urls.get(i));
            }else if (i == 6) {
                userCluesDto.setCoverUrl7(urls.get(i));
            }else if (i == 7) {
                userCluesDto.setCoverUrl8(urls.get(i));
            }else if (i == 8) {
                userCluesDto.setCoverUrl9(urls.get(i));
            }
        }
    }


    private List<LogPictureDto> findLogPicture(List<Long> ids, String sourceTableName) {
        int remainder = ids.size() % 2000;
        int size = (ids.size() / 2000);
        List<LogPictureDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = ids.subList(i * 2000, (i + 1) * 2000);
            List<LogPictureDto> pictureMarketings = activityImgDao.selectUrlByIds(subset, sourceTableName,null);
            result.addAll(pictureMarketings);
        }
        if (remainder > 0) {
            List<Long> subset = ids.subList(size * 2000, size * 2000 + remainder);
            List<LogPictureDto> pictureMarketings = activityImgDao.selectUrlByIds(subset, sourceTableName,null);
            result.addAll(pictureMarketings);
        }
        return result;
    }

    private List<FindVillageByIdOutDto> findVillageByVillageIds(List<Long> villageIds) {
        int remainder = villageIds.size() % 2000;
        int size = (villageIds.size() / 2000);
        List<FindVillageByIdOutDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = villageIds.subList(i * 2000, (i + 1) * 2000);
            FindVillageByVillageIdsInDto findVillageByVillageIdsInDto1 = new FindVillageByVillageIdsInDto();
            findVillageByVillageIdsInDto1.setVillageIds(subset);
            List<FindVillageByIdOutDto> villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto1).getData();
            result.addAll(villageList);
        }
        if (remainder > 0) {
            List<Long> subset = villageIds.subList(size * 2000, size * 2000 + remainder);
            FindVillageByVillageIdsInDto findVillageByVillageIdsInDto1 = new FindVillageByVillageIdsInDto();
            findVillageByVillageIdsInDto1.setVillageIds(subset);
            List<FindVillageByIdOutDto> villageList = villageClientService.findVillageByVillageIds2(findVillageByVillageIdsInDto1).getData();
            result.addAll(villageList);
        }
        return result;
    }

    private List<UserAddressOutDto> selectUserAddressByIdList(List<Long> addressIds) {
        int remainder = addressIds.size() % 2000;
        int size = (addressIds.size() / 2000);

        List<UserAddressOutDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = addressIds.subList(i * 2000, (i + 1) * 2000);
            List<UserAddressOutDto> data = userAddressClientService.selectUserAddressByIdList2(subset).getData();
            result.addAll(data);
        }
        if (remainder > 0) {
            List<Long> subset = addressIds.subList(size * 2000, size * 2000 + remainder);
            List<UserAddressOutDto> data = userAddressClientService.selectUserAddressByIdList2(subset).getData();
            result.addAll(data);
        }
        return result;
    }

    private List<String> setFieldIdsForLog() {
        List<String> list = new ArrayList<>();
        list.add("follow_type");
        list.add("follow_decorate_progres");
        list.add("follow_server");
        list.add("house_type");
        return list;
    }

    private List<String> setFieldIds(){
        List<String> list = new ArrayList<>();
        list.add("follow_decorate_progres");
        list.add("follow_server");
        list.add("lost_order");
        return list;
    }



    //新的值集层级
    private List<SelectAllByFiledIdVO> getAttributeLevelValueList(String targetIds, Long companyId, String fieldName, List<SelectAllByFiledIdVO> selectAllByFiledIdVOS) {
        List<SelectAllByFiledIdVO> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + selectAllByFiledIdVOS);
        if (selectAllByFiledIdVOS != null && selectAllByFiledIdVOS.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            selectAllByFiledIdVOS.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }
    private String getAttributeLevelNameByList(List<SelectAllByFiledIdVO> targetList) {
        String buf = new String();
        for (SelectAllByFiledIdVO idVO:targetList) {
            if (idVO != null) {
                log.info("错误数据：" + targetList.toString());
                buf = idVO.getAttributeValue();
            }
        }
        return buf;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }

    private List<BaseAttributeVo> getAttributeValueList(String targetIds, Long companyId, String fieldName, List<BaseAttributeVo> findAttributeValueOutDtos) {
        List<BaseAttributeVo> targetList = new ArrayList<>();
        log.debug("输入参数companyId：" + companyId);
        log.debug("输入参数fieldName：" + fieldName);
        log.debug("输入参数targetIds：" + targetIds);
        log.debug("输入参数findAttributeValueOutDtos：" + findAttributeValueOutDtos);
        if (findAttributeValueOutDtos != null && findAttributeValueOutDtos.size() > 0) {
            if (targetIds != null && !"".equals(targetIds)) {
                List<String> idLists = Arrays.asList(targetIds.split(","));
                if (idLists != null && idLists.size() > 0 && idLists.get(0) != null) {
                    targetList = idLists.stream().map(decId ->
                            findAttributeValueOutDtos.stream().filter(
                                    target ->
                                            fieldName.equals(target.getFieldId())
                                                    && decId.equals(target.getAttributeId())
                            ).findFirst().orElse(null)
                    ).collect(Collectors.toList());
                }
            }
        }
        return targetList;
    }

    private String getAttributeNameByList(List<BaseAttributeVo> targetList) {
        String buf = new String();
        if (targetList != null && targetList.size() > 0 && targetList.get(0) != null) {
            log.info("错误数据：" + targetList.toString());
            buf = targetList.stream().map(
                    bean -> bean != null && StringUtils.isNotEmpty(bean.getAttributeValue()) ?
                            bean.getAttributeValue() : "").collect(Collectors.joining(","));
        }
        return buf;
    }

    private String setContentTextValue(SelectOperatorLogListOutDto userCluesDto) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("跟进类型:").
                append(StringUtils.isEmpty(userCluesDto.getAppType()) ? "空" : userCluesDto.getAppType())
                .append("\n装修进度:").append(StringUtils.isEmpty(userCluesDto.getDecorateProgres()) ? "空" : userCluesDto.getDecorateProgres())
                .append("\n跟进动作:").append(StringUtils.isEmpty(userCluesDto.getServiceAction()) ? "空" : userCluesDto.getServiceAction())
                .append("\n跟进内容:").append(StringUtils.isEmpty(userCluesDto.getContentText()) ? "空" : userCluesDto.getContentText());
        return buffer.toString();
    }

    //分批次查询门店信息
    private List<FindStoreByOrgIdOutDto> findByOrgIds(List<Long> ids) {
        List<List<?>> lists = splitList(ids, 5000);
        List<FindStoreByOrgIdOutDto> result = new ArrayList<>();
        for (List<?> idList : lists) {
            List<Long> subset = (List<Long>) idList;
            List<FindStoreByOrgIdOutDto> storeList = storeClientService.findByOrgIds(new FindStoreByOrgIdsInDto(subset)).getData();
            result.addAll(storeList);
        }
        return result;
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<List<?>>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    public List<SelectOperatorLogListOutDto> getOpertorLogAndFollowupList(List<Long> cluesIds) {

        List<SelectOperatorLogListOutDto> resultList = new CopyOnWriteArrayList<>();
        //查询写跟进
        List<SelectOperatorLogListOutDto> followUpByCluesIds = operatorLogDao.newFollowUpByCluesIds(cluesIds);
        resultList.addAll(followUpByCluesIds);
        //查询日志
        List<SelectOperatorLogListOutDto> operatorLogByCluesIds = operatorLogDao.newExportOperatorLogByCluesIds(cluesIds);
        resultList.addAll(operatorLogByCluesIds);
        return resultList;
    }
}
