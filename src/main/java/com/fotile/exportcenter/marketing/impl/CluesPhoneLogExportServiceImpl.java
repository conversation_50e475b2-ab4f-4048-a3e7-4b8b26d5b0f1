package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.client.OrgClient;
import com.fotile.exportcenter.marketing.dao.OperatorLogDao;
import com.fotile.exportcenter.marketing.mapper.CluesLogMapper;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("1401")
@Slf4j
public class CluesPhoneLogExportServiceImpl implements CluesExportImpl {

    @Autowired
    private OperatorLogDao operatorLogDao;
    @Autowired
    private OrgClient orgClient;

    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;

    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        log.info("CluesPhoneLogExportServiceImpl.exportFile");
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count = 2000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        List<SelectOperatorLogListOutDto> resultList = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count;
            size = count;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            List<SelectOperatorLogListOutDto> LogList = operatorLogDao.exportPhoneLogExport(inDto);
            resultList.addAll(LogList);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);

            //线程休眠2秒
            try {
                log.error("***线程休眠开始***");
                Thread.sleep(2000);
                log.error("***线程休眠结束***");
            } catch (Exception e) {
                Thread.currentThread().interrupt();
            }
        }
        if (CollectionUtils.isEmpty(resultList)){
            return;
        }
        Set<Long> companyIds1Set = new HashSet<>();

        for (int r = 0; r < resultList.size(); r++) {
            SelectOperatorLogListOutDto result = resultList.get(r);
            result.setIndex(r + 1);
            if (result.getCompanyId() != null) {
                companyIds1Set.add(result.getCompanyId());
            }
            if (StringUtils.isNotBlank(result.getOldData())) {
                Map map = JSON.parseObject(result.getOldData(), Map.class);
                result.setDialchargeCode((String) map.get("dialchargeCode"));
                result.setDialChargeUserName((String) map.get("dialchargeName"));
                result.setDialSource((String) map.get("dialSource"));
            }
        }
        List<Long> companyIds1 = new ArrayList<>(companyIds1Set);

        FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
        findCompanyByIdsInDto.setIdList(companyIds1);
        List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = new ArrayList<>();
        try {
            findCompanyAreaByOrgIdsOutDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
        } catch (Exception e) {
            log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
        }
        if (CollectionUtils.isNotEmpty(findCompanyAreaByOrgIdsOutDtos)) {
            Map<Long, FindCompanyAreaByOrgIdsOutDto> companyMap = findCompanyAreaByOrgIdsOutDtos.stream().collect(Collectors.toMap(FindCompanyAreaByOrgIdsOutDto::getOrgId, Function.identity()));
            for (SelectOperatorLogListOutDto result : resultList) {
                if (result.getCompanyId() != null) {
                    FindCompanyAreaByOrgIdsOutDto findCompanyAreaByOrgIdsOutDto = companyMap.get(result.getCompanyId());
                    if (findCompanyAreaByOrgIdsOutDto != null) {
                        result.setDistrictValue(findCompanyAreaByOrgIdsOutDto.getValueName());
                        result.setDistrictCode(findCompanyAreaByOrgIdsOutDto.getValueCode());

                    }
                }
            }
        }

        //生成Excel，并上传oss
        String fileName = "", sheetName = "线索日志报表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        List<CluesPhoneLogListOutDTO> operatorLogListOutDtos = new ArrayList<>();
        operatorLogListOutDtos = CluesLogMapper.INSTANCE.cluesPhoneLogListOutDTO(resultList);
        try {
            //写入excel文件流
            EasyExcel.write(os, CluesPhoneLogListOutDTO.class).sheet(sheetName).doWrite(operatorLogListOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        } catch (Exception e) {
            log.error("电话日志线索日志报表导出失败", e);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return;
    }
}
