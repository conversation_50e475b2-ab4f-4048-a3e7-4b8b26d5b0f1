package com.fotile.exportcenter.marketing.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.fotile.exportcenter.client.*;
import com.fotile.exportcenter.marketing.constant.CategoryCodeEnum;
import com.fotile.exportcenter.marketing.dao.*;
import com.fotile.exportcenter.marketing.mapper.CluesDetailMapper;
import com.fotile.exportcenter.marketing.mapper.CluesLogMapper;
import com.fotile.exportcenter.marketing.pojo.dto.*;
import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.entity.TCluesDesignInfo;
import com.fotile.exportcenter.marketing.pojo.vo.BaseAttributeVo;
import com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import com.fotile.exportcenter.marketing.service.TAttributeLevelConfigService;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.data.common.config.AliConfig;
import com.fotile.framework.data.common.constant.AliConstant;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component("106")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesFollowServiceExportImplService implements CluesExportImpl {
    @Autowired
    private OperatorLogDao operatorLogDao;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserAddressClientService userAddressClientService;
    @Autowired
    private VillageClientService villageClientService;
    @Autowired
    private BaseAttributeMapper baseAttributeMapper;
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private OssService ossService;
    @Autowired
    private StoreClientService storeClientService;
    @Autowired
    private ActivityImgDao activityImgDao;
    @Autowired
    private TAttributeLevelConfigService attributeLevelConfigService;
    @Autowired
    UserCluesDao userCluesDao;
    @Autowired
    private TCluesDesignInfoDao cluesDesignInfoDao;


    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {
        QueryUserCluseInDto inDto = JsonUtils.parse(exportTaskRecord.getParamJson(), QueryUserCluseInDto.class);
        inDto.setAesKey(MybatisMateConfig.getPassword());
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)){
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())){
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count = 50000;
        Integer start1 = inDto.getStart();
        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        List<Long> cluesIds = new ArrayList<>();
        for (int j = 0; j <= i; j++) {
            Integer start = start1 + (j) * count;
            size = count;
            if (j == i) {
                size = lastCount;
            }
            inDto.setStart(start);
            inDto.setSize(size);
            //查询线索集合
//            List<Long> logCluesExport = userCluesDao.findLogCluesExport(inDto);
            List<Long> logCluesExport = userCluesDao.newCluesFollowServiceExport2(inDto);
            cluesIds.addAll(logCluesExport);
            //更新任务进度
            BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            oldProgress = newProgress.add(oldProgress);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
            dataClientService.updateTask(exportTask);

            //线程休眠2秒
            try {
                log.error("***线程休眠开始***");
                Thread.sleep(2000);
                log.error("***线程休眠结束***");
            } catch (Exception e) {
                Thread.currentThread().interrupt();
            }
        }
        if (CollectionUtils.isEmpty(cluesIds)) {
            throw new RuntimeException("未能查询到结果！");
        }

        Integer size1 = cluesIds.size();
        log.error("查询id结果数量："+size1);
        List<NewCluesFollowServiceVo> resultList = new CopyOnWriteArrayList<>();

        List<CompletableFuture> completableFutureList = new LinkedList<>();
        Integer total = size1 / 2000;
        Integer remainder = size1 % 2000;
        Integer pageSize = 2000;
        for (Integer cluesIdx = 0; cluesIdx <= total; cluesIdx++) {

            List<Long> collect = null;
            if (cluesIdx == total ) {
                if (remainder == 0) {
                    continue;
                }
                collect =  cluesIds.stream().skip((cluesIdx) * pageSize).limit(remainder).collect(Collectors.toList());
            } else {
                collect = cluesIds.stream().skip((cluesIdx) * pageSize).limit(pageSize).collect(Collectors.toList());
            }
            List<Long> finalCollect = collect;
            //查询写跟进
            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                return userCluesDao.newCluesFollowServiceExport(finalCollect);
            }).thenAcceptAsync((result) -> {
                resultList.addAll(result);
            }));
        }
        completableFutureList.stream().forEach(CompletableFuture::join);

        log.error("查询结果数量："+resultList.size());
        if (CollectionUtils.isEmpty(resultList)) {
            throw new RuntimeException("未能查询到结果！");
        }
        //排序根据sourceId 正序 再createdDate倒叙 空排最后
        resultList.sort(Comparator.comparing(NewCluesFollowServiceVo::getId, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(NewCluesFollowServiceVo::getFollowDate, Comparator.nullsLast(Comparator.reverseOrder())).reversed());


        //设置参数
        Set<Long> idsSet = new HashSet<>();
        Set<Long> companyIds1Set = new HashSet<>();
        HashSet<Long> storeIdSet = new HashSet<Long>();
        HashSet<Long> followIdSet = new HashSet<Long>();
        for (NewCluesFollowServiceVo clusesOutDto : resultList) {
            if (clusesOutDto.getId() != null) {
                idsSet.add(clusesOutDto.getId());
            }
            if (clusesOutDto.getCompanyId() != null) {
                companyIds1Set.add(clusesOutDto.getCompanyId());
            }
            followIdSet.add(clusesOutDto.getFollowId());
        }
        //set转换list
        List<Long> ids = new ArrayList<>(idsSet);
        List<Long> companyIds1 = new ArrayList<>(companyIds1Set);
        List<Long> storeIdList = new ArrayList<>(storeIdSet);
        List<Long> followIds = new ArrayList<>(followIdSet);


        List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(companyIds1)) {
            try {
                FindCompanyByIdsInDto findCompanyByIdsInDto = new FindCompanyByIdsInDto();
                findCompanyByIdsInDto.setIdList(companyIds1);
                findCompanyAreaByOrgIdsOutDtos = orgClient.findCompanyByOrgIdList(findCompanyByIdsInDto).getData();
            } catch (Exception e) {
                log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
            }
        log.error("查询公司信息结束："+companyIds1.size());
        }
        List<NewCluesFollowServiceDto> excelData = new ArrayList<>();
        if (ids != null && ids.size() > 0) {
            //查询是否有上门设计报告
            List<TCluesDesignInfo> cluesDesignInfoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ids)) {
                cluesDesignInfoList = findCluesDesign(ids);
            }
            //查询门店信息
            List<FindStoreByOrgIdOutDto> storeList = new ArrayList<>();
            if (storeIdList != null && storeIdList.size() > 0) {
                storeList = findByOrgIds(storeIdList);
            }
            List<FindAttributeValueOutDto> followType = baseAttributeMapper.getByFiledId("follow_type");
            List<TAttributeLevelConfig> attributeLevelConfigs = attributeLevelConfigService.selectListByFieldIdAndLevel("follow_server",null,null);
            //获取日志图片
            List<LogPictureDto> followPictureDtos = null;
            //获取跟进图片
            if (followIds != null && followIds.size() > 0){
                followPictureDtos = findLogPicture(followIds, "clues_follow_up");
            }
            log.error("跟进图片："+followPictureDtos.size());

            for (int r=0; r<resultList.size(); r++) {
                NewCluesFollowServiceVo userCluesDto = resultList.get(r);
                userCluesDto.setIndex(r+1);
                //跟进次数
                List<NewCluesFollowServiceVo> collect = resultList.stream().filter(l -> userCluesDto.getId().equals(l.getId())).collect(Collectors.toList());
                userCluesDto.setFollowUpCount(collect.size());
                //脱敏操作影藏地址和电话
                if ("1".equals(inDto.getFlag())) {
                    if (StringUtils.isNotBlank(userCluesDto.getCustomerName())){
                        if (userCluesDto.getCustomerName().length() == 1){
                            userCluesDto.setCustomerName(userCluesDto.getCustomerName()+"***"+userCluesDto.getCustomerName());
                        }else {
                            String firstName = userCluesDto.getCustomerName().substring(0, 1);
                            String lastName = userCluesDto.getCustomerName().substring(userCluesDto.getCustomerName().length() - 1);
                            userCluesDto.setCustomerName(firstName + "***" + lastName);
                        }
                    }
                }

                if (StringUtils.isNotEmpty(userCluesDto.getType())) {
                    Optional<FindAttributeValueOutDto> first = followType.stream().filter(a -> a.getAttributeId().equals(userCluesDto.getType())).findFirst();
                    if (first.isPresent()){
                        userCluesDto.setType(first.get().getAttributeValue());
                    }else {
                        userCluesDto.setType("");
                    }
                }

                //follow_server
                if (StringUtils.isNotEmpty(userCluesDto.getServiceAction())) {
                    String decName = "";
                    if (CollectionUtils.isNotEmpty(attributeLevelConfigs)) {
                        for (TAttributeLevelConfig find : attributeLevelConfigs) {
                            if (Arrays.asList(userCluesDto.getServiceAction().split(",")).contains(find.getAttributeId())) {
                                String value = "";
                                value = find.getAttributeValue();
                                if (StringUtils.isEmpty(decName)) {
                                    decName = value;
                                } else {
                                    decName = decName + "," + value;
                                }
                            }
                        }
                    }
                    userCluesDto.setServiceAction(decName);
                }

                FindCompanyAreaByOrgIdsOutDto findCompanyAreaByOrgIdsOutDto = findCompanyAreaByOrgIdsOutDtos.stream().filter(s -> s.getOrgId() != null && s.getOrgId().equals(userCluesDto.getCompanyId())).findAny().orElse(null);
                if (findCompanyAreaByOrgIdsOutDto != null) {
                    userCluesDto.setDistrictValue(findCompanyAreaByOrgIdsOutDto.getValueName());
                }
                //门店所属部门
                if (storeList != null && storeList.size() > 0){
                    FindStoreByOrgIdOutDto store = storeList.stream().filter(s -> s.getOrgId().equals(userCluesDto.getStroeId())).findFirst().orElse(null);
                    if (store != null ){
                        //setting门店简称
                        userCluesDto.setStroeName(store.getAbbreviation());
                    }
                }
                //日志图片
                if (followPictureDtos != null && followPictureDtos.size() > 0){
                    List<String> urls = new ArrayList<>();
                    List<String> comeUrls = new ArrayList<>();
                    followPictureDtos.stream().forEach(p -> {
                        if(userCluesDto.getFollowId().equals(p.getSourceId())&& !ObjectUtils.notEqual(p.getPictureType(), 1)){
                            if (StringUtils.isNotEmpty(p.getCoverUrl()) && p.getCoverUrl().contains("https://encr-prod.oss-cn-shanghai.aliyuncs.com/")) {
                                urls.add(AliConfig.getstsDTOMap(AliConstant.STS_ALI_CLOUD_UPLOAD).getOpenUrl() + p.getCoverUrl().replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/", ""));
                            } else {
                                urls.add(p.getCoverUrl());
                            }
                        }else if (userCluesDto.getFollowId().equals(p.getSourceId()) && ObjectUtils.notEqual(p.getPictureType(), 1)) {
                            if (StringUtils.isNotEmpty(p.getCoverUrl()) && p.getCoverUrl().contains("https://encr-prod.oss-cn-shanghai.aliyuncs.com/")) {
                                comeUrls.add(AliConfig.getstsDTOMap(AliConstant.STS_ALI_CLOUD_UPLOAD).getOpenUrl() + p.getCoverUrl().replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/", ""));
                            } else {
                                comeUrls.add(p.getCoverUrl());
                            }
//                                /**
//                                 * 厨房设计结果列表 type =2
//                                 */
//                                private String kitchenDesignImgs;
//                                /**
//                                 * 手绘图 type =3
//                                 */
//                                private String freehandSketchingImgs;
//                                /**
//                                 * 换装报告 type =4
//                                 */
//                                private String costumeChangeReportImgs;
                            if (!ObjectUtils.notEqual(p.getPictureType(), 2)) {
                                userCluesDto.setKitchenDesignFlagName("是");
                            }
                            if (!ObjectUtils.notEqual(p.getPictureType(), 3)) {
                                userCluesDto.setFreehandSketchingFlagName("是");
                            }
                            if (!ObjectUtils.notEqual(p.getPictureType(), 4)) {
                                userCluesDto.setCostumeChangeReportFlagName("是");
                            }
                        }
                    });
                    if (StringUtils.isEmpty(userCluesDto.getFreehandSketchingFlagName())){
                        userCluesDto.setFreehandSketchingFlagName("否");
                    }
                    if (StringUtils.isEmpty(userCluesDto.getKitchenDesignFlagName())){
                        userCluesDto.setKitchenDesignFlagName("否");
                    }
                    if (StringUtils.isEmpty(userCluesDto.getCostumeChangeReportFlagName())){
                        userCluesDto.setCostumeChangeReportFlagName("否");
                    }
                    if (urls != null && urls.size() > 0){
                        getCoverUrl(userCluesDto, urls);
                    }
                    if (CollectionUtils.isNotEmpty(comeUrls)) {
                        userCluesDto.setComeUrls(String.join(",", comeUrls));
                    }

                }

                //是否有上门设计报告
                userCluesDto.setIsComeDeviseReport("否");
                if (CollectionUtils.isNotEmpty(cluesDesignInfoList)){
                    Optional<TCluesDesignInfo> first = cluesDesignInfoList.stream().filter(d -> d.getCluesId().equals(userCluesDto.getId())).findFirst();
                    if (first.isPresent()){
                        userCluesDto.setIsComeDeviseReport("是");
                    }
                }

                //质量评价结果 0待评价，1符合标准，2不符合标准
                if (StringUtils.isNotEmpty(userCluesDto.getEvaluateStatus())) {
                    if ("0".equals(userCluesDto.getEvaluateStatus())) {
                        userCluesDto.setEvaluateStatus("待评价");
                    } else if ("1".equals(userCluesDto.getEvaluateStatus())) {
                        userCluesDto.setEvaluateStatus("符合标准");
                    } else if ("2".equals(userCluesDto.getEvaluateStatus())) {
                        userCluesDto.setEvaluateStatus("不符合标准");
                    }
                }

            }

            //转换导出实体类
            excelData = CluesDetailMapper.INSTANCE.newExportToCluesFollowServiceOutDto(resultList);
        }

        //生成Excel，并上传oss
        String fileName = "", sheetName = "服务动作报表";
        fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try{
            //写入excel文件流
            EasyExcel.write(os, NewCluesFollowServiceDto.class).sheet(sheetName).doWrite(excelData);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            //成功
            dataClientService.successTask(exportTask);
        }catch (Exception e){
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());

            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        }finally {
            try {
                if (os != null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void getCoverUrl(NewCluesFollowServiceVo userCluesDto, List<String> urls) {
        Integer i;
        for (i=0; i < urls.size(); i++){
            if (i == 0) {
                userCluesDto.setCoverUrl1(urls.get(i));
            }else if (i == 1) {
                userCluesDto.setCoverUrl2(urls.get(i));
            }else if (i == 2) {
                userCluesDto.setCoverUrl3(urls.get(i));
            }else if (i == 3) {
                userCluesDto.setCoverUrl4(urls.get(i));
            }else if (i == 4) {
                userCluesDto.setCoverUrl5(urls.get(i));
            }else if (i == 5) {
                userCluesDto.setCoverUrl6(urls.get(i));
            }else if (i == 6) {
                userCluesDto.setCoverUrl7(urls.get(i));
            }else if (i == 7) {
                userCluesDto.setCoverUrl8(urls.get(i));
            }else if (i == 8) {
                userCluesDto.setCoverUrl9(urls.get(i));
            }
        }
    }


    private List<LogPictureDto> findLogPicture(List<Long> ids, String sourceTableName) {
        int remainder = ids.size() % 2000;
        int size = (ids.size() / 2000);
        List<LogPictureDto> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = ids.subList(i * 2000, (i + 1) * 2000);
            List<LogPictureDto> pictureMarketings = activityImgDao.selectUrlByIds(subset, sourceTableName,null);
            result.addAll(pictureMarketings);
        }
        if (remainder > 0) {
            List<Long> subset = ids.subList(size * 2000, size * 2000 + remainder);
            List<LogPictureDto> pictureMarketings = activityImgDao.selectUrlByIds(subset, sourceTableName,null);
            result.addAll(pictureMarketings);
        }
        return result;
    }

    //分批次查询门店信息
    private List<FindStoreByOrgIdOutDto> findByOrgIds(List<Long> ids) {
        List<List<?>> lists = splitList(ids, 5000);
        List<FindStoreByOrgIdOutDto> result = new ArrayList<>();
        for (List<?> idList : lists) {
            List<Long> subset = (List<Long>) idList;
            List<FindStoreByOrgIdOutDto> storeList = storeClientService.findByOrgIds(new FindStoreByOrgIdsInDto(subset)).getData();
            result.addAll(storeList);
        }
        return result;
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<List<?>>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    private List<TCluesDesignInfo> findCluesDesign(List<Long> ids) {
        int remainder = ids.size() % 2000;
        int size = (ids.size() / 2000);
        List<TCluesDesignInfo> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            List<Long> subset = ids.subList(i * 2000, (i + 1) * 2000);
            List<TCluesDesignInfo> tCluesDesignInfos = cluesDesignInfoDao.queryByCluesIds(subset);
            result.addAll(tCluesDesignInfos);
        }
        if (remainder > 0) {
            List<Long> subset = ids.subList(size * 2000, size * 2000 + remainder);
            List<TCluesDesignInfo> tCluesDesignInfos = cluesDesignInfoDao.queryByCluesIds(subset);
            result.addAll(tCluesDesignInfos);
        }
        return result;
    }

}
