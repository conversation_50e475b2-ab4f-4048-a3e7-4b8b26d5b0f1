package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fotile.exportcenter.marketing.pojo.entity.PictureMarketing;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SelectOperatorLogListOutDto implements Serializable {
    private Long id;
    /**
     * 日志类型 
     */
    private String type;

    /**
     * 跟进标题
     */
    private String title;

    /**
     * 描述内容
     */
    private String description;

    /**
     * 跟进内容
     */
    private String contentText;

    /**
     * 装修进度
     */
    private String decorateProgres;

    /**
     * 服务动作
     */
    private String serviceAction;

    /**
     * 跟进时间
     */
    private Date followDate;

    /**
     * 地址
     */
    @FieldEncrypt
    private String address;

    /**
     * 业务员ID
     */
    private String chargeUserId;

    /**
     * 业务员姓名
     */
    @FieldEncrypt
    private String chargeUserName;
    /**
     * 图片列表
     */
    private List<PictureMarketing> pictureMarketingList;
    /**
     * 关联表名
     */
    private String sourceTableName;
    /**
     * 服务过程描述
     */
    private String describe;
    /**
     * genre
     */
    private String genre;
    /**
     * 跟进类型 2（微信），3（到访），1（电话），5（上门设计），4（进店）
     */
    private String appType;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 服务时间
     */
    private Date serviceDate;
    /**
     * 关联表id
     */
    private String sourceId;
    /**
     * 顾客姓名
     */
    @FieldEncrypt
    private String customerName;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 顾客电话
     */
    @FieldEncrypt
    private String customerPhone;
    /**
     * 微信号
     */
    private String wechatno;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店code
     */
    private String stroeCode;
    /**
     * 业务员
     */
    @FieldEncrypt
    private String salesMan;
    /**
     * 小区
     */
    private String village;
    /**
     * 楼盘类型
     */
    private String houseType;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 地址id
     */
    private Long addressId;
    /**
     * 小区id
     */
    private Long villageId;
    /**
     * 创建人名称
     */
    @FieldEncrypt
    private String createUserName;

    /**
     * 大区code
     */
    private String districtCode;
    /**
     * 大区名称
     */
    private String districtValue;
    /**
     * 客户名称（经销商）
     */
    private String distributorName;
    /**
     * 门店所属部门
     */
    private String fullPathName;

    /**
     * 序号
     */
    private Integer index;

    /**
     * 门店简称
     */
    private String abbreviation;
    /**
     * 负责人名称
     */
    private String chargeCode;

    /**
     * 附件1,日志图片
     */
    private String coverUrl1;

    /**
     * 附件2
     */
    private String coverUrl2;

    /**
     * 附件3
     */
    private String coverUrl3;

    /**
     * 附件4
     */
    private String coverUrl4;

    /**
     * 附件5
     */
    private String coverUrl5;

    /**
     * 附件6
     */
    private String coverUrl6;

    /**
     * 附件7
     */
    private String coverUrl7;

    /**
     * 附件8
     */
    private String coverUrl8;

    /**
     * 附件9
     */
    private String coverUrl9;

    private String comeUrls;
    /**
     * 跟进效率
     */
    private String followEfficiencyCategoryValue;
    /**
     * 跟进
     */
    private Integer followEfficiencyTime;
    /**
     * 跟進狀態 跟进状态 1.未跟进 2已跟进 3 已进店 4 已成交
     */
    private String followUpStatus;
    /**
     * 是否上门设计1是0否
     */
    private String isComeDevise;
    /**
     * 得分
     */
    private Integer score;
    /**
     * 上门设计状态,1:带上门，2：已测量，3：已设计
     */
    private Integer comeDeviseStatus;

    private String oldData;

    @ApiModelProperty("来源")
    private String dialSource;

    @ApiModelProperty("拨打时间")
    private Date createdDate;

    @ApiModelProperty("拨打电话业务员姓名")
    private String dialChargeUserName;

    @ApiModelProperty("拨打电话业务员编码")
    private String dialchargeCode;
    /**
     * 驻店时长
     */
    private Integer lengthOfStay;

    @ApiModelProperty("下次跟进时间")
    private Date nextFollowUpTime;
    @ApiModelProperty("跟进次数")
    private Integer followUpCount;
    //是否有上门设计报告
    private String isComeDeviseReport;
    //质量评价结果
    private String evaluateResultDesc;
    //质量评价人
    private String evaluateUserName;
    //质量评价时间
    private Date evaluateTime;

    /**
     *是否测量完整尺寸 （1.是 0.否）
     */

    private String kitchenDesignFlagName;
    /**
     * 是否含换装报告（1.是 0.否
     */

    private String costumeChangeReportFlagName;
    /**
     * 是否上传手绘图（1.是 0.否）
     */

    private String freehandSketchingFlagName;

}
