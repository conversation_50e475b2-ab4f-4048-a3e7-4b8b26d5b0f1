package com.fotile.exportcenter.marketing.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@Data
public class UserAddressOutDto implements Serializable {
    private Long id;
    @TableField(value = "customer_info_id")
    @ApiModelProperty(value = "顾客id")
    private Long customerInfoId;

    @TableField(value = "is_default")
    @ApiModelProperty(value = "是否为默认地址, 0-不是 1-是")
    private Integer isDefault;

    @NotBlank(message = "收货人姓名不能为空")
    @TableField(value = "name")
    @ApiModelProperty(value = "收货人姓名")
    private String name;

    @NotBlank(message = "联系方式不能为空")
    @TableField(value = "phone")
    @ApiModelProperty(value = "联系方式")
    private String phone;

    @TableField(value = "province_id")
    @ApiModelProperty(value = "省id")
    private Long provinceId;

    @TableField(value = "city_id")
    @ApiModelProperty(value = "市id")
    private Long cityId;

    @TableField(value = "county_id")
    @ApiModelProperty(value = "县/区id")
    private Long countyId;

    @TableField(value = "province_name")
    @ApiModelProperty(value = "省")
    private String provinceName;

    @TableField(value = "city_name")
    @ApiModelProperty(value = "市")
    private String cityName;

    @TableField(value = "county_name")
    @ApiModelProperty(value = "县")
    private String countyName;

    @TableField(value = "address")
    @ApiModelProperty(value = "地址")
    private String address;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(value = "house_type")
    @ApiModelProperty(value = "房屋类型")
    private Integer houseType;

    @TableField(value = "self_provice_name")
    @ApiModelProperty(value = "自建房-省名称")
    private String selfProviceName;
    @TableField(value = "self_city_name")
    @ApiModelProperty(value = "自建房-市名称")
    private String selfCityName;
    @TableField(value = "self_county_name")
    @ApiModelProperty(value = "自建房-区名称")
    private String selfCountyName;

    @TableField(value = "street")
    @ApiModelProperty(value = "街道")
    private String street;

    @TableField(value = "building")
    @ApiModelProperty(value = "楼栋")
    private String building;

    @TableField(value = "unit")
    @ApiModelProperty(value = "单元")
    private String unit;

    @TableField(value = "house_number")
    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    @TableField(value = "check_in_date")
    @ApiModelProperty(value = "入住时间")
    private Date checkInDate;

    @TableField(value = "install_date")
    @ApiModelProperty(value = "安装时间")
    private Date installDate;



}
