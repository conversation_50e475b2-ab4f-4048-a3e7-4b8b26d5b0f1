package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 值集层级维护表
    */
@ApiModel(value="值集层级维护表")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "`marketingcenter`.`t_attribute_level_config`")
public class TAttributeLevelConfig extends AuditingEntity {
    /**
     * 字段英名
     */
    @TableField(value = "`field_id`")
    @ApiModelProperty(value="字段英名")
    private String fieldId;

    /**
     * 属性值ID
     */
    @TableField(value = "`attribute_id`")
    @ApiModelProperty(value="属性值ID")
    private String attributeId;

    /**
     * 属性值名称
     */
    @TableField(value = "`attribute_value`")
    @ApiModelProperty(value="属性值名称")
    private String attributeValue;

    /**
     * 排序
     */
    @TableField(value = "`attribute_sort`")
    @ApiModelProperty(value="排序")
    private Long attributeSort;

    /**
     * 层级
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value="层级")
    private Integer level;

    /**
     * 父级属性id
     */
    @TableField(value = "`parent_attribute_id`")
    @ApiModelProperty(value="父级属性id")
    private String parentAttributeId;

    /**
     * 所属大类值,目前只有服务动作有，成交前，成交后-安装前，安装后
     */
    @TableField(value = "`category_code`")
    @ApiModelProperty(value="所属大类值,目前只有服务动作有，成交前，成交后-安装前，安装后")
    private String categoryCode;

    public static final String COL_ID = "id";

    public static final String COL_FIELD_ID = "field_id";

    public static final String COL_ATTRIBUTE_ID = "attribute_id";

    public static final String COL_ATTRIBUTE_VALUE = "attribute_value";

    public static final String COL_ATTRIBUTE_SORT = "attribute_sort";

    public static final String COL_LEVEL = "level";

    public static final String COL_PARENT_ATTRIBUTE_ID = "parent_attribute_id";

    public static final String COL_CATEGORY_CODE = "category_code";

    public static final String COL_IS_DELETED = "is_deleted";

    public static final String COL_CREATED_BY = "created_by";

    public static final String COL_CREATED_DATE = "created_date";

    public static final String COL_MODIFIED_BY = "modified_by";

    public static final String COL_MODIFIED_DATE = "modified_date";
}