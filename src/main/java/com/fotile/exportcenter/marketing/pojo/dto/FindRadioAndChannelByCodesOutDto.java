package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 渠道实体类----一个渠道对应多个频道
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value = "渠道",description = "渠道")
@Accessors(chain = true)
public class FindRadioAndChannelByCodesOutDto implements Serializable {
    
	@ApiModelProperty(value="渠道id",example="渠道id")
    private Long channelId;
	
    @ApiModelProperty(value="渠道编码",example="渠道编码")
    private String channelCode;

    @ApiModelProperty(value="渠道名称",example="渠道名称")
    private String channelName;
    
	@ApiModelProperty(value="频道id",example="频道id")
    private Long radioId;
	
    @ApiModelProperty(value="频道编码",example="频道编码")
    private String radioCode;

    @ApiModelProperty(value="频道名称",example="频道名称")
    private String radioName;
}
