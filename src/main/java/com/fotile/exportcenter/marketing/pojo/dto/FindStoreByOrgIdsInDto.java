package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindStoreByOrgIdsInDto {

	@ApiModelProperty("门店orgId")
	@NotNull(message="门店orgId不能为空")
	private List<Long> orgIds;
}