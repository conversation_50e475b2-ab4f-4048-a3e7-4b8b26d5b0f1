package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Entity;

@Data
@Entity
@ApiModel(value = "组织结构表", description = "组织结构表")
@TableName(value = "operator_marketing_log", schema = "marketingcenter")
public class OperatorMarketingLog extends AuditingEntity {

    /**
     * 操作人名称
     */
    @TableField(value = "operator_name")
    @FieldEncrypt
    private String operatorName;

    /**
     * 操作类型1.新增2.修改 3.员工操作
     */
    @TableField(value = "type")
    private String type;

    /**
     * 老数据（原数据）
     */
    @TableField(value = "old_data")
    private String oldData;

    /**
     * 新数据
     */
    @TableField(value = "new_data")
    private String newData;

    /**
     * 事件描述COde
     */
    @TableField(value = "event_description_code")
    private String eventDescriptionCode;

    /**
     * 事件描述
     */
    @TableField(value = "event_description")
    private String eventDescription;


    /**
     * 来源Id
     */
    @TableField(value = "source_id")
    private Long sourceId;
    /**
     * 来源表
     */
    @TableField(value = "source_table_name")
    private String sourceTableName;

    /**
     * 来源系统员工跟进操作ID
     */
    @TableField(value = "source_follow_id")
    private String sourceFollowId;
    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 类型：1.电话，2.微信，3.到访
     */
    @TableField(value = "follow_type")
    private String followType;
    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;


}
