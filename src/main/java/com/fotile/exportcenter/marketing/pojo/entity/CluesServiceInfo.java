package com.fotile.exportcenter.marketing.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class CluesServiceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	
	 /**
     * 主键id
     */
	private Long id;
	
	 /**
     * 是否删除 0：否；其他：是
     */
	private Long isDeleted;
	
	 /**
     * 创建者
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	 /**
     * 创建时间
     */
	private Date createdDate;
	
	 /**
     * 修改者
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	 /**
     * 修改时间
     */
	private Date modifiedDate;
	
	 /**
     * 线索id
     */
	private Long cluesId;
	
	 /**
     * csm服务工单编码
     */
	private String csmOrderCode;
	
	 /**
     * 工单类型
     */
	private String csmOrderType;
	
	 /**
     * 服务项目
     */
	private String serviceCategory;
	
	 /**
     * 产品线
     */
	private String productCategory;
	
	 /**
     * 购买渠道
     */
	private String purchaseChannel;
	
	 /**
     * 联系人
     */
	 @FieldEncrypt
	private String contact;
	
	 /**
     * 联系电话
     */
	 @FieldEncrypt
	private String contactTel;
	
	 /**
     * 技师编号
     */
	private String serviceEngineerCsn;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	 /**
     * 服务完成时间
     */
	private Date serviceCompleteDate;
	
	 /**
     * 技师手机号
     */
	 @FieldEncrypt
	private String serviceEngineerMobile;
	
	 /**
     * 省
     */
	private String ftContactState;
	
	 /**
     * 省code
     */
	private Long ftContactStateCode;
	
	 /**
     * 市
     */
	private String ftContactCity;
	
	 /**
     * 市code
     */
	private Long ftContactCityCode;
	
	 /**
     * 区
     */
	private String ftContactCounty;
	
	 /**
     * 区code
     */
	private Long ftContactCountyCode;
	
	 /**
     * 详细地址
     */
	 @FieldEncrypt
	private String contactAddressLine;
	
	 /**
     * 工单状态
     */
	private String csmOrderStatus;
	
	 /**
     * 保内外
     */
	private String csmWarrantyFlag;
}