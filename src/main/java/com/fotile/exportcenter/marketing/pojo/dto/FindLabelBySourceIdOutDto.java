package com.fotile.exportcenter.marketing.pojo.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class FindLabelBySourceIdOutDto implements Serializable {
    private Long id;
    /**
     * 源表
     */
    private String sourceTableName;

    /**
     * 源表Id
     */
    private Long sourceId;

    /**
     * 标签顺序
     */
    private Long labelSort;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签id
     */
    private String labelId;
}
