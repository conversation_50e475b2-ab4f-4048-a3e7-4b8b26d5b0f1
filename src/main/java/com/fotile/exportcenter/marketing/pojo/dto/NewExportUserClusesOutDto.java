package com.fotile.exportcenter.marketing.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class NewExportUserClusesOutDto implements Serializable {

    private Long id;
    //渠道来源编码
    private String channelCode;
    //渠道来源名称
    private String channelName;
    //频道编码
    private String radioCode;
    //频道名称
    private String radioName;
    //活动id
    private Long activityId;
    //活动id
    private String activityId1;
    //活动类型
    private String activityType;
    //活动名称
    private String activityName;
    //顾客姓名
    @FieldEncrypt
    private String customerName;
    //性别 1男 2 女
    private String gender;
    //手机号
    @FieldEncrypt
    private String customerPhone;
    private String wechatno;
    //留资时间
    private Date fundingTime;
    //分公司
    private String companyName;
    //门店id
    private Long storeId;
    //门店
    private String stroeName;
    //门店code
    private String stroeCode;
    //业务员
    @FieldEncrypt
    private String salesman;
    //其他说明
    private String others;
    //电话回访状态 101：上门设计 102:有购买需求 103:未接听104:沟通后无意向 105:未报名 106:其他需求
    private String visitStatus;
    //下次回访时间
    private Date visitTime;
    //备注
    private String remark;
    //线索状态 1.未处理 2.已匹配 3.已分配 4.已跟进 5已成交 6已取消
    private String status;
    //关键字
    private String keyWords;
    //是否进店
    private String isIntoStores;
    //预埋烟管
    private String buryPipe;
    //装修进度
    private String decorateProgres;
    //意向产品
    private String intentionProduct;
    //跟进状态
    private String followUpStatus;
    //信息完整度得分
    private String totalScore;
    //线索分类
    private String cluesType;
    //线索来源
    private String cluesSource;
    //线索等级
    private String cluesLevel;
    //房屋类型house_model
    private String houseModel;
    //省
    private String provinceName;
    //市
    private String cityName;
    //区
    private String countyName;
    //小区id
    private Long villageId;
    //小区
    private String village;
    //省市区
    private String cpc;
    //详细地址
    @FieldEncrypt
    private String address;
    //楼栋
    private String building;
    //单元
    private String unit;
    //门牌号
    private String houseNumber;
    //户型
    private String houseType;
    //房屋面积
    private String houseArea;
    //厨房形状
    private String kitchenType;
    //厨房面积
    private String kitchenArea;
    //地址id
    private Long addressId;
    //orgid
    private Long companyId;
    //业务员名称
    @FieldEncrypt
    private String chargeUserName;
    //活动名称
    private String title;
    //数据来源
    private String utmSource;
    //审核状态
    private String auditStatus;
    //是否老用户
    private String regularSubscriber;
    //门店类型
    String storeTypeName;
    //回访状态
    String reviewStatus;
    //社区店
    String communityMemberJson;
    //家装
    String decorateDesignerJson;
    //成交产品
    String makeBargainProduct;
    //成交套系
    String makeBargainSystem;
    //交易状态
    String isMakeBargain;
    //交款状态
    String payStatus;
    //跟进数
    Long followUpCount;
    //电话跟进
    Long teleConnectionCount;
    //微信跟进
    Long weChatConnectionCount;
    //到访跟进
    Long visitConnectionCount;
    //进店跟进
    Long intoShopConnectionCount;
    @FieldEncrypt
    private String createUserName;

    private String lostOrderValue;

    private String lostOrderCOde;
    private Date reviseTime;
    private Date firstFollowTime;//首次跟进时间

    private String isHouserholdsVisit;

    private String followUpNoDate;

    //未跟进天数
    private String unFollowDate;

    @ApiModelProperty(value = "交房时间")
    private String makeRoomTime;
    @ApiModelProperty(value = "装修类型")
    private String decorateType;

    private String districtCode;
    private String districtValue;

    private String distributorName;//客户名称

    private String isFollowUp;//是否跟进
    private String isDeal;//是否成交
    private String isLostOrder;//是否丢单
    private Integer index;//序号

    private String fullPathName;//所属部门

    @ApiModelProperty(value = "门店简称", example = "门店简称")
    private String abbreviation;

    //负责人名称
    private String chargeCode;
    private String tag;//线索标签
    private String tagScore;//线索标签分数
    private String md5;//手机号码md5加密

    @ApiModelProperty(value = "预算")
    private String budgetAccounting;
    private Date lastFollowTime;//最后跟进时间
    private Date auditTime;//审核通过时间
    //14635  线索列表-列表,筛选,导出增加字段
    /**
     * 是否上门设计1是0否
     */
    private String isComeDevise;
    /**
     * 上门设计状态,1:带上门，2：已测量，3：已设计
     */
    private Integer comeDeviseStatus;
    private Date modifiedChargeUserDate;//修改业务员时间

    //带单老用户id
    private Long withSingleOldUserId;

    //带单老用户名称
    @FieldEncrypt
    private String withSingleOldUserName;

    //是否特惠
    private Integer whetherPreference;

    private String preferenceStatusName;

    //特惠备注
    private String preferenceNote;

    //带单老用户code
    private String withSingleOldUserCode;

    //二级丢单原因
    private String  lostOrderSubValue;
    //二级丢单code
    private String  lostOrderSubCode;

    private String followEfficiencyCategoryValue;
    private Integer followEfficiencyTime;

    //5.30 新增字段
    private String assistUserId;
    private String assistUserCode;
    @FieldEncrypt
    private String assistUserName;
    private String flagInstallation;
    private String flagMoveIn;

    private String urgencyFollowUp;
    private String urgencyFollowUpValue;

    private String urgencyPurchase;
    private String urgencyPurchaseValue;

    private Date installDate;
    private String installDateValue;

    private Date moveinDate;
    private String moveinDateValue;

    //进店方式
    private String inStoreType;
    private String inStoreTypeValue;
    //驻点时间
    private String  inStoreTime;
    private String inStoreTimeValue;

    //介绍人姓名
    @FieldEncrypt
    private String introducerName;
    //介绍人联系方式
    @FieldEncrypt
    private String  introducerPhone;
    private boolean villageFalg;

    //留资方式
    private String retentionMethod;
    private String retentionMethodValue;
    private Date dealOrderTime;
    private Long makeBargainCycle;
    private String artificialFollowUpFlag = "否";
    private Integer artificialFollowUpCount = 0 ;

    private String selfLackReviewStatusCode;
    private String selfLackReviewStatusValue;

    //是否成交定金订单
    private String orderEarnestFlagName;

    private String decorateAndDesignerTypeName;
    private Integer decorateAndDesignerType;
    private String decorateAndDesignerCode;
    private String decorateAndDesignerName;
    private String decorateAndDesignerPhone;

    /**
     * 异业三工分类
     */
    private String decorateCategory;

    private String qywxRelationFlag = "否";

    private String unionId;

    private String wxName;
    /**
     * 预约上门时间
     */
    private String appointmentTime;
    private Integer frequencyOpenSeas;
    private String openSeaflag;

    /**
     * 客情带单类型 1.门店员工亲戚朋友
     * 2.老板亲戚朋友
     * 3.办事处员工亲戚朋友
     * 4.总部优惠券（仅慈溪、宁波）
     */
    private Integer customerBringType;

    /**
     * 客情带单类型 1.门店员工亲戚朋友
     * 2.老板亲戚朋友
     * 3.办事处员工亲戚朋友
     * 4.总部优惠券（仅慈溪、宁波）
     */
    private String customerBringTypeValue;
    /**
     * 券码 code
     */
    private String cardNo;
    /**
     * 是否存在券码
     */
    private Integer couponFlag;
    /**
     * 营销活动整合 id
     */
    private String integrateCode;
    /**
     * 营销活动整合 标题
     */
    private String integrateTitle;
    /**
     * 前序业务员
     */
    @FieldEncrypt
    private String frontSalesman;

    private String templateCode;
    /**
     * 工单ID
     */
    private String csmOrderCode;
    /**
     * 技师编码
     */
    private String serviceEngineerCsn;

    /**
     * 新增留资方式:1 顾客主动留资,2 导购手动录入
     */
    private String creatCluesMethod;

    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    private Integer kitchenDesignFlag;
    /**
     * 是否含换装报告（1.是 0.否
     */
    private Integer costumeChangeReportFlag;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    private Integer freehandSketchingFlag;
    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    private String kitchenDesignFlagName;
    /**
     * 是否含换装报告（1.是 0.否
     */
    private String costumeChangeReportFlagName;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    private String freehandSketchingFlagName;


    private Integer isSceneInteraction;

    private String isSceneInteractionValue;

    private String storeCategoryName;

    private String designerClubMemberGrade;
    private String  designerIndustryLevel;
    private String  designerIndustryLevelName;
    private String designerClubMemberGradeName;
    /**
     * 线索来源  来源平台 1-APP 2-合作社 3-云管理小程序 4-方太官方小程序 5-方太5S小程序 7-欢迎语
     */
    private Integer createSourcePlatform;
    private String createSourcePlatformValue;
    private Long designerClubMemberId;

    /**
     * 抖音内容创建者
     */
    private String dyAdvertiseNickName;

    /**
     * 抖音线索id
     */
    private String dyCluesIds;

    /**
     * 抖音订单id
     */
    private String dyOrderIds;

    /**
     * 首次变更场景化为是的时间
     */
    private Date isSceneInteractionTime;

    /**
     * 是否上门设计时间，否变更为是的时间
     */
    private Date isComeDeviseTime;
    private String storeSubChannelName;
    private String storeSubChannelCode;

    private String storeChannelCode;

    private String houseRenovationType;

    private String developSalesmanCode;
    @FieldEncrypt
    private String developSalesmanName;


    private String localLifestyleChannel;

}
