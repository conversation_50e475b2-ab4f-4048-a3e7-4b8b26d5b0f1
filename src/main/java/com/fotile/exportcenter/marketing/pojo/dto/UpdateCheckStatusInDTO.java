package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ Author     ：黄学后
 * @ Date       ：Created in 11:51 2020/3/6
 * @ Description：
 * @ Modified By：
 * <AUTHOR>
 * @Version: $
 */
@Data
public class UpdateCheckStatusInDTO implements Serializable {

    @NotNull(message = "工单id不能为空")
    @ApiModelProperty("工单id")
    private Long orderId;

    @NotNull(message = "检测id不能为空")
    @ApiModelProperty("检测id")
    private Long examinationId;

    @ApiModelProperty(value = "类型 1：厨房健康检测，2：烟机清洗检测 3：新版厨房检测报告")
    private Integer type;

    private Long orderStatus;
}
