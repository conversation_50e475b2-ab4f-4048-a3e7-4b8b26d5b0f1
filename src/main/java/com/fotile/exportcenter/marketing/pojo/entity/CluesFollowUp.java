package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "clues_follow_up", schema = "marketingcenter")
public class CluesFollowUp extends AuditingEntity {
    @TableField(value = "source_id")
    @ApiModelProperty(value = "跟进来源ID")
    private String sourceId;

    @TableField(value = "external_id")
    @ApiModelProperty(value = "跟进来源ID")
    private String externalId;

    @TableField(value = "type")
    @ApiModelProperty(value = "跟进类型 1.电话，2.微信，3.到访")
    private String type;
    @TableField(value = "title")
    @ApiModelProperty(value = "跟进标题")
    private String title;

    @TableField(value = "content_text")
    @ApiModelProperty(value = "跟进内容")
    private String contentText;

    @TableField(value = "decorate_progres")
    @ApiModelProperty(value = "装修进度")
    private String decorateProgres;

    @TableField(value = "service_action")
    @ApiModelProperty(value = "服务动作")
    private String serviceAction;

    @TableField(value = "follow_date")
    @ApiModelProperty(value = "跟进时间")
    private Date followDate;

    @TableField(value = "address")
    @ApiModelProperty(value = "地址")
    @FieldEncrypt
    private String address;
    @TableField(value = "charge_user_id")
    @ApiModelProperty(value = "业务员ID")
    private String chargeUserId;
    @TableField(value = "charge_user_name")
    @ApiModelProperty(value = "业务员姓名")
    @FieldEncrypt
    private String chargeUserName;
    //新增字段
    @ApiModelProperty("家居服务类")
    @TableField(value = "home_service")
    private String homeService;
    @ApiModelProperty("基础装修类")
    @TableField(value = "base_decoration")
    private String baseDecoration;
    @ApiModelProperty("非方太家电服务类")
    @TableField(value = "no_appliance_service")
    private String noApplianceService;
    @ApiModelProperty("木质服务类")
    @TableField(value = "wood_service")
    private String woodService;
    @ApiModelProperty("潜客服务")
    @TableField(value = "latent_customer_service")
    private String latentCustomerService;
    @ApiModelProperty("成交服务")
    @TableField(value = "deal_service")
    private String dealService;
    @ApiModelProperty("用户服务")
    @TableField(value = "customer_service")
    private String customerService;
    @ApiModelProperty("干法描述")
    @TableField(value = "method_desc")
    private String methodDesc;
    @ApiModelProperty("干法标志")
    @TableField(value = "method_flag")
    private Long methodFlag;
    @ApiModelProperty("线索关联id")
    @TableField(value = "clue_related_id")
    private Long clueRelatedId;
    @ApiModelProperty("审核状态 10未审核 20 审核通过。30审核失败")
    @TableField(value = "audit_status")
    private Integer auditStatus;
    @ApiModelProperty("审核时间")
    @TableField(value = "audit_date")
    private Date auditDate;
    @ApiModelProperty("审核原因")
    @TableField(value = "audit_note")
    private String auditNote;
    @ApiModelProperty("得分")
    @TableField(value = "score")
    private Integer score;
    @ApiModelProperty(" 驻店时长")
    @TableField(value = "length_of_stay")
    private Integer lengthOfStay;

}
