package com.fotile.exportcenter.marketing.pojo.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;

/**
 * 频道实体类----一个渠道对应多个频道
 *
 * <AUTHOR>
 */
@Data
@Entity
@ApiModel(value = "频道", description = "频道")
@TableName(value = "t_radio")
public class RadioEntity extends AuditingEntity {

    @TableField(value = "channelId")
    @ApiModelProperty(value = "渠道id，一个频道只能对应一个渠道", example = "渠道id，一个频道只能对应一个渠道")
    private Long channelId;

    @NotBlank(message = "频道编码不能为空")
    @TableField(value = "code")
    @ApiModelProperty(value = "频道编码", example = "频道编码")
    private String code;

    @NotBlank(message = "频道名称不能为空")
    @TableField(value = "name")
    @ApiModelProperty(value = "频道名称", example = "频道名称")
    private String name;

    @TableField(value = "note")
    @ApiModelProperty(value = "备注", example = "备注")
    private String note;

    @TableField(value = "status")
    @ApiModelProperty(value = "状态,0:禁用；1：启用", example = "状态,0:禁用；1：启用")
    private byte status = 1;

    @Transient
    @ApiModelProperty(value = "状态值", example = "状态值:禁用;启用")
    private String statusName;
}
