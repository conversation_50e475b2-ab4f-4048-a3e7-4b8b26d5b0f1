package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

@Data
@TableName(value = "picture_marketing",schema = "marketingcenter")
public class PictureMarketing extends AuditingEntity {
    /**
     * 图片名称
     */
    @TableField(value="`name`")
    private String name;

    /**
     * 图片url
     */
    @TableField(value="cover_url")
    private String coverUrl;
}
