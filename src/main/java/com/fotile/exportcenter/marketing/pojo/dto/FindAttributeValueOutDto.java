package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "根据orgId、companyId，page_type根据排序查询字段英名",description = "根据orgId、companyId，page_type根据排序查询字段英名")
public class FindAttributeValueOutDto implements Serializable {
	
	/**
     * 组织机构id",example="组织机构id
     */
	private Long orgId;
	
	/**
     * 公司id",example="公司id
     */
	private Long companyId;
	
	/**
     * 属性英名
     */
	private String fieldId;
	
	/**
     * 属性值ID
     */
	private String attributeId;

	/**
     * 属性值名称
     */
	private String attributeValue;
	
	/**
     * 排序
     */
	private Long attributeSort;

	private String newAttributeId;

	private String newAttributeValue;

	private List<FindAttributeValueOutDto> children;


}