package com.fotile.exportcenter.marketing.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class UserCluesAssistMapping implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 主键
     */
	private Long id;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Long isDeleted;
	
	/**
     * 
     */
	private String createdBy;
	/**
	 *
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createdDate;
	
	/**
     * 
     */
	private String modifiedBy;
	/**
	 *
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date modifiedDate;
	
	/**
     * 线索id
     */
	private Long cluesId;
	
	/**
     * 负责人ID
     */
	private Long assistUserId;
	
	/**
     * 负责人名字
     */
	private String assistUserName;
	
	/**
     * 负责人编码
     */
	private String assistUserCode;
	
	/**
     * 负责人手机
     */
	private String assistUserPhone;
	
	/**
     * 数据来源类型：1：选择，2：报备
     */
	private Integer sourceType;

}