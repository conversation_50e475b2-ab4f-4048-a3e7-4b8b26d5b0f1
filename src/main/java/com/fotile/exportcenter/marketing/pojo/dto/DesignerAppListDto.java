package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ Author     ：黄学后
 * @ Date       ：Created in 16:11 2020/5/18
 * @ Description：
 * @ Modified By：
 * @Version: $
 */
@Data
public class DesignerAppListDto implements Serializable {
    @ApiModelProperty(value = "设计师id")
    private Long designerId;

    @ApiModelProperty(value = "家装公司编码")
    private String decorateCompanyCode;

    @ApiModelProperty(value = "家装公司名称")
    private String decorateCompanyName;

    @ApiModelProperty(value = "家装公司手机号")
    private String decorateCompanyPhone;

    @ApiModelProperty(value = "设计师编码")
    private String designerCode;

    @ApiModelProperty(value = "设计师名称")
    private String designerName;

    @ApiModelProperty(value = "设计师手机号")
    private String designerPhone;

    @ApiModelProperty(value = "类型 1：家装  2：异业三工 3 设计师")
    private Long decorateCompanyType;

    @ApiModelProperty(value = "家装公司编码")
    private String  decorateCode;

    private String storeName;
    private Long storeId;

    private String decorateCategory;

    @ApiModelProperty(value = "线索id")
    private Long cluesId;

}
