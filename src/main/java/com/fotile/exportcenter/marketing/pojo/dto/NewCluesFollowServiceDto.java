package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class NewCluesFollowServiceDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"})// 0)
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"})// 1)
    @ApiModelProperty("线索ID")
    private Long id;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"})// 2)
    @ApiModelProperty("顾客姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"})// 3)
    @ApiModelProperty("大区名称")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"})// 4)
    @ApiModelProperty("分公司名称")
    private String companyName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店渠道"})// 5)
    @ApiModelProperty("门店渠道")
    private String storeTypeName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"客户名称"})// 5)
    @ApiModelProperty("客户名称")
    private String distributorName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"客户渠道大类"})// 5)
    @ApiModelProperty("客户渠道大类")
    private String channelCategoryName;


    @ColumnWidth(10)
    @ExcelProperty(value = {"门店编码"})// 6)
    @ApiModelProperty("门店编码")
    private String stroeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店名称"})// 7)
    @ApiModelProperty("门店名称")
    private String stroeName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员编码"})// 8)
    @ApiModelProperty("业务员编码")
    private String chargeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员"})// 9)
    @ApiModelProperty("业务员")
    private String salesman;

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进方式"})// 10)
    @ApiModelProperty(value = "跟进类型 1.电话，2.微信，3.到访")
    private String type;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否上传手绘图"})
    @ApiModelProperty("是否上传手绘图")
    private String freehandSketchingFlagName;
    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否测量完整尺寸"})
    @ApiModelProperty("是否测量完整尺寸")
    private String kitchenDesignFlagName;
    /**
     * 是否含换装报告（1.是 0.否
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否含换装报告"})
    @ApiModelProperty("是否含换装报告")
    private String costumeChangeReportFlagName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进动作"})// 11)
    @ApiModelProperty(value = "服务动作")
    private String serviceAction;

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进内容"})// 12)
    @ApiModelProperty(value = "跟进内容")
    private String contentText;

    @ColumnWidth(10)
    @ExcelProperty(value = {"操作类型"})// 13)
    @ApiModelProperty(value = "操作类型")
    private String sourceType;

    @ColumnWidth(10)
    @ExcelProperty(value = {"操作人"})// 14)
    @ApiModelProperty(value = "业务员姓名")
    private String chargeUserName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"操作时间"})// 15)
    @ApiModelProperty(value = "跟进时间")
    private Date followDate;

    @ColumnWidth(10)
    @ExcelProperty(value = {"定位"})// 16)
    @ApiModelProperty(value = "地址")
    private String address;

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进评分"})// 17)
    @ApiModelProperty("得分")
    private Integer score;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件1"})// 18)
    private String coverUrl1;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件2"})// 19)
    private String coverUrl2;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件3"})// 20)
    private String coverUrl3;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件4"})// 21)
    private String coverUrl4;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件5"})// 22)
    private String coverUrl5;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件6"})// 23)
    private String coverUrl6;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件7"})// 24)
    private String coverUrl7;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件8"})// 25)
    private String coverUrl8;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件9"})// 26)
    private String coverUrl9;

    @ColumnWidth(10)
    @ExcelProperty(value = {"上门设计详情附件"})// 36)
    @ApiModelProperty("上门设计详情附件")
    private String comeUrls;
    

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进次数"})// 27)
    @ApiModelProperty("跟进次数")
    private Integer followUpCount;

    @ColumnWidth(10)
    @ExcelProperty(value = {"计划下次跟进时间"})// 28)
    @ApiModelProperty("下次跟进时间")
    private Date nextFollowUpTime;

    @ColumnWidth(10)
    @ExcelProperty(value = {"是否有上门设计报告"})// 29)
      private String isComeDeviseReport;

    @ColumnWidth(10)
    @ExcelProperty(value = {"质量评价结果"})// 30)
    private String evaluateStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = {"评价描述内容"})// 30)
    private String evaluateResultDesc;
    @ColumnWidth(10)
    @ExcelProperty(value = {"质量评价人"})// 31)
    private String evaluateUserName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"评价时间"})// 32)
    private Date evaluateTime;

}
