package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 值集基表 BaseAttribute 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-18 15:33:46
 */
 @ApiModel(value="BaseAttribute", description="值集基表")
 @Data
 @TableName(value = "`marketingcenter`.`t_base_attribute`")
 public class BaseAttribute implements Serializable {

	private static final long serialVersionUID = 1L;
	
		
	/** 主键 **/
	@ApiModelProperty(value = "主键")
	private Long id;
		
	/** 字段英名 **/
	@ApiModelProperty(value = "字段英名")
	private String fieldId;
		
	/** 属性值ID **/
	@ApiModelProperty(value = "属性值ID")
	private String attributeId;
		
	/** 属性值名称 **/
	@ApiModelProperty(value = "属性值名称")
	private String attributeValue;
		
	/** 排序 **/
	@ApiModelProperty(value = "排序")
	private Long attributeSort;
		
	/** 是否删除 0：否；其他：是 **/
	@ApiModelProperty(value = "是否删除 0：否；其他：是")
	private Long isDeleted;
		
	/** 创建者 **/
	@ApiModelProperty(value = "创建者")
	private String createdBy;
		
	/** 创建时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "创建时间")
	private Date createdDate;
		
	/** 修改者 **/
	@ApiModelProperty(value = "修改者")
	private String modifiedBy;
		
	/** 修改时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "修改时间")
	private Date modifiedDate;
		

	
}