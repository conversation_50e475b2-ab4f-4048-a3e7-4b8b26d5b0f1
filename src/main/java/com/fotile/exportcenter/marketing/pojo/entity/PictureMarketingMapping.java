package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;

@Data
@Table(schema = "marketingcenter",name = "picture_marketing_mapping")
public class PictureMarketingMapping extends AuditingEntity {
    @TableField(value="source_table_name")
    @ApiModelProperty(value="源表名")
    private String sourceTableName;
    @TableField(value="source_id")
    @ApiModelProperty(value="源ID")
    private Long sourceId;
    @TableField(value="picture_marketing_id")
    @ApiModelProperty(value="图片ID")
    private Long pictureMarketingId;
    @TableField(value="sort")
    @ApiModelProperty(value="排序")
    private Long sort;
}
