package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class CluesDetailSensitiveExportOutDtos implements Serializable {
    //正则表达式表示 ，


    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"})
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"})
    @ApiModelProperty("线索ID")
    private Long id;

    @ColumnWidth(15)
    @ExcelProperty(value = {"来源渠道"})
    @ApiModelProperty("来源渠道")
    private String channelName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"来源频道"})
    @ApiModelProperty("来源频道")
    private String radioName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"留资内容获取平台"})
    @ApiModelProperty("留资内容获取平台")
    private String createSourcePlatformValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"活动ID"})
    @ApiModelProperty("活动ID")
    private String activityId1;

    @ColumnWidth(15)
    @ExcelProperty(value = {"留资方式"})
    @ApiModelProperty("留资方式")
    private String creatCluesMethod;

    @ColumnWidth(15)
    @ExcelProperty(value = {"活动类型"})
    @ApiModelProperty("活动类型")
    private String activityType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"活动名称"})
    @ApiModelProperty("活动名称")
    private String activityName;


    @ColumnWidth(15)
    @ExcelProperty(value = {"整合营销ID"})
    @ApiModelProperty("整合营销ID")
    private String integrateCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"页面模板ID"})
    @ApiModelProperty("页面模板ID")
    private String templateCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"整合营销标题"})
    @ApiModelProperty("整合营销标题")
    private String integrateTitle;
    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"})
    @ApiModelProperty("姓名")

    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"性别"})
    @ApiModelProperty("性别")
    private String gender;

    @ColumnWidth(10)
    @ExcelProperty(value = {"手机号"})
    @ApiModelProperty("手机号")
    private String customerPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = {"微信号"})
    @ApiModelProperty("微信号")
    private String wechatno;

    @ColumnWidth(15)
    @ExcelProperty(value = {"提交时间"})
    @ApiModelProperty("提交时间")
    private Date fundingTime;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty("大区")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty("分公司")
    private String companyName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店业务发展主管编码"})
    @ApiModelProperty("门店业务发展主管编码")
    private String developSalesmanCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店业务发展主管姓名"})
    @ApiModelProperty("门店业务发展主管姓名")
    private String developSalesmanName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"客户名称"})
    @ApiModelProperty("客户名称")
    private String distributorName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})
    @ApiModelProperty("门店编码")
    private String stroeCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店全称"})
    @ApiModelProperty("门店全称")
    private String stroeName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店简称"})
    @ApiModelProperty("门店简称")
    private String abbreviation;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty("门店渠道")
    private String storeTypeName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty("门店渠道细分")
    private String storeSubChannelName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店类别"})
    @ApiModelProperty("门店类别")
    private String storeCategoryName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"门店所属部门"})
    @ApiModelProperty("门店所属部门")
    private String fullPathName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"线索创建人"})
    @ApiModelProperty("线索创建人")
    private String createUserName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员编码"})
    @ApiModelProperty("业务员编码")
    private String chargeCode;

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员名称"})
    @ApiModelProperty("业务员名称")
    private String salesman;

    /**
     * 5.30 线索协助人编码(业务员)
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"线索协助人编码"})
    @ApiModelProperty("线索协助人编码")
    private String assistUserCode;

    /**
     * 5.30 线索协助人名称(业务员)
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"线索协助人名称"})
    @ApiModelProperty("线索协助人名称")
    private String assistUserName;

    @ColumnWidth(25)
    @ExcelProperty(value = {"分配业务员时间"})
    @ApiModelProperty("分配业务员时间")
    private Date modifiedChargeUserDate;

    @ColumnWidth(15)
    @ExcelProperty(value = {"其他说明"})
    @ApiModelProperty("其他说明")
    private String others;

    @ColumnWidth(20)
    @ExcelProperty(value = {"电话回访状态"})
    @ApiModelProperty("电话回访状态")
    private String visitStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = {"下次回访时间"})
    @ApiModelProperty("下次回访时间")
    private Date visitTime;

    @ColumnWidth(20)
    @ExcelProperty(value = {"最近更新时间"})
    @ApiModelProperty("最近更新时间")
    private Date reviseTime;

    @ColumnWidth(20)
    @ExcelProperty(value = {"首次跟进时间"})
    @ApiModelProperty("首次跟进时间")
    private Date firstFollowTime;

    @ColumnWidth(20)
    @ExcelProperty(value = {"最近跟进时间"})
    @ApiModelProperty("最近跟进时间")
    private Date lastFollowTime;

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否为公海线索"})
    @ApiModelProperty("是否为公海线索")
    private String openSeaflag;
    @ColumnWidth(20)
    @ExcelProperty(value = {"丢入公海次数"})
    @ApiModelProperty("丢入公海次数")
    private Integer frequencyOpenSeas;
    @ColumnWidth(20)
    @ExcelProperty(value = {"前序负责人"})
    @ApiModelProperty("前序负责人")
    private String frontSalesman;

    @ColumnWidth(20)
    @ExcelProperty(value = {"审核通过时间"})
    @ApiModelProperty("审核通过时间")
    private Date auditTime;

    @ColumnWidth(15)
    @ExcelProperty(value = {"跟进时效"})
    @ApiModelProperty("跟进时效")
    private String followEfficiencyCategoryValue;

    @ColumnWidth(20)
    @ExcelProperty(value = {"写跟进条数"})
    @ApiModelProperty("写跟进条数")
    private String followUpCount;

    @ColumnWidth(20)
    @ExcelProperty(value = {"人工写跟进条数"})
    @ApiModelProperty("人工写跟进条数")
    private Integer artificialFollowUpCount = 0 ;

    @ColumnWidth(20)
    @ExcelProperty(value = {"创建后是否跟进"})
    @ApiModelProperty("创建后是否跟进")
    private String artificialFollowUpFlag = "否";

    @ColumnWidth(15)
    @ExcelProperty(value = {"备注描述"})
    @ApiModelProperty("备注描述")
    private String remark;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否上传手绘图"})
    @ApiModelProperty("是否上传手绘图")
    private String freehandSketchingFlagName;
    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否测量完整尺寸"})
    @ApiModelProperty("是否测量完整尺寸")
    private String kitchenDesignFlagName;
    /**
     * 是否含换装报告（1.是 0.否
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否含换装报告"})
    @ApiModelProperty("是否含换装报告")
    private String costumeChangeReportFlagName;


    @ColumnWidth(15)
    @ExcelProperty(value = {"线索状态"})
    @ApiModelProperty("线索状态")
    private String status;

    @ColumnWidth(10)
    @ExcelProperty(value = {"关键字"})
    @ApiModelProperty("关键字")
    private String keyWords;

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否老用户"})
    @ApiModelProperty("是否老用户")
    private String regularSubscriber;

    @ColumnWidth(20)
    @ExcelProperty(value = {"带单老用户编码"})
    @ApiModelProperty("带单老用户编码")
    private String withSingleOldUserCode;

    @ColumnWidth(20)
    @ExcelProperty(value = {"带单老用户名称"})
    @ApiModelProperty("带单老用户名称")
    private String withSingleOldUserName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"预埋烟管"})
    @ApiModelProperty("预埋烟管")
    private String buryPipe;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否跟进"})
    @ApiModelProperty("是否跟进")
    private String isFollowUp;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否进店"})
    @ApiModelProperty("是否进店")
    private String isIntoStores;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否成交"})
    @ApiModelProperty("是否成交")
    private String isDeal;

    @ColumnWidth(15)
    @ExcelProperty(value = {"工单ID"})
    @ApiModelProperty("工单ID")
    private String csmOrderCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"技师编码"})
    @ApiModelProperty("技师编码")
    private String serviceEngineerCsn;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否丢单"})
    @ApiModelProperty("是否丢单")
    private String isLostOrder;


    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-顾客反馈概述"})
    @ApiModelProperty("丢单原因-顾客反馈概述")
    private String lostOrderValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-顾客反馈明细"})
    @ApiModelProperty("丢单原因-顾客反馈明细")
    private String lostOrderSubValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-自我不足"})
    @ApiModelProperty("丢单原因-自我不足")
    private String selfLackReviewStatusValue;

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否上门回访"})
    @ApiModelProperty("是否入户回访")
    private String isHouserholdsVisit;

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否上门设计"})
    @ApiModelProperty("是否上门设计")
    private String isComeDevise;


    @ColumnWidth(20)
    @ExcelProperty(value = {"首次上门设计时间"})
    @ApiModelProperty("首次上门设计时间")
    private Date isComeDeviseTime;


    @ColumnWidth(15)
    @ExcelProperty(value = {"预约上门时间"})
    @ApiModelProperty(value = "预约上门时间")
    private String appointmentTime;
    /**
     * 线索列表线索详情表导出增加字段-是否场景化交互 1是 0否
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"是否场景化交互"})
    @ApiModelProperty("是否场景化交互")
    private String isSceneInteractionValue;

    @ColumnWidth(20)
    @ExcelProperty(value = {"首次场景化交互时间"})
    @ApiModelProperty("首次场景化交互时间")
    private Date isSceneInteractionTime;


    /**
     * 5.30 是否完成安装(需要数字转是否)
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"是否完成安装"})
    @ApiModelProperty("是否完成安装")
    private String flagInstallation;

    /**
     * 5.30 是否入住使用(需要数字转是否)
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"是否入住使用"})
    @ApiModelProperty("是否入住使用")
    private String flagMoveIn;


    @ColumnWidth(20)
    @ExcelProperty(value = {"是否有审核通过定金订单"})
    @ApiModelProperty("是否入住使用")
    private String orderEarnestFlagName;



    @ColumnWidth(15)
    @ExcelProperty(value = {"线索分类"})
    @ApiModelProperty("线索分类")
    private String cluesType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索来源"})
    @ApiModelProperty("线索来源")
    private String cluesSource;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索等级"})
    @ApiModelProperty("线索等级")
    private String cluesLevel;

    /**
     * 5.30 跟进紧急度 字典code()
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"跟进紧急度"})
    @ApiModelProperty("跟进紧急度")
    private String urgencyFollowUp;

    /**
     * 5.30 购买紧急度 字典code()
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"购买紧急度"})
    @ApiModelProperty("购买紧急度")
    private String urgencyPurchase;

    @ColumnWidth(15)
    @ExcelProperty(value = {"无效O2O线索标记"})
    @ApiModelProperty("无效O2O线索标记")
    private String reviewStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = {"当前跟进状态"})
    @ApiModelProperty("当前跟进状态")
    private String followUpStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = {"审核状态"})
    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = {"意向产品"})
    @ApiModelProperty("意向产品")
    private String intentionProduct;

    @ColumnWidth(15)
    @ExcelProperty(value = {"装修进度"})
    @ApiModelProperty("装修进度")
    private String decorateProgres;

    @ColumnWidth(25)
    @ExcelProperty(value = {"信息完整度得分"})
    @ApiModelProperty("信息完整度得分")
    private String totalScore;

//    @ColumnWidth(20)
//    @ExcelProperty(value = {"关联引流渠道"})
//    @ApiModelProperty("关联引流渠道")
//    private String decorateDesignerJson;

    @ColumnWidth(20)
    @ExcelProperty(value = {"关联引流渠道类型"})
    @ApiModelProperty("关联引流渠道类型")
    private String decorateAndDesignerTypeName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"关联引流渠道编码"})
    @ApiModelProperty("关联引流渠道编码")
    private String decorateAndDesignerCode;

    @ColumnWidth(20)
    @ExcelProperty(value = {"关联引流名称"})
    @ApiModelProperty("关联引流名称")
    private String decorateAndDesignerName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"关联引流渠道手机号"})
    @ApiModelProperty("关联引流渠道手机号")
    private String decorateAndDesignerPhone;
    @ColumnWidth(20)
    @ExcelProperty(value = {"是否中高端设计师"})
    @ApiModelProperty("是否中高端设计师")
    private String designerIndustryLevelName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"设计师俱乐部会员等级"})
    @ApiModelProperty("设计师俱乐部会员等级")
    private String designerClubMemberGradeName;


    @ColumnWidth(20)
    @ExcelProperty(value = {"设计师会员ID"})
    @ApiModelProperty("设计师会员ID")
    private Long designerClubMemberId;
    @ColumnWidth(20)
    @ExcelProperty(value = {"关联社区会员"})
    @ApiModelProperty("关联社区会员")
    private String communityMemberJson;

    @ColumnWidth(15)
    @ExcelProperty(value = {"交易状态"})
    @ApiModelProperty("交易状态")
    private String isMakeBargain;

    @ColumnWidth(15)
    @ExcelProperty(value = {"交款状态"})
    @ApiModelProperty("交款状态")
    private String payStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = {"成交商品"})
    @ApiModelProperty("成交商品")
    private String makeBargainProduct;

    @ColumnWidth(15)
    @ExcelProperty(value = {"成交套系"})
    @ApiModelProperty("成交套系")
    private String makeBargainSystem;

    @ColumnWidth(30)
    @ExcelProperty(value = {"是否“特惠券/售卡/客情”线索"})
    @ApiModelProperty("是否“特惠券/售卡/客情”线索")
    private String preferenceStatusName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"特惠备注"})
    @ApiModelProperty("特惠备注")
    private String preferenceNote;

    @ColumnWidth(15)
    @ExcelProperty(value = {"房屋特征"})
    @ApiModelProperty("房屋特征")
    private String decorateType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"交房时间"})
    @ApiModelProperty("交房时间")
    private String makeRoomTime;

    @ColumnWidth(15)
    @ExcelProperty(value = {"驻店时间"})
    @ApiModelProperty("驻点时间")
    private String inStoreTimeValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"进店方式"})
    @ApiModelProperty("进店方式")
    private String inStoreTypeValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"留资方式"})
    @ApiModelProperty("留资方式")
    private String retentionMethodValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"带单类型"})
    @ApiModelProperty("带单类型")
    private String   customerBringTypeValue;



    @ColumnWidth(15)
    @ExcelProperty(value = {"介绍人姓名"})
    @ApiModelProperty("介绍人姓名")
    private String introducerName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"介绍人联系方式"})
    @ApiModelProperty("介绍人联系方式")
    private String introducerPhone;

    @ColumnWidth(15)
    @ExcelProperty(value = {"优惠券码"})
    @ApiModelProperty("优惠券码")
    private String cardNo;

    /**
     * 0426迭代增加字段，调整以下顺序
     *
     *
     */

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否添加企微好友"})
    @ApiModelProperty("是否添加企微好友")
    private String qywxRelationFlag;



    @ColumnWidth(20)
    @ExcelProperty(value = {"未更新天数"})
    @ApiModelProperty("未更新天数")
    private String followUpNoDate;

    @ColumnWidth(20)
    @ExcelProperty(value = {"未跟进天数"})
    @ApiModelProperty("未跟进天数")
    private String unFollowDate;

    @ColumnWidth(30)
    @ExcelProperty(value = {"跟进方式：电话联系"})
    @ApiModelProperty("跟进方式：电话联系")
    private String teleConnectionCount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"跟进方式：微信联系"})
    @ApiModelProperty("跟进方式：微信联系")
    private String weChatConnectionCount;

    @ColumnWidth(35)
    @ExcelProperty(value = {"跟进方式：店外见面沟通"})
    @ApiModelProperty("跟进方式：店外见面沟通")
    private String visitConnectionCount;

    @ColumnWidth(35)
    @ExcelProperty(value = {"跟进方式：店内见面沟通"})
    @ApiModelProperty("跟进方式：店内见面沟通")
    private String intoShopConnectionCount;

    @ColumnWidth(20)
    @ExcelProperty(value = {"utm_source"})
    @ApiModelProperty("utm_source")
    private String utmSource;

    @ColumnWidth(15)
    @ExcelProperty(value = {"房屋类型"})
    @ApiModelProperty("房屋类型")
    private String houseType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"装修类型"})
    @ApiModelProperty("装修类型")
    private String houseRenovationType;

    @ColumnWidth(10)
    @ExcelProperty(value = {"小区"})
    @ApiModelProperty("小区")
    private String village;

    @ColumnWidth(10)
    @ExcelProperty(value = {"省市区"})
    @ApiModelProperty("省市区")
    private String cpc;
    @ColumnWidth(10)
    @ExcelProperty(value = {"楼栋"})
    @ApiModelProperty("楼栋")
    private String building;

    @ColumnWidth(10)
    @ExcelProperty(value = {"单元"})
    @ApiModelProperty("单元")
    private String unit;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门牌号"})
    @ApiModelProperty("门牌号")
    private String houseNumber;

    @ColumnWidth(10)
    @ExcelProperty(value = {"户型"})
    @ApiModelProperty("户型")
    private String houseModel;

    @ColumnWidth(15)
    @ExcelProperty(value = {"房屋面积"})
    @ApiModelProperty("房屋面积")
    private String houseArea;

    @ColumnWidth(15)
    @ExcelProperty(value = {"厨房形状"})
    @ApiModelProperty("厨房形状")
    private String kitchenType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"厨房面积"})
    @ApiModelProperty("厨房面积")
    private String kitchenArea;

    @ColumnWidth(10)
    @ExcelProperty(value = {"预算"})
    @ApiModelProperty(value = "预算")
    private String budgetAccounting;

    /**
     * 5.30 安装时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"安装时间"})
    @ApiModelProperty(value = "安装时间")
    private String installDateValue;

    /**
     * 5.30 入户时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"入住时间"})
    @ApiModelProperty(value = "入住时间")
    private String moveinDateValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"成交周期（小时）"})
    @ApiModelProperty(value = "成交周期")
    private Long makeBargainCycle;
    /**
     * 本地生活渠道
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"本地生活渠道"})
    @ApiModelProperty(value = "本地生活渠道")
    private String localLifestyleChannel;


    /**
     * 抖音内容创建者
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"平台内容创造者"})
    @ApiModelProperty(value = "平台内容创造者")
    private String dyAdvertiseNickName;

    /**
     * 抖音线索id
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"平台线索ID"})
    @ApiModelProperty(value = "抖音线索ID")
    private String dyCluesIds;

    /**
     * 抖音订单id
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"平台订单ID"})
    @ApiModelProperty(value = "抖音订单ID")
    private String dyOrderIds;

    @ColumnWidth(15)
    @ExcelProperty(value = {"unionid"})
    @ApiModelProperty("unionid")
    private String unionId;
    @ColumnWidth(15)
    @ExcelProperty(value = {"微信昵称"})
    @ApiModelProperty("微信昵称")
    private String wxName;
}
