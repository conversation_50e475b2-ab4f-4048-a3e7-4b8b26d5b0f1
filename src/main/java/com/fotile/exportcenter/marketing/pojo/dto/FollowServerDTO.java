package com.fotile.exportcenter.marketing.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
    * 值集层级维护表
    */
@ApiModel(value="值集层级维护表")
@Data
public class FollowServerDTO extends AuditingEntity {
    /**
     * 字段英名
     */
    @TableField(value = "`field_id`")
    @ApiModelProperty(value="字段英名")
    private String fieldId;

    /**
     * 属性值ID
     */
    @TableField(value = "`attribute_id`")
    @ApiModelProperty(value="属性值ID")
    private String attributeId;

    /**
     * 属性值名称
     */
    @TableField(value = "`attribute_value`")
    @ApiModelProperty(value="属性值名称")
    private String attributeValue;

    /**
     * 排序
     */
    @TableField(value = "`attribute_sort`")
    @ApiModelProperty(value="排序")
    private Long attributeSort;

    /**
     * 层级
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value="层级")
    private Integer level;

    /**
     * 父级属性id
     */
    @TableField(value = "`parent_attribute_id`")
    @ApiModelProperty(value="父级属性id")
    private String parentAttributeId;


    private String parentAttributeValue;

    /**
     * 所属大类值,目前只有服务动作有，成交前，成交后-安装前，安装后
     */
    @TableField(value = "`category_code`")
    @ApiModelProperty(value="所属大类值,目前只有服务动作有，成交前，成交后-安装前，安装后")
    private String categoryCode;


}