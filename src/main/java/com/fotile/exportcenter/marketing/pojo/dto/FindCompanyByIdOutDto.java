package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class FindCompanyByIdOutDto implements Serializable {

	@ApiModelProperty(value="公司id",example="公司id")
	private Long id;

	@ApiModelProperty(value="编码",example="编码")
	private String code;

	@ApiModelProperty(value="名称",example="名称")
	private String name;

	@ApiModelProperty(value="地址-省",example="地址-省id")
	private Long provicenId;

	@ApiModelProperty(value="地址-市",example="地址-市id")
	private Long cityId;

	@ApiModelProperty(value="地址-区",example="地址-区id")
	private Long countyId;

	@ApiModelProperty(value="详细地址",example="详细地址")
	private String address;

	@ApiModelProperty(value="总经理",example="总经理id")
	private Integer leaderId;

	@ApiModelProperty(value="备注",example="备注")
	private String note;

	@ApiModelProperty(value="部门列表中'上级部门'id",example="部门列表中'上级部门'id")
	private String fullPathId;

	@ApiModelProperty(value="部门列表中'上级部门'",example="部门列表中'上级部门'")
	private String fullPathName;

	@ApiModelProperty(value="是否删除",example="是否删除")
	private Integer isDeleted;

	@ApiModelProperty(value="组织机构id",example="组织机构id")
	private Long orgId;

	@ApiModelProperty(value="地址-省名称",example="地址-省名称")
	private String provicenName;

	@ApiModelProperty(value="地址-市名称",example="地址-市名称")
	private String cityName;

	@ApiModelProperty(value="地址-区名称",example="地址-区名称")
	private String countyName;

	@ApiModelProperty(value="经度",example="经度")
	private String longitude;

	@ApiModelProperty(value="纬度",example="纬度")
	private String latitude;

	@ApiModelProperty(value="留资通知邮箱",example="留资通知邮箱")
	private String email;

	@ApiModelProperty(value="所属大区;1:华东；2：华南；3：华西；4：华中；5：华北；6：中原",example="所属大区;1:华东；2：华南；3：华西；4：华中；5：华北；6：中原")
	private Integer area;

	@ApiModelProperty(value="所属部门id",example="所属部门id")
	private Long parentId;


}
