package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class CluesActivityExportOutDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"}, index = 0)
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"}, index = 1)
    @ApiModelProperty("线索ID")
    private Long id;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"}, index = 2)
    @ApiModelProperty("姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"手机"}, index = 3)
    @ApiModelProperty("手机号")
    private String customerPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"}, index = 4)
    @ApiModelProperty("大区")
    private String areaName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"}, index = 5)
    @ApiModelProperty("分公司")
    private String companyName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"客户名称"}, index = 6)
    @ApiModelProperty("客户名称")
    private String distributorName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"}, index = 7)
    @ApiModelProperty("门店编码")
    private String stroeCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店全称"}, index = 8)
    @ApiModelProperty("门店全称")
    private String stroeName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店简称"}, index = 9)
    @ApiModelProperty("门店简称")
    private String abbreviation;

    @ColumnWidth(20)
    @ExcelProperty(value = {"门店所属部门"}, index = 10)
    @ApiModelProperty("门店所属部门")
    private String fullPathName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"线索创建人"}, index = 11)
    @ApiModelProperty("线索创建人")
    private String createUserName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员编码"}, index = 12)
    @ApiModelProperty("业务员编码")
    private String chargeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员"}, index = 13)
    @ApiModelProperty("业务员")
    private String chargeUserName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"参与活动ID"}, index = 14)
    @ApiModelProperty("参与活动ID")
    private String activityId;

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否活动新增"}, index = 15)
    @ApiModelProperty("是否活动新增")
    private String recordType;


    @ColumnWidth(15)
    @ExcelProperty(value = {"活动名称"}, index = 16)
    @ApiModelProperty("活动名称")
    private String activityName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"报名时间"}, index = 17)
    @ApiModelProperty("报名时间")
    private String fundingTime;





}
