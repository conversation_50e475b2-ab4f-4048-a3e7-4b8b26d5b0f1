package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel(value = "活动图集关联表",description = "活动图集关联表")
@TableName(value="picture_marketing")
public class ActivityPictureMarketing extends AuditingEntity {


    /**
     * 图片名称
     */
    @TableField(value="picture_name")
    private String pictureName;


    /**
     * 图片url
     */
    @TableField(value="cover_url")
    private String coverUrl;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 修改人
     */
    private String modifiedBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改时间
     */
    private Date modifiedDate;

}
