package com.fotile.exportcenter.marketing.pojo.dto;


import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

@Data
public class CluesActivityOutDto {

    private Integer index;
    private Long id;
    @FieldEncrypt
    private String customerName;
    @FieldEncrypt
    private String customerPhone;
    //大区
    private String areaName;
    private Long companyId;
    private String companyName;
    //客户名称（经销商）
    private String distributorName;
    private Long storeId;
    private String stroeCode;
    private String stroeName;
    //门店简称
    private String abbreviation;
    //门店所属部门
    private String fullPathName;
    //线索创建人
    @FieldEncrypt
    private String createUserName;
    //业务员
    @FieldEncrypt
    private String chargeUserName;
    private String chargeCode;

    //活动
    private String activityId;
    private String activityName;
    private String fundingTime;

    private String recordType;


}
