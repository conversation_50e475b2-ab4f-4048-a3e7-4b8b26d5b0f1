package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class OperatorLogListOutDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"})//0)
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"})//1)
    @ApiModelProperty("线索ID")
    private String sourceId;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"})//2)
    @ApiModelProperty("顾客姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"手机号"})//3)
    @ApiModelProperty("顾客手机号")
    private String customerPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = {"微信号"})//4)
    @ApiModelProperty("微信号")
    private String wechatno;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"})//5)
    @ApiModelProperty("大区名称")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"})//6)
    @ApiModelProperty("分公司名称")
    private String companyName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"客户名称"})//7)
    @ApiModelProperty("经销商名称")
    private String distributorName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})//8)
    @ApiModelProperty("门店编码")
    private String stroeCode;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店全称"})//9)
    @ApiModelProperty("门店全称")
    private String storeName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店简称"})//10)
    @ApiModelProperty("门店简称")
    private String abbreviation;

    @ColumnWidth(20)
    @ExcelProperty(value = {"门店所属部门"})//11)
    @ApiModelProperty("门店名称")
    private String fullPathName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"线索创建人"})//12)
    @ApiModelProperty("线索创建人")
    private String createUserName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员编码"})//13)
    @ApiModelProperty("业务员编码")
    private String chargeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员"})//14)
    @ApiModelProperty("业务员")
    private String salesMan;

    @ColumnWidth(15)
    @ExcelProperty(value = {"楼盘类型"})//15)
    @ApiModelProperty("楼盘类型")
    private String houseType;

    @ColumnWidth(10)
    @ExcelProperty(value = {"小区"})//16)
    @ApiModelProperty("小区")
    private String village;

    @ColumnWidth(10)
    @ExcelProperty(value = {"操作人"})//17)
    @ApiModelProperty("操作人")
    private String chargeUserName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"操作类型"})//18)
    @ApiModelProperty("日志类型")
    private String type;


    @ColumnWidth(15)
    @ExcelProperty(value = {"操作时间"})//19)
    @ApiModelProperty("跟进时间")
    private Date followDate;

    @ColumnWidth(15)
    @ExcelProperty(value = {"描述内容"})//20)
    @ApiModelProperty("描述内容")
    private String description;

    @ColumnWidth(15)
    @ExcelProperty(value = {"跟进类型"})//21)
    @ApiModelProperty("跟进类型")
    private String appType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"驻店时长（分钟）"})//22)
    @ApiModelProperty("驻店时长（分钟）")
    private Integer lengthOfStay;

    @ColumnWidth(15)
    @ExcelProperty(value = {"服务动作"})//23)
    @ApiModelProperty("服务动作")
    private String serviceAction;

    @ColumnWidth(15)
    @ExcelProperty(value = {"装修进度"})//24)
    @ApiModelProperty("装修进度")
    private String decorateProgres;

    @ColumnWidth(15)
    @ExcelProperty(value = {"跟进内容"})//25)
    @ApiModelProperty("跟进内容")
    private String contentText;

    @ColumnWidth(10)
    @ExcelProperty(value = {"定位"})//26)
    @ApiModelProperty("地址")
    private String address;

    @ColumnWidth(10)
    @ExcelProperty(value = {"跟进评分"})//27)
    @ApiModelProperty("跟进评分")
    private Integer score;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件1"})//28)
    @ApiModelProperty("日志图片")
    private String coverUrl1;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件2"})//29)
    @ApiModelProperty("日志图片")
    private String coverUrl2;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件3"})//30)
    @ApiModelProperty("日志图片")
    private String coverUrl3;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件4"})//31)
    @ApiModelProperty("日志图片")
    private String coverUrl4;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件5"})//32)
    @ApiModelProperty("日志图片")
    private String coverUrl5;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件6"})//33)
    @ApiModelProperty("日志图片")
    private String coverUrl6;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件7"})//34)
    @ApiModelProperty("日志图片")
    private String coverUrl7;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件8"})//35)
    @ApiModelProperty("日志图片")
    private String coverUrl8;

    @ColumnWidth(10)
    @ExcelProperty(value = {"附件9"})//36)
    @ApiModelProperty("日志图片")
    private String coverUrl9;




    @ColumnWidth(10)
    @ExcelProperty(value = {"当前跟进状态"})//37)
    @ApiModelProperty("当前跟进状态")
    private String followUpStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = {"是否上门设计"})//38)
    @ApiModelProperty("是否上门设计")
    private String isComeDevise;

}
