package com.fotile.exportcenter.marketing.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fotile.exportcenter.marketing.pojo.bo.FollowEfficiencyBO;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 创建时间：2019年8月12日 下午3:30:30
 * @description
 */
@Data
public class QueryUserCluseInDto implements Serializable {
    //去除重复筛选条件，导出时不使用此字段
    private List<Long> companys;
    private List<String> channels;
    private List<String> stroeLabelOrgIds;
    private List<String> distributorStoreIds;//客户所属门店权限
    /**
     * 门店状态
     */
    private List<Integer> storeStatus;

/**++++++++++++++++++++++++++++++++++++++++++++++*/

    /**
     * 导出文件名称
     */
    private String fileName;
    /**
     * 分页
     */
    private Integer page;
    /**
     * 数量
     */
    private Integer size;
    /**
     * id
     */
    private Long id;
    /**
     * id集合
     */
    private List<Long> ids;
    /**
     * 是否脱敏操作 0否1是
     */
    private String flag;

    private List<Long> companyIds;
    private List<String> storeIds;

    //列表顶部查询条件channelSubdivideCodeList
    /**
     * 顾客名称
     */
    private String customerName;
    /**
     * 顾客手机号
     */
    private String customerPhone;
    /**
     * 业务员id
     */
    private Long salesmanId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 公司ID集合
     */
    private List<Long> companyId;
    /**
     * 公司orgid
     */
    private Long companyOrgId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
//    private String state;
    //前台传入分页参数
    private Integer num;
    /**
     * 开始条数
     */
    private Integer start;
    /**
     * 结束条数
     */
    private Integer end;
//    private Long activityId;
    /**
     * 类型
     */
    private String type;
    /**
     *withinType
     */
    private Integer withinType;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 渠道id集合
     */
    private List<String> channelId;
    /**
     * 客服
     */
    private String customServiceCode;
    /**
     * 门店id 空：-1
     */
    private String stroeId;
    /**
     * 大区集合
     */
    private List<Long> area;
    /**
     * 排序(下次回访时间) asc按照回访时间正序;desc按照回访时间倒序
     */
    private String visitSort;
    /**
     * 下次回訪時間開始
     */
    private String visitStartTime;
    /**
     * 下次回訪時間結束
     */
    private String visitEndTime;
    //跟進狀態 1.未处理 2.已匹配 3.已分配 4.已跟进 5已成交 6已取消
//    private String followUpStatus;
    /**
     * 数据来源
     */
    private String utmSource;
    /**
     * 频道编码集合
     */
    private List<String> radioCodeIds;
    /**
     * 审核状态
     */
    private String auditStatus;

    private UserAuthor userAuthor;
    /**
     * 留资距今时间开始
     */
    private Date durationStartTime;
    /**
     *留资距今时间结束
     */
    private Date durationEndTime;
    /**
     * dueTime
     */
    private Date dueTime;
    /**
     * 省code
     */
    private Long provinceCode;
    /**
     * 市code
     */
    private Long cityCode;
    /**
     * 区Code
     */
    private Long areaCode;
    /**
     *门店类型编码
     */
    private String storeTypeCode;
    /**
     * 门店类型编码集合
     */
    private List<String> storeTypeCodeList;
    /**
     * 线索ids
     */
    private List<Long> cluesIds;
//    private String cluesType;

    /**
     * 最近更新間開始
     */
    private Date reviseTimeStart;
    /**
     * 最近更新時間結束
     */
    private Date reviseTimeEnd;

    /**
     * 所属门店对应的客户id
     */
    private Long distributorId;

    /**
     * 状态: 1.未处理 2.已处理 3.已成单
     */
    private List<String> state;
    /**
     * 线索类型
     */
    private List<String> cluesType;
    /**
     * 跟進狀態 跟进状态 1.未跟进 2已跟进 3 已进店 4 已成交
     */
    private List<String> followUpStatus;
    /**
     * 活动id集合
     */
    private List<Long> activityId;
    /**
     *是否进店
     */
    private Long isIntoStores;
    /**
     *是否丢单
     */
    private Long isLostOrder;
    /**
     *订单状态
     */
    private Long orderStatus;
    /**
     *是否跟进
     */
    private Long isFollowUp;
    /**
     * 线索来源id集合
     */
    private List<String> cluesSource;
    /**
     *门店标签集合
     */
    private String stroeLabels;
    /**
     *渠道细分编码
     */
    private List<String> channelSubdivideCodeList;
    /**
     *渠道分类编码
     */
    private List<String> channelCategoryCodeList;
    /**
     *门店业务发展主管id
     */
    private Long developSalesmanId;
    /**
     * 跟进时间开始
     */
    private Date followUpTimeStart;
    /**
     *跟进时间结束
     */
    private Date followUpTimeEnd;
    /**
     *审核时间开始
     */
    private Date auditTimeStart;
    /**
     * 审核时间结束
     */
    private Date auditTimeEnd;

    //14635  线索列表-列表,筛选,导出增加字段
    /**
     * 是否上门设计1是0否
     */
    private String isComeDevise;
    /**
     * 上门设计状态,1:带上门，2：已测量，3：已设计
     */
    private List<Integer> comeDeviseStatusIds;
    /**
     * 门店类别
     */
    private Set<String> storeLevels;
    /**
     *门店类别orgID集合
     */
    private List<String> stroeLevelsOrgIds;


    /**
     * 引流渠道code
     */
    private String drainageChannel;
    /**
     *引流渠道类型
     */
    private List<Integer> decorateCompanyTypeList;
    /**
     * 引流渠道code
     */
    private List<TargetObjectInfo> drainageChannelList;
    /**
     * 跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
     */
    private List<Integer> followEfficiencyCategory;
    /**
     *跟进时间范围
     */
    private List<FollowEfficiencyBO> followEfficiencyBO;

    //2022-4-24
    /**
     * 是否公海  0否 1是
     */
    private Integer isOpenSea;
    /**
     * 是否老用户 1是 0否,2022-04-26
     */
    private String regularSubscriber;
    /**
     * 是否上门回访 1是 0否
     */
    private String isHouserholdsVisit;

    /**
     * 小区id
     */
    private List<Long> villageIds;
    /**
     *小区标识  判断是否查询了空小区
     */
    private boolean villageFlag;

    /**
     * 详细地址
     */
    private String address;
    /**
     * 回访状态：101：上门设计 102:有购买需求 103:未接听104:沟通后无意向 105:未报名 106:其他需求
     */
    private List<String> visitStatusCodes;
    /**
     *回访状态标志位判断是否选择了空回访状态
     */
    private Boolean visitStatusFlag;
    /**
     * aesKey
     */
    private String aesKey;
    /**
     * 二服务编码
     */
    private List<String> serverCodes;
    /**
     * 一级服务编码
     */
    private List<String> fullServerCodes;

    /**
     * 服务动作时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date serverTimeStart;
    /**
     * 服务动作时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date serverTimeEnd;

    /**
     *relationShipFlag
     */
    private Integer relationShipFlag;
    /**
     * 话题活动id集合
     */
    private List<Long> participateActivities;

    /**
     * 是否场景化交互：1是 0否
     */
    private Integer isSceneInteraction;
    /**
     * 是否成交定金订单， 1.是  0.否
     */
    private Integer orderEarnestFlag;
    /**
     * 门店标签
     */
    private List<String> keyWordSet;
    /**
     * 模糊匹配字段获取线索ids
     */
    private List<Long> fuzzyMatchCluesIds;
    /**
     * 装修类型ids
     */
    private List<Long> decorateTypeIds;

    /**
     * 整合营销id集合
     */
    private List<Long> imIds;


    /**
     * 新增留资方式:1 顾客主动留资,2 导购手动录入
     */
    private Integer creatCluesMethod;
    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    private Integer kitchenDesignFlag;
    /**
     * 是否含换装报告（1.是 0.否
     */
    private Integer costumeChangeReportFlag;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    private Integer freehandSketchingFlag;
    /**
     * 服务动作执行质量评价 评价状态 0 待评价，1符合标准，2不符合标准
     */
    private Integer followUpEvaluate;

    private String designerClubMemberGrade;

    private List<String> designerClubMemberGradeList;
    private List<Long> designerClubMemberIdList;
    /**
     * 未跟进天数开始
     */
    private Integer startUnfollowDays;
    /**
     * 未跟进天数结束
     */
    private Integer endUnfollowDays;
    private String startUnfollowTime;
    private String endUnfollowTime;
    /**
     * 门店渠道细分code
     */
    private Set<String> storeChannelSubdivisionCodes;
    private Set<String> clueLevels;

    private Set<Integer> scores;
    /**
     * 提交时间 asc按照回访时间正序;desc按照回访时间倒序
     */
    private String fundingTimeSort;
    /**
     * 装修类型
     */
    private List<String> houseRenovationTypeIds;
    /**
     * 房屋类型
     */
    private List<String> houseTypeIds;

    private List<String> designerIndustryLevelList;

}
