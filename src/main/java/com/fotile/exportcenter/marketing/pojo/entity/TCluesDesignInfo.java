package com.fotile.exportcenter.marketing.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 上门设计关联表(TCluesDesignInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-05-23 14:36:50
 */
@Data
public class TCluesDesignInfo implements Serializable {
    private static final long serialVersionUID = -27969941808415381L;
    
    private Long id;
    /**
     * 线索id
     */
    private Long cluesId;
    /**
     * 预约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appointmentTime;
    /**
     * 完成测量时间
     */
    private Date measureTime;
    /**
     * 完成设计时间
     */
    private Date designTime;
    /**
     * 预约时间段
     */
    private String appointmentQuantum;
    /**
     * 方案类型：1:简易，2：场景化
     */
    private Integer caseType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String reviseName;
    /**
     * 修改时间
     */
    private Date reviseTime;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 更新人
     */
    private String modifiedBy;
    /**
     * 更新时间
     */
    private Date modifiedDate;
    /**
     * 0-未删除, 1-已删除 
     */
    private Integer isDeleted;

}

