package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "`marketingcenter`.`t_clues_label_mapping`")
public class TCluesLabelMappingEntity extends AuditingEntity {
    /**
     * 线索id
     */
    @TableField(value = "`clues_id`")
    private Long cluesId;

    /**
     * 标签类别 1.服务类型 2.需求识别
     */
    @TableField(value = "`label_category`")
    private Integer labelCategory;

    /**
     * 标签id
     */
    @TableField(value = "`label_id`")
    private Long labelId;

    /**
     * 标签值
     */
    @TableField(value = "`label_value`")
    @Size(max = 64,message = "标签值最大长度要小于 64")
    private String labelValue;

    /**
     * 标签编码
     */
    @TableField(value = "`label_code`")
    @Size(max = 64,message = "标签编码最大长度要小于 64")
    private String labelCode;
}