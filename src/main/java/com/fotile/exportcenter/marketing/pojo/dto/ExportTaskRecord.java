package com.fotile.exportcenter.marketing.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 导出任务记录表 ExportTaskRecord 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-13 10:30:29
 */
 @ApiModel(value="ExportTaskRecord", description="导出任务记录表")
 @Data
public class ExportTaskRecord implements Serializable {

	private static final long serialVersionUID = 1L;
	
		
	/**  **/
	@ApiModelProperty(value = "")
	private Long id;
		
	/**  **/
	@ApiModelProperty(value = "")
	private Long isDeleted;
		
	/**  **/
	@ApiModelProperty(value = "")
	private String createdBy;
		
	/**  **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date createdDate;
		
	/**  **/
	@ApiModelProperty(value = "")
	private String modifiedBy;
		
	/**  **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "")
	private Date modifiedDate;
		
	/** 备注 **/
	@ApiModelProperty(value = "备注")
	private String remark;
		
	/** 操作人id **/
	@ApiModelProperty(value = "操作人id")
	private String operatorId;
		
	/** 操作人username **/
	@ApiModelProperty(value = "操作人username")
	private String operatorName;
		
	/** 最后修改时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "最后修改时间")
	private Date reviseTime;
		
	/** 分公司id **/
	@ApiModelProperty(value = "分公司id")
	private Long companyId;
		
	/** 分公司名称 **/
	@ApiModelProperty(value = "分公司名称")
	private String companyName;
		
	/** 类型--数据字典维护 **/
	@ApiModelProperty(value = "类型--数据字典维护")
	private String type;
		
	/** 导出任务名称 **/
	@ApiModelProperty(value = "导出任务名称")
	private String taskName;
		
	/** 任务状态 0:未开始，1：进行中，2：成功,3:失败 **/
	@ApiModelProperty(value = "任务状态 0:未开始，1：进行中，2：成功,3:失败")
	private Integer status;
		
	/** 进度 **/
	@ApiModelProperty(value = "进度")
	private BigDecimal progress;
		
	/** 总条数 **/
	@ApiModelProperty(value = "总条数")
	private Integer totalCount;
		
	/** 开始时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "开始时间")
	private Date startTime;
		
	/** 结束时间 **/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value = "结束时间")
	private Date endTime;
		
	/** 入参 **/
	@ApiModelProperty(value = "入参")
	private String paramJson;
		
	/** 失败原因 **/
	@ApiModelProperty(value = "失败原因")
	private String failReason;

	@ApiModelProperty(value = "文件链接")
	private String fileUrl;
		
	/** 备用 **/
	@ApiModelProperty(value = "备用")
	private String ext1;
		
	/** 备用 **/
	@ApiModelProperty(value = "备用")
	private String ext2;
		
	/** 备用 **/
	@ApiModelProperty(value = "备用")
	private String ext3;
		
	/** 备用 **/
	@ApiModelProperty(value = "备用")
	private String ext4;
	/**
	 * 服务动作执行质量评价 评价状态 0 待评价，1符合标准，2不符合标准
	 */
	private Integer followUpEvaluate;
	
}