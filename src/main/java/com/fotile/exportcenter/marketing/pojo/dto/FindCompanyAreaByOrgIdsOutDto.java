package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindCompanyAreaByOrgIdsOutDto implements Serializable{
	
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="公司id",example="公司id")
	private Long id;
	
	@ApiModelProperty(value="组织机构id",example="组织机构id")
	private Long orgId;
	
	@ApiModelProperty(value="大区id",example="大区id")
	private Integer areaId;
	    
	@ApiModelProperty(value="大区编码",example="大区编码")
	private String valueCode;
	
	@ApiModelProperty(value="大区名称",example="大区名称")
	private String valueName;
	
	@ApiModelProperty(value="截单时间",example="截单时间")
	private String cutTime;
	
	@ApiModelProperty(value="线索未跟进天数提醒阈值",example="线索未跟进天数提醒阈值")
	private Integer cluesRemind;
}
