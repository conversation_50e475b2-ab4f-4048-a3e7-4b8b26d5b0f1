package com.fotile.exportcenter.marketing.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;

/**
 * @ Author     ：黄学后
 * @ Date       ：Created in 14:54 2020/4/10
 * @ Description：
 * @ Modified By：
 * @Version: $
 */
@Data
public class QueryByParamsOutDto implements Serializable {

    @Id
    @ApiModelProperty("表主键")
    @TableField(value = "ID")
    private Long id;

    @TableField(value = "value_code")
    @ApiModelProperty("数据字典具体的值编码。用于外键存储")
    private String valueCode;

    @TableField(value = "type_code")
    @ApiModelProperty("分类编码")
    private String typeCode;

    @TableField(value = "value_name")
    @ApiModelProperty("数据字典下拉框显示的名称")
    private String valueName;

    @TableField(value = "active")
    @ApiModelProperty("是否有效")
    private Integer active;

    @TableField(value = "sort")
    @ApiModelProperty("排序")
    private Integer sort;


}

