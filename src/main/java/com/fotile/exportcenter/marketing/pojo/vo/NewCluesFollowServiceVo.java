package com.fotile.exportcenter.marketing.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class NewCluesFollowServiceVo implements Serializable {
    @ApiModelProperty("序号")
    private Integer index;

    private Long id;
    private Long followId;
    //顾客姓名
    @FieldEncrypt
    private String customerName;
    //大区
    private String districtValue;
    //分公司
    private Long companyId;
    //分公司
    private String companyName;
    //门店渠道名称
    private String storeTypeName;
    //门店code
    private Long stroeId;
    //门店code
    private String stroeCode;
    //门店
    private String stroeName;
    //负责人名称
    private String chargeCode;
    //业务员
    @FieldEncrypt
    private String salesman;

    @ApiModelProperty(value = "操作类型")
    private String sourceType;
    @ApiModelProperty(value = "跟进类型 1.电话，2.微信，3.到访")
    private String type;

    @ApiModelProperty(value = "服务动作")
    private String serviceAction;

    @ApiModelProperty(value = "跟进内容")
    private String contentText;

    @ApiModelProperty(value = "装修进度")
    private String decorateProgres;

    //操作类型

    @ApiModelProperty(value = "业务员姓名")
    @FieldEncrypt
    private String chargeUserName;
    @ApiModelProperty(value = "跟进时间")
    private Date followDate;

    @ApiModelProperty(value = "地址")
    @FieldEncrypt
    private String address;

    @ApiModelProperty("得分")
    private Integer score;

    @ApiModelProperty("日志图片")
    private String coverUrl1;

    @ApiModelProperty("日志图片")
    private String coverUrl2;

    @ApiModelProperty("日志图片")
    private String coverUrl3;

    @ApiModelProperty("日志图片")
    private String coverUrl4;

    @ApiModelProperty("日志图片")
    private String coverUrl5;
    @ApiModelProperty("日志图片")
    private String coverUrl6;

    @ApiModelProperty("日志图片")
    private String coverUrl7;

    @ApiModelProperty("日志图片")
    private String coverUrl8;

    @ApiModelProperty("日志图片")
    private String coverUrl9;

    @ApiModelProperty("跟进次数")
    private Integer followUpCount;
    @ApiModelProperty("下次跟进时间")
    private Date nextFollowUpTime;

    //是否有上门设计报告
    private String isComeDeviseReport;

    //质量评价结果
    private String evaluateStatus;
    //质量评价人
    @FieldEncrypt
    private String evaluateUserName;
    //质量评价时间
    private Date evaluateTime;
    /**
     *是否测量完整尺寸 （1.是 0.否）
     */

    private String kitchenDesignFlagName;
    /**
     * 是否含换装报告（1.是 0.否
     */

    private String costumeChangeReportFlagName;
    /**
     * 是否上传手绘图（1.是 0.否）
     */

    private String freehandSketchingFlagName;

    private String comeUrls;

    private String evaluateResultDesc;

    private String distributorName;
    private String channelCategoryName;
}
