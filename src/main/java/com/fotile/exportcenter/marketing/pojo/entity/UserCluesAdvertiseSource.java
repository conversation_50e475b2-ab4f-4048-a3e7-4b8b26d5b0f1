package com.fotile.exportcenter.marketing.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserCluesAdvertiseSource implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 
     */
	private Long id;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Long isDeleted;
	
	/**
     * 
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date createdDate;
	
	/**
     * 
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date modifiedDate;
	
	/**
     * 线索ID
     */
	private Long userCluesId;
	
	/**
     * data类型：18抖音
     */
	private Long type;
	
	/**
     * 投放账户名称
     */
	private String advertiseSourceAccountName;
	
	/**
     * 投放数据订单id
     */
	private String advertiseSourceOrderId;
	
	/**
     * 投放数据线索id
     */
	private String advertiseSourceClueId;

}