package com.fotile.exportcenter.marketing.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class TargetObjectInfo implements Serializable {
    /**
     * 目标对象ID(家装公司、小B端引流、家装设计师、物业政企燃气ID)
     */
    private Long id;

    /**
     * 目标对象名称(家装公司、小B端引流、家装设计师、物业政企燃气名称)
     */
    @FieldEncrypt
    private String name;

    /**
     * 目标对象编码(家装公司、小B端引流、家装设计师、物业政企燃气编码)
     */
    private String code;

    /**
     * 目标对象手机(家装公司、小B端引流、家装设计师、物业政企燃气手机)
     */
    @FieldEncrypt
    private String phone;

    /**
     * 目标对象类型(1:家装公司; 2:小B端引流; 3:家装设计师; 4:物业政企燃气(部分接口返回数据归在2中))
     */
    private Integer type;

    /**
     * 目标对象类型名称
     */
    private String typeName;

    /**
     * 目标对象分类(家装公司、小B端引流分类、家装设计师、物业政企燃气分类)
     */
    private String category;

    /**
     * 目标对象分类名称
     */
    private String categoryName;

    /**
     * 目标所属分公司orgId
     */
    private Long companyOrgId;

    /**
     * 目标所属门店orgId
     */
    private Long storeOrgId;

    /**
     * 目标状态(0:禁用; 1:启用)
     */
    private Integer status;

    /**
     * 目标状态名称
     */
    private String statusName;

    /**
     * 审核状态(1:待审核; 2:已通过; 3:已拒绝)
     */
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    private String auditStatusName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 修改时间
     */
    private Date modifiedDate;
}
