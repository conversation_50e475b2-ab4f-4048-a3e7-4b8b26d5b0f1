package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class CluesScorePortionExportOutDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"}, index = 0)
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"MD5"}, index = 1)
    @ApiModelProperty("MD5")
    private String md5;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"}, index = 2)
    @ApiModelProperty("线索ID")
    private Long id;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索标签"}, index = 3)
    @ApiModelProperty("线索标签")
    private String tag;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索标签评分"}, index = 4)
    @ApiModelProperty("线索标签评分")
    private String tagScore;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"}, index = 5)
    @ApiModelProperty("姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"手机号"}, index = 6)
    @ApiModelProperty("手机号")
    private String customerPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"}, index = 7)
    @ApiModelProperty("大区")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"}, index = 8)
    @ApiModelProperty("分公司")
    private String companyName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店"}, index = 9)
    @ApiModelProperty("门店")
    private String stroeName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"省"}, index = 10)
    @ApiModelProperty("省")
    private String provinceName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"市"}, index = 11)
    @ApiModelProperty("市")
    private String cityName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"区"}, index = 12)
    @ApiModelProperty("区")
    private String countyName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"详细地址"}, index = 13)
    @ApiModelProperty("详细地址")
    private String address;

    @ColumnWidth(10)
    @ExcelProperty(value = {"小区"}, index = 14)
    @ApiModelProperty("小区")
    private String village;





}
