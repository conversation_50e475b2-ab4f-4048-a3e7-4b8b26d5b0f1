package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class CluesPortionExportOutDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"}, index = 0)
    @ApiModelProperty("序号")
    private Integer index;

    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"}, index = 1)
    @ApiModelProperty("线索ID")
    private Long id;

    @ColumnWidth(15)
    @ExcelProperty(value = {"来源渠道"}, index = 2)
    @ApiModelProperty("来源渠道")
    private String channelName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"来源频道"}, index = 3)
    @ApiModelProperty("来源频道")
    private String radioName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"活动ID"}, index = 4)
    @ApiModelProperty("活动ID")
    private String activityId1;

    @ColumnWidth(15)
    @ExcelProperty(value = {"活动类型"}, index = 5)
    @ApiModelProperty("活动类型")
    private String activityType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"活动名称"}, index = 6)
    @ApiModelProperty("活动名称")
    private String activityName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"}, index = 7)
    @ApiModelProperty("姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"性别"}, index = 8)
    @ApiModelProperty("性别")
    private String gender;

    @ColumnWidth(10)
    @ExcelProperty(value = {"手机号"}, index = 9)
    @ApiModelProperty("手机号")
    private String customerPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = {"微信号"}, index = 10)
    @ApiModelProperty("微信号")
    private String wechatno;

    @ColumnWidth(15)
    @ExcelProperty(value = {"提交时间"}, index = 11)
    @ApiModelProperty("提交时间")
    private Date fundingTime;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"}, index = 12)
    @ApiModelProperty("大区")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"}, index = 13)
    @ApiModelProperty("分公司")
    private String companyName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"}, index = 14)
    @ApiModelProperty("门店编码")
    private String stroeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"门店"}, index = 15)
    @ApiModelProperty("门店")
    private String stroeName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道"}, index = 16)
    @ApiModelProperty("门店渠道")
    private String storeTypeName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"线索创建人"}, index = 17)
    @ApiModelProperty("线索创建人")
    private String createUserName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员"}, index = 18)
    @ApiModelProperty("业务员")
    private String salesman;

    @ColumnWidth(20)
    @ExcelProperty(value = {"其他说明"}, index = 19)
    @ApiModelProperty("其他说明")
    private String others;

    @ColumnWidth(20)
    @ExcelProperty(value = {"电话回访状态"}, index = 20)
    @ApiModelProperty("电话回访状态")
    private String visitStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = {"下次回访时间"}, index = 21)
    @ApiModelProperty("下次回访时间")
    private Date visitTime;

    @ColumnWidth(20)
    @ExcelProperty(value = {"最近更新时间"}, index = 22)
    @ApiModelProperty("最近更新时间")
    private Date reviseTime;

    @ColumnWidth(15)
    @ExcelProperty(value = {"备注描述"}, index = 23)
    @ApiModelProperty("备注描述")
    private String remark;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索状态"}, index = 24)
    @ApiModelProperty("线索状态")
    private String status;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索分类"}, index = 25)
    @ApiModelProperty("线索分类")
    private String cluesType;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索来源"}, index = 26)
    @ApiModelProperty("线索来源")
    private String cluesSource;

    @ColumnWidth(15)
    @ExcelProperty(value = {"线索等级"}, index = 27)
    @ApiModelProperty("线索等级")
    private String cluesLevel;

    @ColumnWidth(15)
    @ExcelProperty(value = {"无效O2O线索标记"}, index = 28)
    @ApiModelProperty("无效O2O线索标记")
    private String reviewStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = {"当前跟进状态"}, index = 29)
    @ApiModelProperty("当前跟进状态")
    private String followUpStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-顾客反馈概述"}, index = 30)
    @ApiModelProperty("丢单原因-顾客反馈概述")
    private String lostOrderValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-顾客反馈明细"}, index = 31)
    @ApiModelProperty("丢单原因-顾客反馈明细")
    private String lostOrderSubValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"丢单原因-自我不足"}, index = 32)
    @ApiModelProperty("丢单原因-自我不足")
    private String selfLackReviewStatusValue;

    @ColumnWidth(20)
    @ExcelProperty(value = {"utm_source"}, index = 33)
    @ApiModelProperty("utm_source")
    private String utmSource;

}
