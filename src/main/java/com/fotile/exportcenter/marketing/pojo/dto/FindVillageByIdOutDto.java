package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindVillageByIdOutDto implements Serializable {

    @ApiModelProperty(value="小区id",example="小区id")
    private Long id;

    @ApiModelProperty(value="小区名称",example="小区名称")
    private String name;

    @ApiModelProperty(value="地址-省",example="地址-省id")
    private Long provicenId;

    @ApiModelProperty(value="地址-省名称",example="地址-省名称")
    private String provicenName;

    @ApiModelProperty(value="地址-市",example="地址-市id")
    private Long cityId;

    @ApiModelProperty(value="地址-市名称",example="地址-市名称")
    private String cityName;

    @ApiModelProperty(value="地址-区",example="地址-区id")
    private Long countyId;

    @ApiModelProperty(value="地址-区名称",example="地址-区名称")
    private String countyName;

    @ApiModelProperty(value="详细地址",example="详细地址")
    private String address;

    @ApiModelProperty(value="竣工时间",example="竣工时间")
    private String finishTime;

    @ApiModelProperty(value="开发商",example="开发商")
    private String developer;

    @ApiModelProperty(value="总户数",example="总户数")
    private Integer num;

    @ApiModelProperty(value="备注",example="备注")
    private String note;

    @ApiModelProperty(value="归属门店",example="归属门店id")
    private Long storeId;

    @ApiModelProperty(value="经度")
    private String longitude;

    @ApiModelProperty(value="纬度")
    private String latitude;
}
