package com.fotile.exportcenter.marketing.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DecorateAndDesignerDto {

    @ApiModelProperty(value = "家装公司编码")
    @NotBlank(message = "code不能为空")
    private String code;

    @ApiModelProperty(value = "家装公司名称")
    private String name;

    @ApiModelProperty(value = "家装公司名称")
    private String phone;

    @ApiModelProperty(value = "设计师编码")
    private String designerCode;

    @ApiModelProperty(value = "设计师名称")
    private String designerName;

    @ApiModelProperty(value = "设计师手机号")
    private String designerPhone;

    @ApiModelProperty("1家装公司，2异业三工，3设计师")
    @NotBlank(message = "type不能为空")
    private Integer type;

    /**
     * 异业三工分类
     */
    private String decorateCategory;
}
