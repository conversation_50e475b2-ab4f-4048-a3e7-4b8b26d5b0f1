package com.fotile.exportcenter.marketing.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class BaseAttributeVo implements Serializable {

    /** 主键 **/
    @ApiModelProperty(value = "主键")
    private Long id;

    /** 字段英名 **/
    @ApiModelProperty(value = "字段英名")
    private String fieldId;

    /** 属性值ID **/
    @ApiModelProperty(value = "属性值ID")
    private String attributeId;

    /** 属性值名称 **/
    @ApiModelProperty(value = "属性值名称")
    private String attributeValue;

    /** 排序 **/
    @ApiModelProperty(value = "排序")
    private Long attributeSort;
}
