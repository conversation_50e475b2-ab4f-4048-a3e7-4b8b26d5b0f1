package com.fotile.exportcenter.marketing.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.TreeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 渠道实体类----一个渠道对应多个频道
 *
 * <AUTHOR>
 */
@Data
@Entity
@ApiModel(value = "渠道", description = "渠道")
@TableName(value = "t_channel")
@Accessors(chain = true)
public class ChannelEntity extends TreeEntity {

    @NotBlank(message = "渠道编码不能为空")
    @TableField(value = "code")
    @ApiModelProperty(value = "渠道编码", example = "渠道编码")
    private String code;

    @NotBlank(message = "渠道名称不能为空")
    @TableField(value = "name")
    @ApiModelProperty(value = "渠道名称", example = "渠道名称")
    private String name;

    @NotNull(message = "渠道类型不能为空")
    @TableField(value = "type")
    @ApiModelProperty(value = "渠道类型：1：App；2：H5；3：小程序；4：PC", example = "渠道类型：1：App；2：H5；3：小程序；4：PC")
    private Byte type;

    @TableField(value = "note")
    @ApiModelProperty(value = "备注", example = "备注")
    private String note;

    @TableField(value = "status")
    @ApiModelProperty(value = "状态,0:禁用；1：启用", example = "状态,0:禁用；1：启用")
    private byte status = 1;

    @Transient
    @ApiModelProperty(value = "父渠道名称", example = "父渠道名称")
    private String parentName;

    @Transient
    @ApiModelProperty(value = "状态值", example = "状态值:禁用；启用")
    private String statusName;

    @Transient
    @ApiModelProperty(value = "关联的频道", example = "关联的频道")
    private List<RadioEntity> radios;
}
