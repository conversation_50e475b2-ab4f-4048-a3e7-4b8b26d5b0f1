package com.fotile.exportcenter.marketing.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class Dict implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 数据字典分类，属于固定值根据代码而来
     */
    private String typeCode;
    /**
     * 数据字典值用于各类表的外键存储
     */
    private String valueCode;
    /**
     * 数据字典业务上显示的名称
     */
    private String valueName;
    /**
     * 页面使用时排序字段
     */
    private Integer sort;
    /**
     * 页面使用时是否生效，不影响后端查询逻辑 0失效 1生效
     */
    private Integer active;

    private Long parentId;
    /**
     * 创建人keycloak系统里的账户ID
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人keycloak系统里的账户ID
     */
    private String modifiedBy;
    /**
     * 修改时间
     */
    private Date modifiedDate;
    /**
     * 是否删除 0未删除 非0等于ID标示删除
     */
    private Byte isDeleted;
}