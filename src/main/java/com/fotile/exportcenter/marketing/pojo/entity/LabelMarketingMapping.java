package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Entity;

@Data
@Entity
@ApiModel(value = "标签关系表", description = "标签关系表")
@TableName(value = "label_marketing_mapping", schema = "marketingcenter")
public class LabelMarketingMapping extends AuditingEntity {
    @TableField(value = "source_table_name")
    /**
     * 标签类型 1：线索 user_clues\n" + "2：活动 activity\n" + "3：内容 content  content_menu\n" + "4: 音频/视屏 audio_video
     */
    private String sourceTableName;

    @TableField(value = "source_id")
    /**
     * 来源ID
     */
    private Long sourceId;

    @TableField(value = "label_id")
    /**
     * 标签id
     */
    private Long labelId;

    @TableField(value = "label_sort")
    /**
     * 标签顺序
     */
    private Long labelSort;


}
