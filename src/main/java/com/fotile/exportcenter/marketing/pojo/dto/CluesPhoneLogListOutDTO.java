package com.fotile.exportcenter.marketing.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CluesPhoneLogListOutDTO {
    @ColumnWidth(10)
    @ExcelProperty(value = {"序号"})
    @ApiModelProperty("序号")
    private Integer index;
    @ColumnWidth(10)
    @ExcelProperty(value = {"线索ID"})
    @ApiModelProperty("线索ID")
    private String sourceId;

    @ColumnWidth(10)
    @ExcelProperty(value = {"姓名"})
    @ApiModelProperty("顾客姓名")
    private String customerName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty("大区名称")
    private String districtValue;

    @ColumnWidth(10)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty("分公司名称")
    private String companyName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})
    @ApiModelProperty("门店编码")
    private String stroeCode;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店名称"})
    @ApiModelProperty("门店全称")
    private String storeName;

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员编码"})
    @ApiModelProperty("业务员编码")
    private String chargeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员"})
    @ApiModelProperty("业务员")
    private String salesMan;

    @ColumnWidth(10)
    @ExcelProperty(value = {"拨打电话业务员编码"})
    @ApiModelProperty("拨打电话业务员编码")
    private String dialchargeCode;

    @ColumnWidth(10)
    @ExcelProperty(value = {"拨打电话业务员姓名"})
    @ApiModelProperty("拨打电话业务员姓名")
    private String dialChargeUserName;

    @ColumnWidth(10)
    @ExcelProperty(value = {"拨打时间"})
    @ApiModelProperty("拨打时间")
    private Date createdDate;

    @ColumnWidth(10)
    @ExcelProperty(value = {"来源"})
    @ApiModelProperty("来源")
    private String dialSource;

}
