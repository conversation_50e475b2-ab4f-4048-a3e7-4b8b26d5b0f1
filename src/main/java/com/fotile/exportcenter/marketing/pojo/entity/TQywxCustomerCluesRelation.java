package com.fotile.exportcenter.marketing.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 线索扩展表(TQywxCustomerCluesRelation)实体类
 *
 * <AUTHOR>
 * @since 2021-11-12 13:42:33
 */
@Data
public class TQywxCustomerCluesRelation implements Serializable {
    private static final long serialVersionUID = 411140952637030251L;
    /**
     * 自增长id
     */
    private Long id;

    /**
     * 账号id
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 客户的userid
     */
    private String externalUserid;
    /**
     * 唯一身份标识
     */
    private String unionId;
    /**
     * 线索表ID
     */
    private Long cluesId;
    private List<Long> cluesIds;
    /**
     * 线索手机号
     */
    private String customerPhone;
    /**
     * 成员id
     */
    private String userId;
    /**
     * 删除标志
     */
    private Integer isDeleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人
     */
    private String modifiedBy;
    /**
     * 修改时间
     */
    private Date modifiedDate;

}