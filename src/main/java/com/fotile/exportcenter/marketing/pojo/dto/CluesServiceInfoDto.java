package com.fotile.exportcenter.marketing.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class CluesServiceInfoDto implements Serializable {
	 /**
     * 主键id
     */
	private Long id;
	
	 /**
     * 线索id
     */
	private Long cluesId;
	
	 /**
     * csm服务工单编码
     */
	private String csmOrderCode;
	/**
	 * 技师编号
	 */
	private String serviceEngineerCsn;

}