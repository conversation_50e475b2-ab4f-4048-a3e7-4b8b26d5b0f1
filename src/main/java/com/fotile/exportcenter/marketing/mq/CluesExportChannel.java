package com.fotile.exportcenter.marketing.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface CluesExportChannel {
    /**
     * 处理放松【发送通道】
     */
    String CLUES_EXPORT_PRODUCES_OUTPUT = "clues_export_produces_output";

    /**
     * 发待处理消息【接收通道】
     */
    String CLUES_EXPORT_CONSUMES_INPUT = "clues_export_consumes_input";

    @Output(CLUES_EXPORT_PRODUCES_OUTPUT)
    MessageChannel sendCluesExportProduces();

    /**
     * 发消息【j接受】
     *
     * @return
     */
    @Input(CLUES_EXPORT_CONSUMES_INPUT)
    SubscribableChannel disposeCluesExportConsumes();

    /**
     * ===========================================================
     */

    String CMS_EXPORT_INPUT = "cms_export_input";

    String CMS_EXPORT_OUTPUT = "cms_export_output";

    /**
     * cmscenter导出(接受)
     */
    @Input(CMS_EXPORT_INPUT)
    SubscribableChannel cmsExportInput();

    /**
     * cmscenter导出(发送)，统一定义，希望导出统一，不要在创建新的topic
     */
    @Output(CMS_EXPORT_OUTPUT)
    MessageChannel cmsExportOutput();


}
