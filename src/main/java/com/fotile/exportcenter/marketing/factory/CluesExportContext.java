package com.fotile.exportcenter.marketing.factory;


import com.fotile.exportcenter.marketing.service.CluesExportImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CluesExportContext {

    @Autowired
    Map<String, CluesExportImpl> cluesExportMap = new ConcurrentHashMap<>(4);


    public CluesExportImpl getCluesExportImpl(String component) throws Exception{
        CluesExportImpl strategy = cluesExportMap.get(component);
        if(strategy == null) {
            throw new RuntimeException("no strategy defined");
        }
        return strategy;
    }
}
