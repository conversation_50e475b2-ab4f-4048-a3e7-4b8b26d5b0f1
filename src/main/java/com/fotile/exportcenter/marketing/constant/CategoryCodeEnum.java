package com.fotile.exportcenter.marketing.constant;

import lombok.Getter;

@Getter
public enum CategoryCodeEnum {

    category_10("follow_server_category_10","成交前"),
    category_20("follow_server_category_20","成交后-安装前"),
    category_30("follow_server_category_30","安装后"),
    ;

    private String code;
    private String name;

    CategoryCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getCategory(String code) {
        String name = null;
        for (CategoryCodeEnum enumVal : CategoryCodeEnum.values()) {
            if (enumVal.getCode().equals(code)) {
                name = enumVal.getName();
                break;
            }
        }
        return name;
    }
}
