package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.FindAttributeValueOutDto;
import com.fotile.exportcenter.marketing.pojo.entity.BaseAttribute;
import com.fotile.exportcenter.marketing.pojo.vo.BaseAttributeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 值集基表 BaseAttributeMapper
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-18 15:33:46
 */
public interface BaseAttributeMapper extends BaseMapper<BaseAttribute> {

    int deleteByPrimaryKey(Long id);
		
    int insert(BaseAttribute record);

    int insertSelective(BaseAttribute record);

    BaseAttribute selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BaseAttribute record);

    int updateByPrimaryKey(BaseAttribute record);

    List<BaseAttributeVo> getAttributeByFieldId(@Param("filedId")String fieldId, @Param("value")String value);

    BaseAttributeVo getAttributeByAttributeId(String fieldId,String attributeId);

    List<BaseAttributeVo> getAttributeByFieldIds(@Param("fieldIds") List<String> fieldIds);

    List<FindAttributeValueOutDto> getByFiledId(@Param("filedId") String filedId);

    List<FindAttributeValueOutDto> findAttributeByFieldIds(@Param("fieldIds") List<String> fieldIds);

}