package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.fotile.exportcenter.marketing.pojo.dto.FollowServerDTO;
import com.fotile.exportcenter.marketing.pojo.dto.SelectAttributeLevelInDto;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOneTweOutDto;
import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TAttributeLevelConfigMapper extends BaseMapper<TAttributeLevelConfig> {
    int updateBatch(List<TAttributeLevelConfig> list);

//    int updateBatchSelective(List<TAttributeLevelConfig> list);

    int batchInsert(@Param("list") List<TAttributeLevelConfig> list);

    int insertOrUpdate(TAttributeLevelConfig record);

    int insertOrUpdateSelective(TAttributeLevelConfig record);

    List<SelectAllByFiledIdVO> findByFiledIds(@Param("fieldIds") List<String> fieldIds);

    List<TAttributeLevelConfig> selectListByFieldIdAndLevel(@Param("fieldId") String fieldId , @Param("level")Integer level,@Param("parentAttributeId")String parentAttributeId);

    TAttributeLevelConfig selectListByFieldIdAndAttributeAndLevel(@Param("fieldId")String fieldId, @Param("attributeId") String attributeId,  @Param("level")Integer level);

    SelectAllByFiledIdVO selectAllByFiledIdAndAttributeId(SelectAttributeLevelInDto selectAttributeLevelInDto);

    SelectOneTweOutDto selectOneTweConfigByFiledIdAndAttributeId(@Param("fieldId")String fieldId, @Param("attributeId") String attributeId);

    List<FollowServerDTO> getFollowServerList();

    List<SelectAllByFiledIdVO> selectAllByFiledIdAndAttributeIds(@Param("attributeIds") List<String> attributeIds);


    List<TAttributeLevelConfig> selectListByFieldIdAndLevelAndParentIds(@Param("fieldId") String fieldId , @Param("level")Integer level,@Param("parentAttributeIds")List<String> parentAttributeId);


}