package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.FindLabelBySourceIdInDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindLabelBySourceIdOutDto;
import com.fotile.exportcenter.marketing.pojo.entity.LabelMarketingMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LabelMappingDao extends BaseMapper<LabelMarketingMapping>
{

//    @ApiOperation("批量添加")
//    public Integer batchInsertLabelMapping(@Param("list") List<LabelMarketingMapping> labelMarketingMappingList);

    public List<FindLabelBySourceIdOutDto> findLabelBySourceId(FindLabelBySourceIdInDto findLabelBySourceIdInDto);

    List<FindLabelBySourceIdOutDto> findLabelNameById(@Param("labelIds")List<Long> labelIds, @Param("sourceTableName")String sourceTableName);
    List<FindLabelBySourceIdOutDto> findLabelNameById(@Param("labelIds")Long[] labelIds, @Param("sourceTableName")String sourceTableName);

    List<FindLabelBySourceIdOutDto> findLabelNameByIds( @Param("ids") List<Long> ids, @Param("sourceTableName")String sourceTableName);
}
