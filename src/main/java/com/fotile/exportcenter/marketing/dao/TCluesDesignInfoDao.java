package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.entity.TCluesDesignInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上门设计关联表(TCluesDesignInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-23 14:36:50
 */
public interface TCluesDesignInfoDao extends BaseMapper<TCluesDesignInfo> {

    /**
     * 通过线索ids查询多条数据
     */
    List<TCluesDesignInfo> queryByCluesIds(@Param("cluesIds") List<Long> cluesId);
}

