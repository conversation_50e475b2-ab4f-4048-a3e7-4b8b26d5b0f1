package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.SelectAllPictureBysourceIdListOutDTO;
import com.fotile.exportcenter.marketing.pojo.entity.PictureMarketing;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PictureMarketingDao extends BaseMapper<PictureMarketing> {
    public List<PictureMarketing> selectAllByPictureMarketing(@Param("sourceId")Long sourceId, @Param("sourceTableName")String sourceTableName);
    public List<String> selectPictureMarketingBySourceId(@Param("sourceId")Long sourceId,@Param("sourceTableName")String sourceTableName);



    public List<SelectAllPictureBysourceIdListOutDTO> selectAllPictureBysourceIdList(@Param("sourceIds")List<Long> sourceIds, @Param("sourceTableName")String sourceTableName);

    void insertBatch(@Param("list") List<PictureMarketing> list);
    int deleteByIds(@Param("ids") List<Long> ids);
}