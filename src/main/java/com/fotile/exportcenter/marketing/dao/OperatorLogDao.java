package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto;
import com.fotile.exportcenter.marketing.pojo.entity.OperatorMarketingLog;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OperatorLogDao extends BaseMapper<OperatorMarketingLog> {


    @DruidTxcMultiDataSourceAnnontation("adb")
    Long exportOperatorLogCount(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("adb")
    List<SelectOperatorLogListOutDto> newExportOperatorLogList(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("adb")
    Long exportPhoneLogCount(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("adb")
    List<SelectOperatorLogListOutDto> exportPhoneLogExport(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SelectOperatorLogListOutDto> newExportOperatorLogByCluesIds(List<Long> ids);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SelectOperatorLogListOutDto> newFollowUpByCluesIds(List<Long> ids);
}
