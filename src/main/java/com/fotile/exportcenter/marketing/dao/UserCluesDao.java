package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.CluesActivityOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.CluesServiceInfoDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.pojo.entity.CluesServiceInfo;
import com.fotile.exportcenter.marketing.pojo.entity.UserClues;
import com.fotile.exportcenter.marketing.pojo.entity.UserCluesAdvertiseSource;
import com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo;
import com.fotile.exportcenter.marketing.pojo.vo.SelectCluesFollowUpByCluesIdsVO;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserCluesDao extends BaseMapper<UserClues> {



    @DruidTxcMultiDataSourceAnnontation("adb")
    Long querySalesLeadsListForExportCounts(QueryUserCluseInDto inDto);


    @DruidTxcMultiDataSourceAnnontation("adb")
    List<CluesActivityOutDto> newCluesActivityForExport(QueryUserCluseInDto inDto);


    @DruidTxcMultiDataSourceAnnontation("adb")
    Long exportCluesActivityCount(QueryUserCluseInDto inDto);



    @DruidTxcMultiDataSourceAnnontation("adb")
    List<NewExportUserClusesOutDto> newQuerySalesLeadsListForExport(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("adb")
    List<Long> newQuerySalesLeadsIdListForExport(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<NewExportUserClusesOutDto> newQuerySalesLeadsListForExportByIds(List<Long> cluesIds);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SelectCluesFollowUpByCluesIdsVO>  selectCluesFollowUpByCluesIds(@Param("cluesIds") List<Long> cluesIds);

    //线索评分部分字段导出
    @DruidTxcMultiDataSourceAnnontation("adb")
    List<NewExportUserClusesOutDto> cluesScorePortionExport(QueryUserCluseInDto inDto);

    //部分字段导出
    @DruidTxcMultiDataSourceAnnontation("adb")
    List<NewExportUserClusesOutDto> cluesPortionExport(QueryUserCluseInDto inDto);


    @DruidTxcMultiDataSourceAnnontation("adb")
    List<Long> findLogCluesExport(QueryUserCluseInDto inDto);


    @DruidTxcMultiDataSourceAnnontation("adb")
    List<NewCluesFollowServiceVo> newCluesFollowServiceExport(@Param("list") List<Long> ids);
    @DruidTxcMultiDataSourceAnnontation("adb")
    List<Long> newCluesFollowServiceExport2(QueryUserCluseInDto inDto);

    @DruidTxcMultiDataSourceAnnontation("adb")
    Long newExportCluesId(QueryUserCluseInDto inDto);
    @DruidTxcMultiDataSourceAnnontation("adb")
    Long newCluesFollowServiceExportCount(@Param("list") List<Long> ids);

    /** 线索服务*/
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<CluesServiceInfoDto> getCluesServiceByCluesIds(@Param("list") List<Long> finalCollect);


    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<UserCluesAdvertiseSource> getUserCluesAdvertiseSourceByCluesIds(@Param("list") List<Long> ids);

}
