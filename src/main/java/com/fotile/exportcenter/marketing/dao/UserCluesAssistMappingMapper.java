package com.fotile.exportcenter.marketing.dao;


import com.fotile.exportcenter.marketing.pojo.entity.UserCluesAssistMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface UserCluesAssistMappingMapper {



    List<UserCluesAssistMapping> selectListByCluesId(@Param("cluesId") Long cluesId);

    List<UserCluesAssistMapping> selectListByCluesIds(@Param("cluesIds") List<Long> cluesIds);

    int deleteAllByCluesId(@Param("cluesId") Long cluesId);

    int deleteAllByCluesIds(@Param("cluesIds") List<Long> cluesIds);

    UserCluesAssistMapping selectOneByCluesIdAndUserId(@Param("cluesId")Long userCluesId, @Param("userId") Long userId);
}