package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

import com.fotile.exportcenter.marketing.pojo.entity.TCluesLabelMappingEntity;
import org.apache.ibatis.annotations.Param;

public interface TCluesLabelMappingMapper extends BaseMapper<TCluesLabelMappingEntity> {
    int updateBatch(List<TCluesLabelMappingEntity> list);

    int updateBatchSelective(List<TCluesLabelMappingEntity> list);

    int batchInsert(@Param("list") List<TCluesLabelMappingEntity> list);

    int insertOrUpdate(TCluesLabelMappingEntity record);

    int insertOrUpdateSelective(TCluesLabelMappingEntity record);

    List<TCluesLabelMappingEntity> selectListByCluesId(@Param("cluesId") Long cluesId);

    List<TCluesLabelMappingEntity> selectListByCluesIds(@Param("cluesIds") List<Long> ids);


}