package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.entity.TQywxCustomerCluesRelation;
import com.fotile.exportcenter.marketing.pojo.vo.QywxCustomerRelationVo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 扩展线索信息表(TQywxCustomerCluesRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-12 13:42:36
 */
@Repository
@Mapper
public interface TQywxCustomerCluesRelationDao extends BaseMapper<TQywxCustomerCluesRelation> {

    List<TQywxCustomerCluesRelation> getCustomerCluesBycluesIds(@Param("cluesIds") List<Long> cluesIds);

    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<QywxCustomerRelationVo> queryQywxCustomerRelationByexternalUserid(@Param("externalUseridList") List<String> externalUseridList);

}