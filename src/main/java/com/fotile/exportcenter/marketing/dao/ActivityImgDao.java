package com.fotile.exportcenter.marketing.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.exportcenter.marketing.pojo.dto.LogPictureDto;
import com.fotile.exportcenter.marketing.pojo.entity.ActivityPictureMarketing;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityImgDao extends BaseMapper<ActivityPictureMarketing> {

    List<LogPictureDto> selectUrlByIds(@Param("ids") List<Long> id, @Param("sourceTableName") String sourceTableName, @Param("pictureType")String pictureType);

}
