package com.fotile.exportcenter.marketing.service;


import com.alibaba.fastjson.JSONObject;
import com.fotile.exportcenter.client.CompanyClient;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.marketing.dao.TAttributeLevelConfigMapper;
import com.fotile.exportcenter.marketing.mq.CluesExportChannel;
import com.fotile.exportcenter.marketing.pojo.bo.FollowEfficiencyBO;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByAreaIdsDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByIdOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import com.fotile.exportcenter.util.TimeUtils;
import com.fotile.framework.data.auth.dataAuthor.pojo.ChannelAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.CompanyAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
@Slf4j
public class ClueExportService<UserAuthorUtils> {

    @Autowired
    UserAuthorConfig userAuthorConfig;
    @Autowired
    private CompanyClient companyClient;
    @Autowired
    private DataClientService dataClientService;
    @Autowired
    private TAttributeLevelConfigMapper attributeLevelConfigMapper;

    @Autowired
    private MarketingExportService marketingExportService;

    @Resource(name = CluesExportChannel.CLUES_EXPORT_PRODUCES_OUTPUT)
    private MessageChannel cluesExportProduces;



    private List<Long> getCompanyByAreas(List<Long> area) {
        List<Long> companys = new ArrayList<>();
        FindCompanyByAreaIdsDto findCompanyByAreaIdsDto = new FindCompanyByAreaIdsDto();
        findCompanyByAreaIdsDto.setAreaIds(area);
        List<FindCompanyByIdOutDto> findCompanyByIdOutDtos = companyClient.findCompanyByAreaIds(findCompanyByAreaIdsDto).getData();
        companys = findCompanyByIdOutDtos.stream().map(s -> s.getOrgId()).collect(Collectors.toList());
        return companys;
    }

    /**
     * 设置留资距今时间时长的值
     */
    private void setCluesDuration(QueryUserCluseInDto inDto) {
        Date durationStartTime = null;
        Date durationEndTime = null;
        if (inDto.getWithinType() == 1) {
            //24小时以内
            durationStartTime = getOtherDayBeforeNowDate(-1);
        }
        if (inDto.getWithinType() == 2) {
            durationStartTime = getOtherDayBeforeNowDate(-1);
            durationEndTime = getOtherDayBeforeNowDate(-2);
        }
        if (inDto.getWithinType() == 3) {
            durationEndTime = getOtherDayBeforeNowDate(-2);
        }
        inDto.setDurationStartTime(durationStartTime);
        inDto.setDurationEndTime(durationEndTime);
    }

    private void setCluesDuetimeByFlag(QueryUserCluseInDto inDto) {
        inDto.setDueTime(getOtherDayBeforeNowDate(-30));
    }

    /**
     * 某一天前
     *
     * @return
     */
    private Date getOtherDayBeforeNowDate(Integer amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, amount);
        return cal.getTime();
    }


    public int newQuerySalesLeadsListForExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }

        try {
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("101", userAuthor.getUserId(), userAuthor.getUsername(), inDto.getFileName(), inDto.getSize(), JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }

    public int newQuerySalesLeadsPhoneLogForExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }

        try {
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("1401", userAuthor.getUserId(), userAuthor.getUsername(), inDto.getFileName(), inDto.getSize(), JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }

    public ExportTaskRecord insertTask(String type,String userId,String userName, String taskName,Integer total,String param){
        ExportTaskRecord exportTaskRecord = new ExportTaskRecord();
        exportTaskRecord.setType(type);
        exportTaskRecord.setOperatorId(userId);
        exportTaskRecord.setOperatorName(userName);
        exportTaskRecord.setTaskName(taskName);//先写死
        exportTaskRecord.setTotalCount(total);//先写死
        exportTaskRecord.setParamJson(param);
        exportTaskRecord.setStatus(0);
        exportTaskRecord = dataClientService.insertTask(exportTaskRecord).getData();
        return exportTaskRecord;
    }


    //线索详情导出部分字段信息
    public int newCluesPortionListForExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }
        try{
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("102",userAuthor.getUserId(),userAuthor.getUsername(),inDto.getFileName(),inDto.getSize(),JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }

    //线索评分详情导出部分字段信息
    public int newCluesScorePortionListForExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }
        try{
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("104",userAuthor.getUserId(),userAuthor.getUsername(),inDto.getFileName(),inDto.getSize(),JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }


    //查询销售线索日志列表
    public int newExportOperatorLogList(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }
        try {
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("103", userAuthor.getUserId(), userAuthor.getUsername(), inDto.getFileName(), inDto.getSize(), JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }

    //抽取代码成公共方法，目前仅日志导出使用，测试阶段
    private Integer getAuthor(QueryUserCluseInDto inDto, UserAuthor userAuthor) {
        //获取公司权限和渠道权限
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthor);
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)){
            inDto.setCompanyOrgId(-11L);
        }
        //获得门店标签对应的门店信息
//        List<String> stroeLabelOrgIds = new ArrayList<>();
//        if (StringUtils.isNotBlank(inDto.getStroeLabels())) {//门店标签
//            List<Long> storeOrgIdList = orgClient.findStoreByKeyWordWithStoreScope(inDto.getStroeLabels(), true).getData();
//            if (storeOrgIdList == null || storeOrgIdList.size() == 0) {
//                return 3;
//            }
//            stroeLabelOrgIds = storeOrgIdList.stream().map(String::valueOf).collect(Collectors.toList());
//        }
        //客户所属门店权限
//        List<String> distributorStoreIds = new ArrayList<>();
//        if (inDto.getDistributorId() != null) {
//            List<DistributorEntity> distributorEntityList = orgClient.findDistributorById(inDto.getDistributorId()).getData();
//            if (distributorEntityList != null && distributorEntityList.size() > 0) {
//                distributorStoreIds = distributorEntityList.stream().map(d -> String.valueOf(d.getOrgId())).collect(Collectors.toList());
//            } else {
//                return 2;
//            }
//        }
        //判断门店业务发展经理是否为空
//        List<String> developStoreIds = new ArrayList<>();
//        if (inDto.getDevelopSalesmanId() != null) {
//            Result<List<QueryStoreByDevelopSalesmanIdVO>> result = orgClient.queryStoreByDevelopSalesmanId(inDto.getDevelopSalesmanId());
//            if (result != null && result.getData() != null && result.getData().size() > 0) {
//                developStoreIds = result.getData().stream().filter(s -> s.getOrgId() != null).map(s -> String.valueOf(s.getOrgId())).collect(Collectors.toList());
//            }else {
//                return 4;
//            }
//        }

//        stroeLabelOrgIds.addAll(distributorStoreIds);
//        stroeLabelOrgIds.addAll(developStoreIds);
//        HashSet storeSet = new HashSet(stroeLabelOrgIds);
//        stroeLabelOrgIds = new ArrayList<>(storeSet);
//        if(distributorStoreIds != null && distributorStoreIds.size() > 0){
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(stroeLabelOrgIds, distributorStoreIds);
//        }
//        if (developStoreIds != null && developStoreIds.size() > 0){
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(developStoreIds, stroeLabelOrgIds);
//        }
        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(storeIdList, stroeLabelOrgIds);
        }

//        if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0){
//            storeIdList = null;
//        }
//        List<String> queryStoreByConditionsOrgId = queryStoreByConditions(inDto.getDistributorId(), inDto.getStroeLabels(), inDto.getDevelopSalesmanId(), inDto.getStoreStatus(), inDto.getStoreLevels() );
//        if (queryStoreByConditionsOrgId != null && queryStoreByConditionsOrgId.size() == 0 ){
//           return 3;
//        }
//        inDto.setStroeLevelsOrgIds(queryStoreByConditionsOrgId);

//        if (StringUtils.isNotBlank(inDto.getStroeId())){
//            if ("-1".equals(inDto.getStroeId())) {
//                storeIdList = null;
//                queryStoreByConditionsOrgId = null;
//            } else {
//                if (storeIdList != null && storeIdList.size() > 0 && storeIdList.contains(inDto.getStroeId())) {
//                    storeIdList = null;
//                }
//                if (queryStoreByConditionsOrgId != null && queryStoreByConditionsOrgId.size() > 0 && queryStoreByConditionsOrgId.contains(inDto.getStroeId())) {
//                    queryStoreByConditionsOrgId = null;
//                }
//            }
//        }
//
//        inDto.setStroeLabelOrgIds(queryStoreByConditionsOrgId);
        inDto.setStoreIds(storeIdList);


        /**设置渠道id*/
        inDto.setChannelId(channels);

        /**获取分公司id，判断去除重复数据，若companyid不为空且在权限内，也就不需要in公司IDS了。*/
        List<Long> companys = new ArrayList<>();
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0){
                return 5;
            }
            if (companyIds != null && companyIds.size() >0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            }else {
                companyIds = companys;
            }
//            inDto.setCompanys(companys);
        }
        inDto.setCompanyIds(companyIds);


        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }
        if (("0").equals(inDto.getFlag())) {
            setCluesDuetimeByFlag(inDto);
        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS =  new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO  followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }

        List<String> serviceActionValue = marketingExportService.getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);
        //上门设计状态
//        if(StringUtils.isNotBlank(inDto.getIsComeDevise())){
//            if("1".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(0);
//            }else if ("2".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(1);
//            }else if ("3".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(3);
//            }else if ("4".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(4);
//            }else if ("5".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(0);
//            }else if ("6".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(1);
//            }
//        }

        return 0;
    }


    /**
     * 获取分公司用户权限
     */
    public Map getUserAuthor(UserAuthor userAuthor) {
        log.info("userAuthor" + userAuthor);
        Map map = new HashMap();
        if (userAuthor == null) {
            throw new BusinessException("获取登录信息失败");
        }
        if (userAuthor.getUserEntityCompanyRoleList() != null && userAuthor.getUserEntityCompanyRoleList().size() > 0) {
            List<CompanyAuthor> list = userAuthor.getUserEntityCompanyRoleList();
            List<Long> companyIds = new ArrayList<>();
            for (CompanyAuthor companyAuthor : list) {
                if (companyAuthor.getCompanyId().equals(-1L) || companyAuthor.getCompanyId() == null) {
                    companyIds = null;
                    break;
                }
                companyIds.add(companyAuthor.getOrgId());
            }
            map.put("companyIds", companyIds);
        } else {
            throw new BusinessException("分公司权限不足");
        }
        if (userAuthor.getUserEntityChannelRoleList() != null && userAuthor.getUserEntityChannelRoleList().size() > 0) {
            List<ChannelAuthor> channelAuthorList = userAuthor.getUserEntityChannelRoleList();
            List<String> channels = new ArrayList<>();
            for (ChannelAuthor channelAuthor : channelAuthorList) {
                if (channelAuthor.getChannelId() == null || channelAuthor.getChannelId().equals(-1L)) {
                    channels = null;
                    break;
                }
                channels.add(channelAuthor.getCode());
            }
            map.put("channelIds", channels);
        } else {
            throw new BusinessException("渠道权限不足");
        }

        //因为queryUserAuthor方法里面没有返回useCompanyAuthor的值，所以导致需要再调一次
        UserAuthor author = userAuthorConfig.queryUserEntityInfoByUserId();
        //不用修改
        if (author.getUseCompanyAuthor() != null && author.getUseCompanyAuthor() == 1) {
            map.put("storeIds", null);
        } else if (userAuthor.getUserEntityStoreRoleList() != null && userAuthor.getUserEntityStoreRoleList().size() > 0) {
            List<StoreAuthor> storeAuthors = userAuthor.getUserEntityStoreRoleList();
            List<Long> storeIds = new ArrayList<>();
            for (StoreAuthor storeAuthor : storeAuthors) {
                if (storeAuthor.getStoreId() == null || storeAuthor.getStoreId().equals(-1L)) {
                    storeIds = null;
                    break;
                }
                storeIds.add(storeAuthor.getOrgId());
            }
            map.put("storeIds", storeIds);
        } else {
            throw new BusinessException("门店权限不足");
        }
        return map;
    }


    /**线索参与活动导出*/
    public int newCluesActivityForExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();

        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }
        try{
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("105",userAuthor.getUserId(),userAuthor.getUsername(),inDto.getFileName(),inDto.getSize(),JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }


    public int newCluesFollowServiceExport(QueryUserCluseInDto inDto) {
        //获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        inDto.setFlag("");
        Integer x = getAuthor(inDto,userAuthor);
        if (x > 0){
            return x;
        }
        try{
            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = insertTask("106",userAuthor.getUserId(),userAuthor.getUsername(),inDto.getFileName(),inDto.getSize(),JsonUtils.toJson(inDto));
            if ("1".equals(exportTaskRecord.getExt1())) {
                return 5;
            }
            String content = JSONObject.toJSON(exportTaskRecord).toString();
            cluesExportProduces.send(MessageBuilder.withPayload(content).build());
            return 0;
        }catch (Exception e) {
            return 1;
        }
    }


}
