package com.fotile.exportcenter.marketing.service;

import com.fotile.exportcenter.cmscenter.scheme.pojo.dto.CluesCustomerInfoForSchemeDTO;
import com.fotile.exportcenter.marketing.dao.UserCluesDao;
import com.fotile.exportcenter.marketing.pojo.entity.UserClues;
import com.fotile.exportcenter.util.ReflectUtil;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class SalesLeadsService {
    @Autowired
    private UserCluesDao userCluesDao;

    public List<CluesCustomerInfoForSchemeDTO> queryCluesCustomerIndoByCluesIdsOpen(List<Long> cluesIds) {
        List<CluesCustomerInfoForSchemeDTO> customerInfoForSchemeDTOList = new ArrayList<>();
        List<UserClues> userCluesList = userCluesDao.selectBatchIds(cluesIds);
        if (userCluesList != null && userCluesList.size() > 0) {
            for (UserClues userClues : userCluesList) {
                CluesCustomerInfoForSchemeDTO customerInfoForSchemeDTO = new CluesCustomerInfoForSchemeDTO();
                BeanUtils.copyProperties(userClues, customerInfoForSchemeDTO);
                customerInfoForSchemeDTOList.add(customerInfoForSchemeDTO);
            }
        }
        for(CluesCustomerInfoForSchemeDTO customerInfoForSchemeDTO : customerInfoForSchemeDTOList){
            if(StringUtils.isNotBlank(customerInfoForSchemeDTO.getCustomerName())){
                if (customerInfoForSchemeDTO.getCustomerName().length() == 1) {
                    customerInfoForSchemeDTO.setCustomerName(customerInfoForSchemeDTO.getCustomerName() + "***" + customerInfoForSchemeDTO.getCustomerName());
                } else {
                    String firstName = customerInfoForSchemeDTO.getCustomerName().substring(0, 1);
                    String lastName = customerInfoForSchemeDTO.getCustomerName().substring(customerInfoForSchemeDTO.getCustomerName().length() - 1);
                    customerInfoForSchemeDTO.setCustomerName(firstName + "***" + lastName);
                }
            }
            if(StringUtils.isNotBlank(customerInfoForSchemeDTO.getCustomerPhone())){
                customerInfoForSchemeDTO.setCustomerPhone(ReflectUtil.replace(customerInfoForSchemeDTO.getCustomerPhone()));
            }
        }
        return customerInfoForSchemeDTOList;
    }


}
