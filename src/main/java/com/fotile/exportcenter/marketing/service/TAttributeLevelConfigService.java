package com.fotile.exportcenter.marketing.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.exportcenter.marketing.constant.CategoryCodeEnum;
import com.fotile.exportcenter.marketing.dao.TAttributeLevelConfigMapper;
import com.fotile.exportcenter.marketing.mapper.AttributeLevelConfigMapper;
import com.fotile.exportcenter.marketing.pojo.dto.FollowServerDTO;
import com.fotile.exportcenter.marketing.pojo.dto.SelectAttributeLevelInDto;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOneTweOutDto;
import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.vo.FollowServerFiledIdVO;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TAttributeLevelConfigService extends ServiceImpl<TAttributeLevelConfigMapper, TAttributeLevelConfig> {


    public int updateBatch(List<TAttributeLevelConfig> list) {
        return baseMapper.updateBatch(list);
    }

//    public int updateBatchSelective(List<TAttributeLevelConfig> list) {
//        return baseMapper.updateBatchSelective(list);
//    }

    public int batchInsert(List<TAttributeLevelConfig> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(TAttributeLevelConfig record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(TAttributeLevelConfig record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public TAttributeLevelConfig selectByPrimaryKey(Long id) {
        return baseMapper.selectById(id);
    }

    public List<SelectAllByFiledIdVO> selectAllByFiledId(String filedId) {
        QueryWrapper<TAttributeLevelConfig> queryWrapper =
                new QueryWrapper<TAttributeLevelConfig>().eq("field_id", filedId).eq("is_deleted", 0).orderByAsc("attribute_sort");
        List<TAttributeLevelConfig> tAttributeLevelConfigs = baseMapper.selectList(queryWrapper);
        return AttributeLevelConfigMapper.INSTANCE.from(tAttributeLevelConfigs);
    }

    public List<SelectAllByFiledIdVO> findByFiledIds(List<String> filedIds){
        return baseMapper.findByFiledIds(filedIds);
    }

    public List<TAttributeLevelConfig> selectListByFieldIdAndLevel(String fieldId , Integer level,String parentAttributeId){
        return baseMapper.selectListByFieldIdAndLevel(fieldId,level,parentAttributeId);
    }

    public TAttributeLevelConfig selectListByFieldIdAndAttributeAndLevel(String fieldId , String attributeId,Integer level){
        return baseMapper.selectListByFieldIdAndAttributeAndLevel(fieldId,attributeId,level);
    }

    public SelectAllByFiledIdVO selectAllByFiledIdAndAttributeId(SelectAttributeLevelInDto inDto){
        return baseMapper.selectAllByFiledIdAndAttributeId(inDto);
    }

    public SelectOneTweOutDto selectOneTweConfigByFiledIdAndAttributeId(String fieldId , String attributeId){
        return baseMapper.selectOneTweConfigByFiledIdAndAttributeId(fieldId,attributeId);
    }

    public List<FollowServerFiledIdVO> getFollowServerList() {
        List<FollowServerFiledIdVO> returnList = new ArrayList<>();
        FollowServerFiledIdVO filedIdVO = new FollowServerFiledIdVO();
        List<FollowServerDTO> list = baseMapper.getFollowServerList();
        if(CollectionUtils.isNotEmpty(list)){
            for(FollowServerDTO serverDTO : list){
                filedIdVO = new FollowServerFiledIdVO();
                filedIdVO.setAttributeId(serverDTO.getAttributeId());
                filedIdVO.setAttributeValue(CategoryCodeEnum.getCategory(serverDTO.getCategoryCode()) + "/" + serverDTO.getParentAttributeValue() + "/" + serverDTO.getAttributeValue());
                returnList.add(filedIdVO);
            }
        }
        return returnList;
    }
}
