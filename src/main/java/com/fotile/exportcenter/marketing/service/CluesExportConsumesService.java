package com.fotile.exportcenter.marketing.service;

import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.marketing.factory.CluesExportContext;
import com.fotile.exportcenter.marketing.mq.CluesExportChannel;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.framework.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CluesExportConsumesService {

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    CluesExportContext cluesExportContext;

    @StreamListener(CluesExportChannel.CLUES_EXPORT_CONSUMES_INPUT)
    public void sendPushAndMessageAndSms(Message<ExportTaskRecord> message) {

        ExportTaskRecord exportTaskRecord = message.getPayload();
        if(exportTaskRecord == null || exportTaskRecord.getId() == null){
            return;
        }
//        ExportTaskRecord newBean = dataClientService.getTaskById(exportTaskRecord.getId()).getData();
//        if(newBean.getStatus() != 0){
//            return;
//        }
        //调用开始任务接口
        Integer result = dataClientService.startTask(exportTaskRecord).getData();
        if(result != 1){
            //不等于1，则说明任务状态不是未进行，直接return
            return;
        }
        //调用查询任务接口，判断状态是否可以进行下载
        try {
            //根据类型调用不同的方法生产不同的数据
            cluesExportContext.getCluesExportImpl(exportTaskRecord.getType()).exportFile(exportTaskRecord);
        } catch (Exception e) {
            log.error("错误数据" + exportTaskRecord);
            log.error(String.format("线索导出,原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason("error:"+e.getMessage());
            dataClientService.failureTask(exportTask);
        }
        return;
    }
}
