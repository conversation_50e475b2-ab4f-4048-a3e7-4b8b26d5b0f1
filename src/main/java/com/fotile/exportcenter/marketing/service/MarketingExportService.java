package com.fotile.exportcenter.marketing.service;


import com.fotile.exportcenter.client.CompanyClient;
import com.fotile.exportcenter.marketing.dao.OperatorLogDao;
import com.fotile.exportcenter.marketing.dao.TAttributeLevelConfigMapper;
import com.fotile.exportcenter.marketing.dao.UserCluesDao;
import com.fotile.exportcenter.marketing.pojo.bo.FollowEfficiencyBO;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByAreaIdsDto;
import com.fotile.exportcenter.marketing.pojo.dto.FindCompanyByIdOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo;
import com.fotile.exportcenter.util.TimeUtils;
import com.fotile.framework.data.auth.dataAuthor.pojo.ChannelAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.CompanyAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
@Slf4j
public class MarketingExportService {

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private CompanyClient companyClient;


    @Autowired
    private UserCluesDao userCluesDao;

    @Autowired
    private OperatorLogDao operatorLogDao;

    @Autowired
    private TAttributeLevelConfigMapper tAttributeLevelConfigMapper;





    //线索详情导出（精简版，评分共用接口）
    public Long querySalesLeadsListForExportCounts(QueryUserCluseInDto inDto) {
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
//        //获取用户信息
//        UserAuthor userAuthor = userAuthorUtils.getUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);

        //获取公司权限和渠道权限
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthorConfig.queryUserAuthor());
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }
        //获得门店标签对应的门店信息
        List<String> stroeLabelOrgIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }

        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {

            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }

        stroeLabelOrgIds = inDto.getStroeLevelsOrgIds();

        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        if (StringUtils.isNotBlank(inDto.getStroeId())) {
            if ("-1".equals(inDto.getStroeId())) {
                storeIdList = null;
                stroeLabelOrgIds = null;
            } else {
                if (storeIdList != null && storeIdList.size() > 0 && storeIdList.contains(inDto.getStroeId())) {
                    storeIdList = null;
                }
                if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0 && stroeLabelOrgIds.contains(inDto.getStroeId())) {
                    stroeLabelOrgIds = null;
                }
            }
        }

        inDto.setStroeLabelOrgIds(stroeLabelOrgIds);

        inDto.setStoreIds(storeIdList);

        inDto.setChannels(channels);

        List<Long> companys = new ArrayList<>();

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)) {
            inDto.setCompanyOrgId(-11L);
        }
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0) {
                return 0L;
            }
            if (companyIds != null && companyIds.size() > 0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            } else {
                companyIds = companys;
            }
        }
        inDto.setCompanyIds(companyIds);

        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }
        if (("0").equals(inDto.getFlag())) {
            setCluesDuetimeByFlag(inDto);
        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS = new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }
        inDto.setAesKey(MybatisMateConfig.getPassword());
        List<String> serviceActionValue = getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);

        Long clueCount = userCluesDao.querySalesLeadsListForExportCounts(inDto);
        return clueCount;
    }


    /**
     * 获取分公司用户权限
     */
    public Map getUserAuthor(UserAuthor userAuthor) {
        log.info("userAuthor" + userAuthor);
        Map map = new HashMap();
        if (userAuthor == null) {
            throw new BusinessException("获取登录信息失败");
        }
        if (userAuthor.getUserEntityCompanyRoleList() != null && userAuthor.getUserEntityCompanyRoleList().size() > 0) {
            List<CompanyAuthor> list = userAuthor.getUserEntityCompanyRoleList();
            List<Long> companyIds = new ArrayList<>();
            for (CompanyAuthor companyAuthor : list) {
                if (companyAuthor.getCompanyId().equals(-1L) || companyAuthor.getCompanyId() == null) {
                    companyIds = null;
                    break;
                }
                companyIds.add(companyAuthor.getOrgId());
            }
            map.put("companyIds", companyIds);
        } else {
            throw new BusinessException("分公司权限不足");
        }
        if (userAuthor.getUserEntityChannelRoleList() != null && userAuthor.getUserEntityChannelRoleList().size() > 0) {
            List<ChannelAuthor> channelAuthorList = userAuthor.getUserEntityChannelRoleList();
            List<String> channels = new ArrayList<>();
            for (ChannelAuthor channelAuthor : channelAuthorList) {
                if (channelAuthor.getChannelId() == null || channelAuthor.getChannelId().equals(-1L)) {
                    channels = null;
                    break;
                }
                channels.add(channelAuthor.getCode());
            }
            map.put("channelIds", channels);
        } else {
            throw new BusinessException("渠道权限不足");
        }

        //因为queryUserAuthor方法里面没有返回useCompanyAuthor的值，所以导致需要再调一次
        //不用修改
        UserAuthor author = userAuthorConfig.queryUserEntityInfoByUserId();
        if (author.getUseCompanyAuthor() != null && author.getUseCompanyAuthor() == 1) {
            map.put("storeIds", null);
        } else if (userAuthor.getUserEntityStoreRoleList() != null && userAuthor.getUserEntityStoreRoleList().size() > 0) {
            List<StoreAuthor> storeAuthors = userAuthor.getUserEntityStoreRoleList();
            List<Long> storeIds = new ArrayList<>();
            for (StoreAuthor storeAuthor : storeAuthors) {
                if (storeAuthor.getStoreId() == null || storeAuthor.getStoreId().equals(-1L)) {
                    storeIds = null;
                    break;
                }
                storeIds.add(storeAuthor.getOrgId());
            }
            map.put("storeIds", storeIds);
        } else {
            throw new BusinessException("门店权限不足");
        }
        return map;
    }


    private List<Long> getCompanyByAreas(List<Long> area) {
        List<Long> companys = new ArrayList<>();
        FindCompanyByAreaIdsDto findCompanyByAreaIdsDto = new FindCompanyByAreaIdsDto();
        findCompanyByAreaIdsDto.setAreaIds(area);
        List<FindCompanyByIdOutDto> findCompanyByIdOutDtos = companyClient.findCompanyByAreaIds(findCompanyByAreaIdsDto).getData();
        companys = findCompanyByIdOutDtos.stream().map(s -> s.getOrgId()).collect(Collectors.toList());
        return companys;
    }

    private void setCluesDuetimeByFlag(QueryUserCluseInDto inDto) {
        inDto.setDueTime(getOtherDayBeforeNowDate(-30));
    }


    /**
     * 设置留资距今时间时长的值
     */
    private void setCluesDuration(QueryUserCluseInDto inDto) {
        Date durationStartTime = null;
        Date durationEndTime = null;
        if (inDto.getWithinType() == 1) {
            //24小时以内
            durationStartTime = getOtherDayBeforeNowDate(-1);
        }
        if (inDto.getWithinType() == 2) {
            durationStartTime = getOtherDayBeforeNowDate(-1);
            durationEndTime = getOtherDayBeforeNowDate(-2);
        }
        if (inDto.getWithinType() == 3) {
            durationEndTime = getOtherDayBeforeNowDate(-2);
        }
        inDto.setDurationStartTime(durationStartTime);
        inDto.setDurationEndTime(durationEndTime);
    }

    /**
     * 某一天前
     *
     * @return
     */
    private Date getOtherDayBeforeNowDate(Integer amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, amount);
        return cal.getTime();
    }


    //线索日志导出count
    public Long exportOperatorLogCount(QueryUserCluseInDto inDto) {
        //处理未跟进天数
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        //获取公司权限和渠道权限
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthorConfig.queryUserAuthor());
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }
        log.info("公司权限=" + companyIds + ";门店权限=" + storeIds + ";渠道权限=" + channels);
        //获得门店标签对应的门店信息
        List<String> stroeLabelOrgIds = new ArrayList<>();

        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }

        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
            storeIdList = null;
        }
        if (StringUtils.isNotBlank(inDto.getStroeId())) {
            if ("-1".equals(inDto.getStroeId())) {
                storeIdList = null;
                stroeLabelOrgIds = null;
            } else {
                if (storeIdList != null && storeIdList.size() > 0) {
                    if (storeIdList.contains(inDto.getStroeId())) {
                        storeIdList = null;
                    } else {
                        return 0L;
                    }
                }
                if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
                    if (stroeLabelOrgIds.contains(inDto.getStroeId())) {
                        stroeLabelOrgIds = null;
                    } else {
                        return 0L;
                    }
                }
            }
        }
        inDto.setStroeLabelOrgIds(stroeLabelOrgIds);
        inDto.setStoreIds(storeIdList);

        /**设置渠道id*/
        inDto.setChannelId(channels);

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)) {
            inDto.setCompanyOrgId(-11L);
        }
        /**获取分公司id，判断去除重复数据，若companyid不为空且在权限内，也就不需要in公司IDS了。*/
        List<Long> companys = new ArrayList<>();
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0) {
                return 0L;
            }
            if (companyIds != null && companyIds.size() > 0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            } else {
                companyIds = companys;
            }
//            inDto.setCompanys(companys);
        }
        inDto.setCompanyIds(companyIds);


        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }
        if (("0").equals(inDto.getFlag())) {
            setCluesDuetimeByFlag(inDto);
        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS = new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }
        inDto.setAesKey(MybatisMateConfig.getPassword());
        //上门设计状态
        List<String> serviceActionValue = getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);

        log.info("线索日志导出请求参数=" + inDto);
        Long clueCount = operatorLogDao.exportOperatorLogCount(inDto);
        return clueCount;
    }


    /**
     * 线索参与活动导出查询个数
     */
    public Long exportCluesActivityCount(QueryUserCluseInDto inDto) {
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthorConfig.queryUserAuthor());
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }
        log.info("公司权限=" + companyIds + ";门店权限=" + storeIds + ";渠道权限=" + channels);
        //获得门店标签对应的门店信息
        List<String> stroeLabelOrgIds = new ArrayList<>();

        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
        stroeLabelOrgIds = inDto.getStroeLevelsOrgIds();
        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
            storeIdList = null;
        }
        if (StringUtils.isNotBlank(inDto.getStroeId())) {
            if ("-1".equals(inDto.getStroeId())) {
                storeIdList = null;
                stroeLabelOrgIds = null;
            } else {
                if (storeIdList != null && storeIdList.size() > 0) {
                    if (storeIdList.contains(inDto.getStroeId())) {
                        storeIdList = null;
                    } else {
                        return 0L;
                    }
                }
                if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
                    if (stroeLabelOrgIds.contains(inDto.getStroeId())) {
                        stroeLabelOrgIds = null;
                    } else {
                        return 0L;
                    }
                }
            }
        }
        inDto.setStroeLabelOrgIds(stroeLabelOrgIds);
        inDto.setStoreIds(storeIdList);

        /**设置渠道id*/
        inDto.setChannelId(channels);

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)) {
            inDto.setCompanyOrgId(-11L);
        }
        /**获取分公司id，判断去除重复数据，若companyid不为空且在权限内，也就不需要in公司IDS了。*/
        List<Long> companys = new ArrayList<>();
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0) {
                return 0L;
            }
            if (companyIds != null && companyIds.size() > 0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            } else {
                companyIds = companys;
            }
//            inDto.setCompanys(companys);
        }
        inDto.setCompanyIds(companyIds);


        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }
        if (("0").equals(inDto.getFlag())) {
            setCluesDuetimeByFlag(inDto);
        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS = new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }
        inDto.setAesKey(MybatisMateConfig.getPassword());

        List<String> serviceActionValue = getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);

        //上门设计状态
//        if(StringUtils.isNotBlank(inDto.getIsComeDevise())){
//            if("1".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(0);
//            }else if ("2".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(1);
//            }else if ("3".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(3);
//            }else if ("4".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(4);
//            }else if ("5".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(0);
//            }else if ("6".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(1);
//            }
//        }

        log.info("线索参与活动导出查询个数参数=" + inDto);
        Long count = userCluesDao.exportCluesActivityCount(inDto);
        return count;
    }


    public Long exportPhoneLogCount(QueryUserCluseInDto inDto) {
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
//        //获取用户信息
//        UserAuthor userAuthor = userAuthorUtils.getUserAuthor();
//        //获取分公司权限
//        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
//        //获取渠道权限
//        List<String> channels = userAuthorUtils.getAuthorChannelCodes(userAuthor);
//        List<Long> storeIds = userAuthorUtils.getAuthorStoreIds(userAuthor);
        //获取公司权限和渠道权限
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthorConfig.queryUserAuthor());
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }
        log.info("公司权限=" + companyIds + ";门店权限=" + storeIds + ";渠道权限=" + channels);
        //获得门店标签对应的门店信息
        List<String> stroeLabelOrgIds = new ArrayList<>();
//        if (StringUtils.isNotBlank(inDto.getStroeLabels())) {//门店标签
//            List<Long> storeOrgIdList = orgClient.findStoreByKeyWordWithStoreScope(inDto.getStroeLabels(), true).getData();
//            if (storeOrgIdList == null || storeOrgIdList.size() == 0) {
//                return 0L;
//            }
//            stroeLabelOrgIds = storeOrgIdList.stream().map(String::valueOf).collect(Collectors.toList());
//        }
        //客户所属门店权限
//        List<String> distributorStoreIds = new ArrayList<>();
//        if (inDto.getDistributorId() != null) {
//            List<DistributorEntity> distributorEntityList = orgClient.findDistributorById(inDto.getDistributorId()).getData();
//            if (distributorEntityList != null && distributorEntityList.size() > 0) {
//                distributorStoreIds = distributorEntityList.stream().map(d -> String.valueOf(d.getOrgId())).collect(Collectors.toList());
//            } else {
//                return 0L;
//            }
//        }
        //判断门店业务发展经理是否为空
//        List<String> developStoreIds = new ArrayList<>();
//        if (inDto.getDevelopSalesmanId() != null) {
//            Result<List<QueryStoreByDevelopSalesmanIdVO>> result = orgClient.queryStoreByDevelopSalesmanId(inDto.getDevelopSalesmanId());
//            if (result != null && result.getData() != null && result.getData().size() > 0) {
//                developStoreIds = result.getData().stream().filter(s -> s.getOrgId() != null).map(s -> String.valueOf(s.getOrgId())).collect(Collectors.toList());
//            } else {
//                return 0L;
//            }
//        }
        //门店类别
//        List<String> queryStoreByConditionsOrgId = queryStoreByConditions(inDto.getDistributorId(), inDto.getStroeLabels(), inDto.getDevelopSalesmanId(), inDto.getStoreStatus(), inDto.getStoreLevels());
//        if (queryStoreByConditionsOrgId != null && queryStoreByConditionsOrgId.size() == 0) {
//        List<String> queryStoreByConditionsOrgId = queryStoreByConditions(inDto.getDistributorId(), inDto.getStroeLabels(), inDto.getDevelopSalesmanId(), inDto.getStoreStatus(), inDto.getStoreLevels() );
//        if (queryStoreByConditionsOrgId != null && queryStoreByConditionsOrgId.size() == 0 ){
//            return 0L;
//        }
//        inDto.setStroeLevelsOrgIds(queryStoreByConditionsOrgId);

//        List<String> queryStoreByConditionsOrgId = queryStoreByConditions(inDto.getDistributorId(), inDto.getStroeLabels(), inDto.getDevelopSalesmanId(), inDto.getStoreStatus(), inDto.getStoreLevels() );
//        if (queryStoreByConditionsOrgId != null && queryStoreByConditionsOrgId.size() == 0 ){
//            return 0L;
//        }
//        inDto.setStroeLevelsOrgIds(queryStoreByConditionsOrgId);

        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }
//        stroeLabelOrgIds = inDto.getStroeLevelsOrgIds();
//        if (distributorStoreIds != null && distributorStoreIds.size() > 0) {
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(stroeLabelOrgIds, distributorStoreIds);
//        }
//        if (developStoreIds != null && developStoreIds.size() > 0) {
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(developStoreIds, stroeLabelOrgIds);
//        }
        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
//            stroeLabelOrgIds = (List<String>) CollectionUtils.intersection(storeIdList, stroeLabelOrgIds);
        }

        if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
            storeIdList = null;
        }
        if (StringUtils.isNotBlank(inDto.getStroeId())) {
            if ("-1".equals(inDto.getStroeId())) {
                storeIdList = null;
                stroeLabelOrgIds = null;
            } else {
                if (storeIdList != null && storeIdList.size() > 0) {
                    if (storeIdList.contains(inDto.getStroeId())) {
                        storeIdList = null;
                    } else {
                        return 0L;
                    }
                }
                if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
                    if (stroeLabelOrgIds.contains(inDto.getStroeId())) {
                        stroeLabelOrgIds = null;
                    } else {
                        return 0L;
                    }
                }
            }
        }
        inDto.setStroeLabelOrgIds(stroeLabelOrgIds);
        inDto.setStoreIds(storeIdList);

        /**设置渠道id*/
        inDto.setChannelId(channels);

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)) {
            inDto.setCompanyOrgId(-11L);
        }
        /**获取分公司id，判断去除重复数据，若companyid不为空且在权限内，也就不需要in公司IDS了。*/
        List<Long> companys = new ArrayList<>();
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0) {
                return 0L;
            }
            if (companyIds != null && companyIds.size() > 0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            } else {
                companyIds = companys;
            }
//            inDto.setCompanys(companys);
        }
        inDto.setCompanyIds(companyIds);


        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS = new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }
        inDto.setAesKey(MybatisMateConfig.getPassword());

        List<String> serviceActionValue = getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);

        //上门设计状态
//        if(StringUtils.isNotBlank(inDto.getIsComeDevise())){
//            if("1".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(0);
//            }else if ("2".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("0");
//                inDto.setComeDeviseStatus(1);
//            }else if ("3".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(3);
//            }else if ("4".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(4);
//            }else if ("5".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(0);
//            }else if ("6".equals(inDto.getIsComeDevise())){
//                inDto.setIsComeDevise("1");
//                inDto.setComeDeviseStatus(1);
//            }
//        }

        log.info("线索日志导出请求参数=" + inDto);
        Long clueCount = operatorLogDao.exportPhoneLogCount(inDto);
        return clueCount;
    }


    /**
     * issue:44617-22513-【场景化】-上门设计，新增营销中台上门设计列表-改成线索列表新增上门设计等相关跟进查看--0424
     * 此处为修改服务动作的方法
     */
    public List<String> getServiceActionValue(List<String> firstLevelList,List<String> secondLevelList){
        if(CollectionUtils.isEmpty(firstLevelList) && CollectionUtils.isEmpty(secondLevelList)){
            return new ArrayList<>();
        }
        if(CollectionUtils.isNotEmpty(secondLevelList)){
            return secondLevelList;
        }
        if(CollectionUtils.isEmpty(secondLevelList) && CollectionUtils.isNotEmpty(firstLevelList)){
            //查询一级下全部二级数据
            List<TAttributeLevelConfig> attributeLevelConfigList =  tAttributeLevelConfigMapper.selectListByFieldIdAndLevelAndParentIds("follow_server",2,firstLevelList);
            if(CollectionUtils.isNotEmpty(attributeLevelConfigList)){
                List<String>  serviceActionValue = attributeLevelConfigList.stream().map(t -> t.getAttributeId()).collect(Collectors.toList());
                return serviceActionValue;
            }else {
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }


    //线索跟进动作导出个数count
    public Long newCluesFollowServiceExportCount(QueryUserCluseInDto inDto) {
        if (inDto.getStartUnfollowDays() != null) {
            inDto.setStartUnfollowTime(  LocalDateTime.now().minusDays(inDto.getStartUnfollowDays()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (inDto.getEndUnfollowDays() != null) {
            inDto.setEndUnfollowTime(  LocalDateTime.now().minusDays(inDto.getEndUnfollowDays()).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        //处理数据
        if (CollectionUtils.isNotEmpty(inDto.getVillageIds())) {
            inDto.setVillageFlag(inDto.getVillageIds().contains(0L));
            if (inDto.getVillageIds().contains(0L)) {
                inDto.getVillageIds().add(-1L);
            }
        }
        if (CollectionUtils.isNotEmpty(inDto.getVisitStatusCodes())) {
            inDto.setVisitStatusFlag(inDto.getVisitStatusCodes().contains("0"));
        }
        //获取公司权限和渠道权限
        List<Long> companyIds = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Long> storeIds = new ArrayList<>();
        Map map = getUserAuthor(userAuthorConfig.queryUserAuthor());
        if (map.get("companyIds") != null && map.get("companyIds") != "") {
            companyIds = (List<Long>) map.get("companyIds");
        }
        if (map.get("channelIds") != null && map.get("channelIds") != "") {
            channels = (List<String>) map.get("channelIds");
        }
        if (map.get("storeIds") != null && map.get("storeIds") != "") {
            storeIds = (List<Long>) map.get("storeIds");
        }
        log.info("公司权限=" + companyIds + ";门店权限=" + storeIds + ";渠道权限=" + channels);
        //获得门店标签对应的门店信息
        List<String> stroeLabelOrgIds = new ArrayList<>();

        if (StringUtils.isNotEmpty(inDto.getCustomerPhone())) {
            inDto.setCustomerPhone(MybatisMateConfig.encrypt(inDto.getCustomerPhone()));
        }
        if (StringUtils.isNotEmpty(inDto.getStroeLabels())) {
            inDto.setKeyWordSet(Arrays.stream(inDto.getStroeLabels().split(",")).collect(Collectors.toList()));
        }

        List<String> storeIdList = null;
        if (storeIds != null && storeIds.size() > 0) {
            storeIdList = storeIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
            storeIdList = null;
        }
        if (StringUtils.isNotBlank(inDto.getStroeId())) {
            if ("-1".equals(inDto.getStroeId())) {
                storeIdList = null;
                stroeLabelOrgIds = null;
            } else {
                if (storeIdList != null && storeIdList.size() > 0) {
                    if (storeIdList.contains(inDto.getStroeId())) {
                        storeIdList = null;
                    } else {
                        return 0L;
                    }
                }
                if (stroeLabelOrgIds != null && stroeLabelOrgIds.size() > 0) {
                    if (stroeLabelOrgIds.contains(inDto.getStroeId())) {
                        stroeLabelOrgIds = null;
                    } else {
                        return 0L;
                    }
                }
            }
        }
        inDto.setStroeLabelOrgIds(stroeLabelOrgIds);
        inDto.setStoreIds(storeIdList);

        /**设置渠道id*/
        inDto.setChannelId(channels);

        if (StringUtils.isNotBlank(inDto.getStoreTypeCode())) {
            inDto.setStoreTypeCodeList(Arrays.asList(inDto.getStoreTypeCode().split(",")));
        }
        if (inDto.getCompanyId() != null && inDto.getCompanyId().contains(-11L)) {
            inDto.setCompanyOrgId(-11L);
        }
        /**获取分公司id，判断去除重复数据，若companyid不为空且在权限内，也就不需要in公司IDS了。*/
        List<Long> companys = new ArrayList<>();
        if (inDto.getArea() != null && inDto.getArea().size() > 0) {
            companys = getCompanyByAreas(inDto.getArea());
            if (companys == null || companys.size() == 0) {
                return 0L;
            }
            if (companyIds != null && companyIds.size() > 0) {
                companyIds = (List<Long>) CollectionUtils.intersection(companyIds, companys);
            } else {
                companyIds = companys;
            }
//            inDto.setCompanys(companys);
        }
        inDto.setCompanyIds(companyIds);


        if (inDto.getWithinType() != null) {
            setCluesDuration(inDto);
        }
//        if (("0").equals(inDto.getFlag())) {
//            setCluesDuetimeByFlag(inDto);
//        }

        //处理跟进效率时间
        //跟进效率 1.30分钟内 2.30分钟-1个小时内 3.1个小时-6小时内 4.24小时内 5.1天-2天内 6.2天-3天内 7.3天-7天内 8.7天-30天内 9.30天以上
        if (!CollectionUtils.isEmpty(inDto.getFollowEfficiencyCategory())) {
            List<Integer> followEfficiencyCategorys = inDto.getFollowEfficiencyCategory();
            followEfficiencyCategorys = followEfficiencyCategorys.stream().filter(s -> s != null).collect(Collectors.toList());
            List<FollowEfficiencyBO> followEfficiencyBOS = new ArrayList<>();
            followEfficiencyCategorys.forEach(followEfficiencyCategory -> {
                FollowEfficiencyBO followEfficiencyBO = new FollowEfficiencyBO();
                if (followEfficiencyCategory.equals(1)) {
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.minutesConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(2)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.minutesConversionSeconds(30));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(1));
                } else if (followEfficiencyCategory.equals(3)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(6));
                } else if (followEfficiencyCategory.equals(4)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.hoursConversionSeconds(6));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.hoursConversionSeconds(24));
                } else if (followEfficiencyCategory.equals(5)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(1));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(2));
                } else if (followEfficiencyCategory.equals(6)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(2));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(3));
                } else if (followEfficiencyCategory.equals(7)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(3));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(7));
                } else if (followEfficiencyCategory.equals(8)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(7));
                    followEfficiencyBO.setFollowEfficeEndDate(TimeUtils.daysConversionSeconds(30));
                } else if (followEfficiencyCategory.equals(9)) {
                    followEfficiencyBO.setFollowEfficeStartDate(TimeUtils.daysConversionSeconds(30));
                }
                followEfficiencyBOS.add(followEfficiencyBO);
            });
            inDto.setFollowEfficiencyBO(followEfficiencyBOS);
        }
        inDto.setAesKey(MybatisMateConfig.getPassword());
        //上门设计状态
        List<String> serviceActionValue = getServiceActionValue(inDto.getFullServerCodes(),inDto.getServerCodes());
        inDto.setServerCodes(serviceActionValue);

        log.info("线索跟进动作导出导出请求参数=" + inDto);
//        Long clueCount = userCluesDao.newCluesFollowServiceExportCount(inDto);
        Long clueCount = userCluesDao.newExportCluesId(inDto);
        return clueCount;
    }

}
