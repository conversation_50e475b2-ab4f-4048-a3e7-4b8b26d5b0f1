package com.fotile.exportcenter.marketing.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.exportcenter.marketing.dao.TCluesLabelMappingMapper;
import com.fotile.exportcenter.marketing.pojo.entity.TCluesLabelMappingEntity;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class TCluesLabelMappingService extends ServiceImpl<TCluesLabelMappingMapper, TCluesLabelMappingEntity> {

    
    public int updateBatch(List<TCluesLabelMappingEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TCluesLabelMappingEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TCluesLabelMappingEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TCluesLabelMappingEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TCluesLabelMappingEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public List<TCluesLabelMappingEntity> selectListByCluesId(Long cluesId){
        return baseMapper.selectListByCluesId(cluesId);
    }


}
