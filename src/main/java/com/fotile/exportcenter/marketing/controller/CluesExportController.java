package com.fotile.exportcenter.marketing.controller;

import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.service.ClueExportService;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 线索异步导出接口
 * <AUTHOR>
 */
@RequestMapping("/api/cluesExport")
@RestController
@Slf4j
public class CluesExportController extends BaseController {

    @Autowired
    UserAuthorConfig userAuthorConfig;

    @Autowired
    private ClueExportService clueExportService;

    /***
     * 线索详情导出--最新接口
     * @param
     * @return
     */
    @RequestMapping(value = "/newQuerySalesLeadsListForExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newQuerySalesLeadsListForExport(@RequestBody @Valid QueryUserCluseInDto inDto) {
        //inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newQuerySalesLeadsListForExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }

    /**
     * 拨打电话日志导出表
     * @param inDto
     * @return
     */
    @RequestMapping(value = "/newQuerySalesLeadsPhoneLogForExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newQuerySalesLeadsPhoneLogForExport(@RequestBody @Valid QueryUserCluseInDto inDto) {
        //inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newQuerySalesLeadsPhoneLogForExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }

    /**
     * 线索详情导出部分字段信息
     */
    @RequestMapping(value = "/newCluesPortionListForExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> newCluesPortionListForExport(@RequestBody @Valid QueryUserCluseInDto inDto) {
//        inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newCluesPortionListForExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }

    /**
     * 线索评分详情导出部分字段信息
     */
    @RequestMapping(value = "/newCluesScorePortionListForExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> newCluesScorePortionListForExport(@RequestBody @Valid QueryUserCluseInDto inDto) {
//        inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newCluesScorePortionListForExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }

    /**
     * 查询销售线索日志列表
     */
    @RequestMapping(value = "/newExportOperatorLogList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newExportOperatorLogList(@RequestBody @Valid QueryUserCluseInDto inDto) {
//        inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newExportOperatorLogList(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success( "导出任务已创建，请勿重复创建", null);
        }
    }


    /**
     * 线索参与活动导出--最新接口
     */
    @RequestMapping(value = "/newCluesActivityForExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newCluesActivityForExport(@RequestBody @Valid QueryUserCluseInDto inDto) {
        //inDto.setUserAuthor(userAuthorConfig.queryUserAuthor());
        int i = clueExportService.newCluesActivityForExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }



    /**
     * 线索跟进动作导出--最新接口
     */
    @RequestMapping(value = "/newCluesFollowServiceExport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newCluesFollowServiceExport(@RequestBody QueryUserCluseInDto inDto) {
        int i = clueExportService.newCluesFollowServiceExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else if (i == 2){
            return failure(1, "无门店所属客户关系数据，无法导出", null);
        }else if (i == 3){
            return failure(1, "无门店标签关系数据，无法导出", null);
        }else if (i == 4){
            return failure(1, "无门店业务发展经理关系数据，无法导出", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }


}
