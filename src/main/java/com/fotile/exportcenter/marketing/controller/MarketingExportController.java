package com.fotile.exportcenter.marketing.controller;

import com.fotile.exportcenter.marketing.pojo.dto.QueryUserCluseInDto;
import com.fotile.exportcenter.marketing.service.MarketingExportService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RequestMapping("/api/marketingExport")
@RestController
@Slf4j
public class MarketingExportController extends BaseController {


    @Autowired
    private MarketingExportService marketingExportService;



    /***
     * 线索详情导出count
     * @param
     * @return
     */
    @RequestMapping(value = "/querySalesLeadsListForExportCounts", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> querySalesLeadsListForExportCounts(@RequestBody @Valid QueryUserCluseInDto inDto) {
        return success(marketingExportService.querySalesLeadsListForExportCounts(inDto));
    }


    /**
     * 线索日志导出查询日志个数
     */
    @RequestMapping(value = "/exportOperatorLogCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> exportOperatorLogCount(@RequestBody QueryUserCluseInDto inDto) {
        return success(marketingExportService.exportOperatorLogCount(inDto));
    }


    /**
     * 线索参与活动导出查询个数
     */
    @RequestMapping(value = "/exportCluesActivityCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> exportCluesActivityCount(@RequestBody QueryUserCluseInDto inDto) {
        return success(marketingExportService.exportCluesActivityCount(inDto));
    }

    /**
     * 拨打电话日志导出表 数量
     */
    @RequestMapping(value = "/exportPhoneLogCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Long> exportPhoneLogCount(@RequestBody QueryUserCluseInDto inDto) {
        return success(marketingExportService.exportPhoneLogCount(inDto));
    }

    /**
     * 线索跟进动作导出个数
     */
    @RequestMapping(value = "/newCluesFollowServiceExportCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> newCluesFollowServiceExportCount(@RequestBody QueryUserCluseInDto inDto) {
        return success(marketingExportService.newCluesFollowServiceExportCount(inDto));
    }
}
