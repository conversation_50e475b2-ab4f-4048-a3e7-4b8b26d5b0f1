package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesPhoneLogListOutDTO;
import com.fotile.exportcenter.marketing.pojo.dto.OperatorLogListOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface CluesLogMapper {
    CluesLogMapper INSTANCE = Mappers.getMapper( CluesLogMapper.class );
    List<OperatorLogListOutDto> operatorLogToCluesLogOutDto(List<SelectOperatorLogListOutDto> operatorLogListOutDtos);

    List<CluesPhoneLogListOutDTO> cluesPhoneLogListOutDTO(List<SelectOperatorLogListOutDto> operatorLogListOutDtos);
}
