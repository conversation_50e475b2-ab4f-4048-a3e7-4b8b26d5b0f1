package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesScorePortionExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface CluesScorePortionMapper {
    CluesScorePortionMapper INSTANCE = Mappers.getMapper( CluesScorePortionMapper.class );
    List<CluesScorePortionExportOutDto> newExportToCluesScorePortionOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos);
}
