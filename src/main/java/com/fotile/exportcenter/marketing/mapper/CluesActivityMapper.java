package com.fotile.exportcenter.marketing.mapper;


import com.fotile.exportcenter.marketing.pojo.dto.CluesActivityExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.CluesActivityOutDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface CluesActivityMapper {
    CluesActivityMapper INSTANCE = Mappers.getMapper( CluesActivityMapper.class );
    List<CluesActivityExportOutDto> cluesActivityToExport(List<CluesActivityOutDto> cluesActivityOutDtos);

}
