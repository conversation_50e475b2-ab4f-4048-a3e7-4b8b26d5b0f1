package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesDetailExportOutDtos;
import com.fotile.exportcenter.marketing.pojo.dto.CluesDetailSensitiveExportOutDtos;
import com.fotile.exportcenter.marketing.pojo.dto.NewCluesFollowServiceDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;

import com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CluesDetailMapper {
    CluesDetailMapper INSTANCE = Mappers.getMapper(CluesDetailMapper.class);

    List<CluesDetailExportOutDtos> newExportToCluesDetailOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos);

    /**
     * 线索脱敏转换
     */
    List<CluesDetailSensitiveExportOutDtos> newExportToCluesDetailSensitiveOutDto(List<NewExportUserClusesOutDto> clues);


    List<NewCluesFollowServiceDto> newExportToCluesFollowServiceOutDto(List<NewCluesFollowServiceVo> newExportUserClusesOutDto);
}
