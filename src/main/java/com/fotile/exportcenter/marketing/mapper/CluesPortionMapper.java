package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesPortionExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface CluesPortionMapper {
    CluesPortionMapper INSTANCE = Mappers.getMapper( CluesPortionMapper.class );
    List<CluesPortionExportOutDto> newExportToCluesPortionOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos);
}
