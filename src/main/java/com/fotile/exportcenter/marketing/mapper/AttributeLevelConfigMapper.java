package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface AttributeLevelConfigMapper {
    AttributeLevelConfigMapper INSTANCE = Mappers.getMapper( AttributeLevelConfigMapper.class );
    SelectAllByFiledIdVO from(TAttributeLevelConfig tAttributeLevelConfig);
    List<SelectAllByFiledIdVO> from(List<TAttributeLevelConfig> tAttributeLevelConfigs);

}
