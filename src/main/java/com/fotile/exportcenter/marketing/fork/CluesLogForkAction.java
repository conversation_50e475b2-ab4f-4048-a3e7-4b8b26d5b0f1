package com.fotile.exportcenter.marketing.fork;

import com.fotile.exportcenter.marketing.dao.OperatorLogDao;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.RecursiveTask;

public class CluesLogForkAction extends RecursiveTask<List<SelectOperatorLogListOutDto>> {


    private static final Integer size = 2000;
    private Integer start;
    private Integer end;
    private List<Long> cluesIds;
    private OperatorLogDao operatorLogDao;
    public CluesLogForkAction(Integer start,Integer end,OperatorLogDao operatorLogDao,List<Long> clueIds) {
        this.start = start;
        this.operatorLogDao = operatorLogDao;
        this.cluesIds = clueIds;
        this.end = end;
    }
    @Override
    protected List<SelectOperatorLogListOutDto> compute() {
        if (end -  start > size) {
            int middle = (start + end) / 2;
            CluesLogForkAction left = new CluesLogForkAction(start ,end,operatorLogDao,cluesIds);
            CluesLogForkAction right = new CluesLogForkAction(middle,end,operatorLogDao,cluesIds);
            left.fork();
            right.fork();
            List<SelectOperatorLogListOutDto> join = left.join();
            List<SelectOperatorLogListOutDto> join1 = right.join();
            join.addAll(join1);
            return join;
        } else {
            return getOpertorLogAndFollowupList(cluesIds.subList(start, end));
        }
    }
    public List<SelectOperatorLogListOutDto> getOpertorLogAndFollowupList(List<Long> cluesIds) {

        List<SelectOperatorLogListOutDto> resultList = new CopyOnWriteArrayList<>();
        //查询写跟进
        List<SelectOperatorLogListOutDto> followUpByCluesIds = operatorLogDao.newFollowUpByCluesIds(cluesIds);
        resultList.addAll(followUpByCluesIds);
        //查询日志
        List<SelectOperatorLogListOutDto> operatorLogByCluesIds = operatorLogDao.newExportOperatorLogByCluesIds(cluesIds);
        resultList.addAll(operatorLogByCluesIds);
        return resultList;
    }
}
