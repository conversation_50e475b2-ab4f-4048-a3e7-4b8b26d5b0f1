package com.fotile.exportcenter.common.export;

import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.cmscenter.scheme.mapper.ExportContext;
import com.fotile.exportcenter.common.exportchannel.ExportChannel;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ExportConsumesService {

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    ExportContext exportContext;

    @StreamListener(ExportChannel.MATERIAL_EXPORT_INPUT)
    public void exportConsumes(Message<ExportTaskRecord> message) {

        ExportTaskRecord exportTaskRecord = message.getPayload();
        if(exportTaskRecord == null || exportTaskRecord.getId() == null){
            return;
        }
        Integer result = dataClientService.startTask(exportTaskRecord).getData();
        if(result != 1){
            //不等于1，则说明任务状态不是未进行，直接return
            return;
        }
        //调用查询任务接口，判断状态是否可以进行下载
        try {
            //根据类型调用不同的方法生产不同的数据
            exportContext.getExportImpl(exportTaskRecord.getType()).exportFile(exportTaskRecord);
        } catch (Exception e) {
            log.error("错误数据" + exportTaskRecord);
            log.error("报错信息" + e.getMessage());
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClientService.failureTask(exportTask);
        }
        return;
    }
}