package com.fotile.exportcenter.common.exportchannel;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface ExportChannel {

    /**
     * 导出接收消息
     */
    String MATERIAL_EXPORT_INPUT = "material_export_input";

    @Input(MATERIAL_EXPORT_INPUT)
    SubscribableChannel exportConsumesChannel();
}
