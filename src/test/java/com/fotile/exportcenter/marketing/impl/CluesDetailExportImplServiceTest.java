package com.fotile.exportcenter.marketing.impl;

import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@RunWith(SpringRunner.class)
public class CluesDetailExportImplServiceTest {
    @Autowired
    CluesDetailExportImplService cluesDetailExportImplService;

    @org.junit.jupiter.api.BeforeEach
    void setUp() {
    }

    @org.junit.jupiter.api.AfterEach
    void tearDown() {
    }

    @Test
    void exportFile() {
        ExportTaskRecord exportTaskRecord = new ExportTaskRecord();
        exportTaskRecord.setParamJson("{\"designerClubMemberIdList\":[-1],\"flag\":1,\"num\":1,\"page\":1,\"size\":50000},\"start\":1");
        exportTaskRecord.setTotalCount(100);
        exportTaskRecord.setProgress(new BigDecimal(1));
        cluesDetailExportImplService.exportFile(exportTaskRecord);
    }
}