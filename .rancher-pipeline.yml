stages:
- name: packages
  steps:
  - runScriptConfig:
      image: registry-vpc.cn-shanghai.aliyuncs.com/fotile_product/tools:mvn3.6-jdk11-v1.0
      shellScript: |-
        curl https://efotilebackup.oss-cn-shanghai.aliyuncs.com/ft_software/rancher/center/Dockerfile > Dockerfile
        mvn --batch-mode clean package -U -Dmaven.test.skip=true -P k8s
- name: build-images
  steps:
  - publishImageConfig:
      dockerfilePath: ./Dockerfile
      buildContext: .
      tag: registry-vpc.cn-shanghai.aliyuncs.com/fotile_product/appimages:${CICD_GIT_REPO_NAME}-${CICD_GIT_BRANCH}
      pushRemote: true
      registry: registry-vpc.cn-shanghai.aliyuncs.com
    env:
      PLUGIN_DEBUG: "true"
      PLUGIN_INSECURE: "true"
- name: build-commit-images
  steps:
  - publishImageConfig:
      dockerfilePath: ./Dockerfile
      buildContext: .
      tag: registry-vpc.cn-shanghai.aliyuncs.com/fotile_product/appimages:${CICD_GIT_REPO_NAME}-${CICD_GIT_COMMIT}
      pushRemote: true
      registry: registry-vpc.cn-shanghai.aliyuncs.com
timeout: 60
notification: {}
