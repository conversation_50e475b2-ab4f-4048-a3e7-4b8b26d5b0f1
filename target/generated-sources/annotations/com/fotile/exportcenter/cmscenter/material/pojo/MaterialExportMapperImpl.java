package com.fotile.exportcenter.cmscenter.material.pojo;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-01T16:55:42+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class MaterialExportMapperImpl implements MaterialExportMapper {

    @Override
    public List<MaterialExportOutDto> convertExportDTO(List<MaterialExportDto> dto) {
        if ( dto == null ) {
            return null;
        }

        List<MaterialExportOutDto> list = new ArrayList<MaterialExportOutDto>( dto.size() );
        for ( MaterialExportDto materialExportDto : dto ) {
            list.add( materialExportDtoToMaterialExportOutDto( materialExportDto ) );
        }

        return list;
    }

    protected MaterialExportOutDto materialExportDtoToMaterialExportOutDto(MaterialExportDto materialExportDto) {
        if ( materialExportDto == null ) {
            return null;
        }

        MaterialExportOutDto materialExportOutDto = new MaterialExportOutDto();

        materialExportOutDto.setTreeId( materialExportDto.getTreeId() );
        materialExportOutDto.setTreeName( materialExportDto.getTreeName() );
        materialExportOutDto.setMaterialId( materialExportDto.getMaterialId() );
        materialExportOutDto.setTitle( materialExportDto.getTitle() );
        materialExportOutDto.setTypeDesc( materialExportDto.getTypeDesc() );
        materialExportOutDto.setNodeName( materialExportDto.getNodeName() );
        materialExportOutDto.setProductName( materialExportDto.getProductName() );
        materialExportOutDto.setModelName( materialExportDto.getModelName() );
        materialExportOutDto.setMaterialTag( materialExportDto.getMaterialTag() );
        materialExportOutDto.setSort( materialExportDto.getSort() );
        materialExportOutDto.setStatusDesc( materialExportDto.getStatusDesc() );
        materialExportOutDto.setShareCount( materialExportDto.getShareCount() );
        materialExportOutDto.setFavoriteCount( materialExportDto.getFavoriteCount() );
        materialExportOutDto.setHelpfulCount( materialExportDto.getHelpfulCount() );
        materialExportOutDto.setUnhelpfulCount( materialExportDto.getUnhelpfulCount() );
        materialExportOutDto.setModifiedDateDesc( materialExportDto.getModifiedDateDesc() );
        materialExportOutDto.setModifiedUsername( materialExportDto.getModifiedUsername() );
        materialExportOutDto.setCreatedDateDesc( materialExportDto.getCreatedDateDesc() );
        materialExportOutDto.setBasicMaterialIdOne( materialExportDto.getBasicMaterialIdOne() );
        materialExportOutDto.setSourceDescNamesOne( materialExportDto.getSourceDescNamesOne() );
        materialExportOutDto.setContentTypeDescOne( materialExportDto.getContentTypeDescOne() );
        materialExportOutDto.setMaterialUrlOne( materialExportDto.getMaterialUrlOne() );
        materialExportOutDto.setContentTextOne( materialExportDto.getContentTextOne() );
        materialExportOutDto.setBasicMaterialIdSecond( materialExportDto.getBasicMaterialIdSecond() );
        materialExportOutDto.setSourceDescNamesSecond( materialExportDto.getSourceDescNamesSecond() );
        materialExportOutDto.setContentTypeDescSecond( materialExportDto.getContentTypeDescSecond() );
        materialExportOutDto.setMaterialUrlSecond( materialExportDto.getMaterialUrlSecond() );
        materialExportOutDto.setContentTextSecond( materialExportDto.getContentTextSecond() );
        materialExportOutDto.setBasicMaterialIdThird( materialExportDto.getBasicMaterialIdThird() );
        materialExportOutDto.setSourceDescNamesThird( materialExportDto.getSourceDescNamesThird() );
        materialExportOutDto.setContentTypeDescThird( materialExportDto.getContentTypeDescThird() );
        materialExportOutDto.setMaterialUrlThird( materialExportDto.getMaterialUrlThird() );
        materialExportOutDto.setContentTextThird( materialExportDto.getContentTextThird() );
        materialExportOutDto.setBasicMaterialIdFourth( materialExportDto.getBasicMaterialIdFourth() );
        materialExportOutDto.setSourceDescNamesFourth( materialExportDto.getSourceDescNamesFourth() );
        materialExportOutDto.setContentTypeDescFourth( materialExportDto.getContentTypeDescFourth() );
        materialExportOutDto.setMaterialUrlFourth( materialExportDto.getMaterialUrlFourth() );
        materialExportOutDto.setContentTextFourth( materialExportDto.getContentTextFourth() );
        materialExportOutDto.setBasicMaterialIdFifth( materialExportDto.getBasicMaterialIdFifth() );
        materialExportOutDto.setSourceDescNamesFifth( materialExportDto.getSourceDescNamesFifth() );
        materialExportOutDto.setContentTypeDescFifth( materialExportDto.getContentTypeDescFifth() );
        materialExportOutDto.setMaterialUrlFifth( materialExportDto.getMaterialUrlFifth() );
        materialExportOutDto.setContentTextFifth( materialExportDto.getContentTextFifth() );
        materialExportOutDto.setBasicMaterialIdSix( materialExportDto.getBasicMaterialIdSix() );
        materialExportOutDto.setSourceDescNamesSix( materialExportDto.getSourceDescNamesSix() );
        materialExportOutDto.setContentTypeDescSix( materialExportDto.getContentTypeDescSix() );
        materialExportOutDto.setMaterialUrlSix( materialExportDto.getMaterialUrlSix() );
        materialExportOutDto.setContentTextSix( materialExportDto.getContentTextSix() );
        materialExportOutDto.setBasicMaterialIdSeven( materialExportDto.getBasicMaterialIdSeven() );
        materialExportOutDto.setSourceDescNamesSeven( materialExportDto.getSourceDescNamesSeven() );
        materialExportOutDto.setContentTypeDescSeven( materialExportDto.getContentTypeDescSeven() );
        materialExportOutDto.setMaterialUrlSeven( materialExportDto.getMaterialUrlSeven() );
        materialExportOutDto.setContentTextSeven( materialExportDto.getContentTextSeven() );
        materialExportOutDto.setBasicMaterialIdEighth( materialExportDto.getBasicMaterialIdEighth() );
        materialExportOutDto.setSourceDescNamesEighth( materialExportDto.getSourceDescNamesEighth() );
        materialExportOutDto.setContentTypeDescEighth( materialExportDto.getContentTypeDescEighth() );
        materialExportOutDto.setMaterialUrlEighth( materialExportDto.getMaterialUrlEighth() );
        materialExportOutDto.setContentTextEighth( materialExportDto.getContentTextEighth() );
        materialExportOutDto.setBasicMaterialIdNine( materialExportDto.getBasicMaterialIdNine() );
        materialExportOutDto.setSourceDescNamesNine( materialExportDto.getSourceDescNamesNine() );
        materialExportOutDto.setContentTypeDescNine( materialExportDto.getContentTypeDescNine() );
        materialExportOutDto.setMaterialUrlNine( materialExportDto.getMaterialUrlNine() );
        materialExportOutDto.setContentTextNine( materialExportDto.getContentTextNine() );

        return materialExportOutDto;
    }
}
