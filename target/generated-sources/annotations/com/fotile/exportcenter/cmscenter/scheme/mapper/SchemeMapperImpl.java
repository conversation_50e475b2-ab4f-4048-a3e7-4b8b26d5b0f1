package com.fotile.exportcenter.cmscenter.scheme.mapper;

import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.DeviseSchemeVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeExplainRecordVO;
import com.fotile.exportcenter.cmscenter.scheme.pojo.vo.ExportDeviseSchemeVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:17+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class SchemeMapperImpl implements SchemeMapper {

    @Override
    public List<ExportDeviseSchemeVO> detailToExcelDto(List<DeviseSchemeVO> likeDetails) {
        if ( likeDetails == null ) {
            return null;
        }

        List<ExportDeviseSchemeVO> list = new ArrayList<ExportDeviseSchemeVO>( likeDetails.size() );
        for ( DeviseSchemeVO deviseSchemeVO : likeDetails ) {
            list.add( deviseSchemeVOToExportDeviseSchemeVO( deviseSchemeVO ) );
        }

        return list;
    }

    @Override
    public List<ExportDeviseSchemeExplainRecordVO> explainRecordToExcelDto(List<DeviseSchemeExplainRecordVO> resultList) {
        if ( resultList == null ) {
            return null;
        }

        List<ExportDeviseSchemeExplainRecordVO> list = new ArrayList<ExportDeviseSchemeExplainRecordVO>( resultList.size() );
        for ( DeviseSchemeExplainRecordVO deviseSchemeExplainRecordVO : resultList ) {
            list.add( deviseSchemeExplainRecordVOToExportDeviseSchemeExplainRecordVO( deviseSchemeExplainRecordVO ) );
        }

        return list;
    }

    protected ExportDeviseSchemeVO deviseSchemeVOToExportDeviseSchemeVO(DeviseSchemeVO deviseSchemeVO) {
        if ( deviseSchemeVO == null ) {
            return null;
        }

        ExportDeviseSchemeVO exportDeviseSchemeVO = new ExportDeviseSchemeVO();

        exportDeviseSchemeVO.setProjectCode( deviseSchemeVO.getProjectCode() );
        exportDeviseSchemeVO.setProjectName( deviseSchemeVO.getProjectName() );
        exportDeviseSchemeVO.setProvinceName( deviseSchemeVO.getProvinceName() );
        exportDeviseSchemeVO.setVillageName( deviseSchemeVO.getVillageName() );
        exportDeviseSchemeVO.setPlanName( deviseSchemeVO.getPlanName() );
        exportDeviseSchemeVO.setTypeName( deviseSchemeVO.getTypeName() );
        exportDeviseSchemeVO.setLabelNames( deviseSchemeVO.getLabelNames() );
        exportDeviseSchemeVO.setGoodsName( deviseSchemeVO.getGoodsName() );
        exportDeviseSchemeVO.setRelationClues( deviseSchemeVO.getRelationClues() );
        exportDeviseSchemeVO.setExplainCount( deviseSchemeVO.getExplainCount() );
        exportDeviseSchemeVO.setDurationTimes( deviseSchemeVO.getDurationTimes() );
        exportDeviseSchemeVO.setExplainCluesCount( deviseSchemeVO.getExplainCluesCount() );
        exportDeviseSchemeVO.setExplainDealCluesCount( deviseSchemeVO.getExplainDealCluesCount() );
        exportDeviseSchemeVO.setOrderAmount( deviseSchemeVO.getOrderAmount() );
        exportDeviseSchemeVO.setStatusName( deviseSchemeVO.getStatusName() );
        exportDeviseSchemeVO.setAreaName( deviseSchemeVO.getAreaName() );
        exportDeviseSchemeVO.setCompanyName( deviseSchemeVO.getCompanyName() );
        exportDeviseSchemeVO.setStoreChannelName( deviseSchemeVO.getStoreChannelName() );
        exportDeviseSchemeVO.setStoreSubChannelName( deviseSchemeVO.getStoreSubChannelName() );
        exportDeviseSchemeVO.setStoreName( deviseSchemeVO.getStoreName() );
        exportDeviseSchemeVO.setCreatedName( deviseSchemeVO.getCreatedName() );
        exportDeviseSchemeVO.setCreateChargeCode( deviseSchemeVO.getCreateChargeCode() );
        exportDeviseSchemeVO.setCreateChargeUserName( deviseSchemeVO.getCreateChargeUserName() );
        exportDeviseSchemeVO.setCreateStoreCode( deviseSchemeVO.getCreateStoreCode() );
        exportDeviseSchemeVO.setCreateStoreName( deviseSchemeVO.getCreateStoreName() );
        exportDeviseSchemeVO.setCreatedDate( deviseSchemeVO.getCreatedDate() );
        exportDeviseSchemeVO.setReviseName( deviseSchemeVO.getReviseName() );
        exportDeviseSchemeVO.setReviseTime( deviseSchemeVO.getReviseTime() );
        exportDeviseSchemeVO.setThreeViewCount( deviseSchemeVO.getThreeViewCount() );
        exportDeviseSchemeVO.setThreeThumbsCount( deviseSchemeVO.getThreeThumbsCount() );
        exportDeviseSchemeVO.setThreeCollectCount( deviseSchemeVO.getThreeCollectCount() );
        exportDeviseSchemeVO.setThreeSendCount( deviseSchemeVO.getThreeSendCount() );

        return exportDeviseSchemeVO;
    }

    protected ExportDeviseSchemeExplainRecordVO deviseSchemeExplainRecordVOToExportDeviseSchemeExplainRecordVO(DeviseSchemeExplainRecordVO deviseSchemeExplainRecordVO) {
        if ( deviseSchemeExplainRecordVO == null ) {
            return null;
        }

        ExportDeviseSchemeExplainRecordVO exportDeviseSchemeExplainRecordVO = new ExportDeviseSchemeExplainRecordVO();

        exportDeviseSchemeExplainRecordVO.setProjectCode( deviseSchemeExplainRecordVO.getProjectCode() );
        exportDeviseSchemeExplainRecordVO.setProjectName( deviseSchemeExplainRecordVO.getProjectName() );
        exportDeviseSchemeExplainRecordVO.setProvinceName( deviseSchemeExplainRecordVO.getProvinceName() );
        exportDeviseSchemeExplainRecordVO.setVillageName( deviseSchemeExplainRecordVO.getVillageName() );
        exportDeviseSchemeExplainRecordVO.setTypeName( deviseSchemeExplainRecordVO.getTypeName() );
        exportDeviseSchemeExplainRecordVO.setRelationClues( deviseSchemeExplainRecordVO.getRelationClues() );
        exportDeviseSchemeExplainRecordVO.setRelatedCluesTime( deviseSchemeExplainRecordVO.getRelatedCluesTime() );
        exportDeviseSchemeExplainRecordVO.setExplainCount( deviseSchemeExplainRecordVO.getExplainCount() );
        exportDeviseSchemeExplainRecordVO.setDurationTimes( deviseSchemeExplainRecordVO.getDurationTimes() );
        exportDeviseSchemeExplainRecordVO.setStatusName( deviseSchemeExplainRecordVO.getStatusName() );
        exportDeviseSchemeExplainRecordVO.setCreatedCompanyName( deviseSchemeExplainRecordVO.getCreatedCompanyName() );
        exportDeviseSchemeExplainRecordVO.setCreatedName( deviseSchemeExplainRecordVO.getCreatedName() );
        exportDeviseSchemeExplainRecordVO.setCreatedDate( deviseSchemeExplainRecordVO.getCreatedDate() );
        exportDeviseSchemeExplainRecordVO.setCreatedName1( deviseSchemeExplainRecordVO.getCreatedName1() );
        exportDeviseSchemeExplainRecordVO.setChargeUserName( deviseSchemeExplainRecordVO.getChargeUserName() );
        exportDeviseSchemeExplainRecordVO.setRegionName( deviseSchemeExplainRecordVO.getRegionName() );
        exportDeviseSchemeExplainRecordVO.setCompanyName( deviseSchemeExplainRecordVO.getCompanyName() );
        exportDeviseSchemeExplainRecordVO.setStoreChannelName( deviseSchemeExplainRecordVO.getStoreChannelName() );
        exportDeviseSchemeExplainRecordVO.setStoreSubChannelName( deviseSchemeExplainRecordVO.getStoreSubChannelName() );
        exportDeviseSchemeExplainRecordVO.setStoreName( deviseSchemeExplainRecordVO.getStoreName() );
        exportDeviseSchemeExplainRecordVO.setSourceName( deviseSchemeExplainRecordVO.getSourceName() );
        exportDeviseSchemeExplainRecordVO.setExplainCluesResult( deviseSchemeExplainRecordVO.getExplainCluesResult() );
        exportDeviseSchemeExplainRecordVO.setOrderStatus( deviseSchemeExplainRecordVO.getOrderStatus() );
        exportDeviseSchemeExplainRecordVO.setOrderAmount( deviseSchemeExplainRecordVO.getOrderAmount() );
        exportDeviseSchemeExplainRecordVO.setStartTime( deviseSchemeExplainRecordVO.getStartTime() );
        exportDeviseSchemeExplainRecordVO.setEndTime( deviseSchemeExplainRecordVO.getEndTime() );
        exportDeviseSchemeExplainRecordVO.setAbnormalEndTypeName( deviseSchemeExplainRecordVO.getAbnormalEndTypeName() );
        exportDeviseSchemeExplainRecordVO.setSingleDurationTime( deviseSchemeExplainRecordVO.getSingleDurationTime() );

        return exportDeviseSchemeExplainRecordVO;
    }
}
