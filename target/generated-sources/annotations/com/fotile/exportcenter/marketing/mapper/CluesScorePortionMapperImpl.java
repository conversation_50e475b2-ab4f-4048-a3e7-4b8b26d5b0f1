package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesScorePortionExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:17+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CluesScorePortionMapperImpl implements CluesScorePortionMapper {

    @Override
    public List<CluesScorePortionExportOutDto> newExportToCluesScorePortionOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos) {
        if ( newExportUserClusesOutDtos == null ) {
            return null;
        }

        List<CluesScorePortionExportOutDto> list = new ArrayList<CluesScorePortionExportOutDto>( newExportUserClusesOutDtos.size() );
        for ( NewExportUserClusesOutDto newExportUserClusesOutDto : newExportUserClusesOutDtos ) {
            list.add( newExportUserClusesOutDtoToCluesScorePortionExportOutDto( newExportUserClusesOutDto ) );
        }

        return list;
    }

    protected CluesScorePortionExportOutDto newExportUserClusesOutDtoToCluesScorePortionExportOutDto(NewExportUserClusesOutDto newExportUserClusesOutDto) {
        if ( newExportUserClusesOutDto == null ) {
            return null;
        }

        CluesScorePortionExportOutDto cluesScorePortionExportOutDto = new CluesScorePortionExportOutDto();

        cluesScorePortionExportOutDto.setIndex( newExportUserClusesOutDto.getIndex() );
        cluesScorePortionExportOutDto.setMd5( newExportUserClusesOutDto.getMd5() );
        cluesScorePortionExportOutDto.setId( newExportUserClusesOutDto.getId() );
        cluesScorePortionExportOutDto.setTag( newExportUserClusesOutDto.getTag() );
        cluesScorePortionExportOutDto.setTagScore( newExportUserClusesOutDto.getTagScore() );
        cluesScorePortionExportOutDto.setCustomerName( newExportUserClusesOutDto.getCustomerName() );
        cluesScorePortionExportOutDto.setCustomerPhone( newExportUserClusesOutDto.getCustomerPhone() );
        cluesScorePortionExportOutDto.setDistrictValue( newExportUserClusesOutDto.getDistrictValue() );
        cluesScorePortionExportOutDto.setCompanyName( newExportUserClusesOutDto.getCompanyName() );
        cluesScorePortionExportOutDto.setStroeName( newExportUserClusesOutDto.getStroeName() );
        cluesScorePortionExportOutDto.setProvinceName( newExportUserClusesOutDto.getProvinceName() );
        cluesScorePortionExportOutDto.setCityName( newExportUserClusesOutDto.getCityName() );
        cluesScorePortionExportOutDto.setCountyName( newExportUserClusesOutDto.getCountyName() );
        cluesScorePortionExportOutDto.setAddress( newExportUserClusesOutDto.getAddress() );
        cluesScorePortionExportOutDto.setVillage( newExportUserClusesOutDto.getVillage() );

        return cluesScorePortionExportOutDto;
    }
}
