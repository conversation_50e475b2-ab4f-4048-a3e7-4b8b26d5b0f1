package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesDetailExportOutDtos;
import com.fotile.exportcenter.marketing.pojo.dto.CluesDetailSensitiveExportOutDtos;
import com.fotile.exportcenter.marketing.pojo.dto.NewCluesFollowServiceDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import com.fotile.exportcenter.marketing.pojo.vo.NewCluesFollowServiceVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:17+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CluesDetailMapperImpl implements CluesDetailMapper {

    @Override
    public List<CluesDetailExportOutDtos> newExportToCluesDetailOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos) {
        if ( newExportUserClusesOutDtos == null ) {
            return null;
        }

        List<CluesDetailExportOutDtos> list = new ArrayList<CluesDetailExportOutDtos>( newExportUserClusesOutDtos.size() );
        for ( NewExportUserClusesOutDto newExportUserClusesOutDto : newExportUserClusesOutDtos ) {
            list.add( newExportUserClusesOutDtoToCluesDetailExportOutDtos( newExportUserClusesOutDto ) );
        }

        return list;
    }

    @Override
    public List<CluesDetailSensitiveExportOutDtos> newExportToCluesDetailSensitiveOutDto(List<NewExportUserClusesOutDto> clues) {
        if ( clues == null ) {
            return null;
        }

        List<CluesDetailSensitiveExportOutDtos> list = new ArrayList<CluesDetailSensitiveExportOutDtos>( clues.size() );
        for ( NewExportUserClusesOutDto newExportUserClusesOutDto : clues ) {
            list.add( newExportUserClusesOutDtoToCluesDetailSensitiveExportOutDtos( newExportUserClusesOutDto ) );
        }

        return list;
    }

    @Override
    public List<NewCluesFollowServiceDto> newExportToCluesFollowServiceOutDto(List<NewCluesFollowServiceVo> newExportUserClusesOutDto) {
        if ( newExportUserClusesOutDto == null ) {
            return null;
        }

        List<NewCluesFollowServiceDto> list = new ArrayList<NewCluesFollowServiceDto>( newExportUserClusesOutDto.size() );
        for ( NewCluesFollowServiceVo newCluesFollowServiceVo : newExportUserClusesOutDto ) {
            list.add( newCluesFollowServiceVoToNewCluesFollowServiceDto( newCluesFollowServiceVo ) );
        }

        return list;
    }

    protected CluesDetailExportOutDtos newExportUserClusesOutDtoToCluesDetailExportOutDtos(NewExportUserClusesOutDto newExportUserClusesOutDto) {
        if ( newExportUserClusesOutDto == null ) {
            return null;
        }

        CluesDetailExportOutDtos cluesDetailExportOutDtos = new CluesDetailExportOutDtos();

        cluesDetailExportOutDtos.setIndex( newExportUserClusesOutDto.getIndex() );
        cluesDetailExportOutDtos.setId( newExportUserClusesOutDto.getId() );
        cluesDetailExportOutDtos.setChannelName( newExportUserClusesOutDto.getChannelName() );
        cluesDetailExportOutDtos.setRadioName( newExportUserClusesOutDto.getRadioName() );
        cluesDetailExportOutDtos.setCreateSourcePlatformValue( newExportUserClusesOutDto.getCreateSourcePlatformValue() );
        cluesDetailExportOutDtos.setActivityId1( newExportUserClusesOutDto.getActivityId1() );
        cluesDetailExportOutDtos.setCreatCluesMethod( newExportUserClusesOutDto.getCreatCluesMethod() );
        cluesDetailExportOutDtos.setActivityType( newExportUserClusesOutDto.getActivityType() );
        cluesDetailExportOutDtos.setActivityName( newExportUserClusesOutDto.getActivityName() );
        cluesDetailExportOutDtos.setIntegrateCode( newExportUserClusesOutDto.getIntegrateCode() );
        cluesDetailExportOutDtos.setTemplateCode( newExportUserClusesOutDto.getTemplateCode() );
        cluesDetailExportOutDtos.setIntegrateTitle( newExportUserClusesOutDto.getIntegrateTitle() );
        cluesDetailExportOutDtos.setCustomerName( newExportUserClusesOutDto.getCustomerName() );
        cluesDetailExportOutDtos.setGender( newExportUserClusesOutDto.getGender() );
        cluesDetailExportOutDtos.setCustomerPhone( newExportUserClusesOutDto.getCustomerPhone() );
        cluesDetailExportOutDtos.setWechatno( newExportUserClusesOutDto.getWechatno() );
        cluesDetailExportOutDtos.setFundingTime( newExportUserClusesOutDto.getFundingTime() );
        cluesDetailExportOutDtos.setDistrictValue( newExportUserClusesOutDto.getDistrictValue() );
        cluesDetailExportOutDtos.setCompanyName( newExportUserClusesOutDto.getCompanyName() );
        cluesDetailExportOutDtos.setDistributorName( newExportUserClusesOutDto.getDistributorName() );
        cluesDetailExportOutDtos.setStroeCode( newExportUserClusesOutDto.getStroeCode() );
        cluesDetailExportOutDtos.setStroeName( newExportUserClusesOutDto.getStroeName() );
        cluesDetailExportOutDtos.setAbbreviation( newExportUserClusesOutDto.getAbbreviation() );
        cluesDetailExportOutDtos.setStoreTypeName( newExportUserClusesOutDto.getStoreTypeName() );
        cluesDetailExportOutDtos.setStoreSubChannelName( newExportUserClusesOutDto.getStoreSubChannelName() );
        cluesDetailExportOutDtos.setStoreCategoryName( newExportUserClusesOutDto.getStoreCategoryName() );
        cluesDetailExportOutDtos.setFullPathName( newExportUserClusesOutDto.getFullPathName() );
        cluesDetailExportOutDtos.setCreateUserName( newExportUserClusesOutDto.getCreateUserName() );
        cluesDetailExportOutDtos.setChargeCode( newExportUserClusesOutDto.getChargeCode() );
        cluesDetailExportOutDtos.setSalesman( newExportUserClusesOutDto.getSalesman() );
        cluesDetailExportOutDtos.setAssistUserCode( newExportUserClusesOutDto.getAssistUserCode() );
        cluesDetailExportOutDtos.setAssistUserName( newExportUserClusesOutDto.getAssistUserName() );
        cluesDetailExportOutDtos.setModifiedChargeUserDate( newExportUserClusesOutDto.getModifiedChargeUserDate() );
        cluesDetailExportOutDtos.setOthers( newExportUserClusesOutDto.getOthers() );
        cluesDetailExportOutDtos.setVisitStatus( newExportUserClusesOutDto.getVisitStatus() );
        cluesDetailExportOutDtos.setVisitTime( newExportUserClusesOutDto.getVisitTime() );
        cluesDetailExportOutDtos.setReviseTime( newExportUserClusesOutDto.getReviseTime() );
        cluesDetailExportOutDtos.setFirstFollowTime( newExportUserClusesOutDto.getFirstFollowTime() );
        cluesDetailExportOutDtos.setLastFollowTime( newExportUserClusesOutDto.getLastFollowTime() );
        cluesDetailExportOutDtos.setOpenSeaflag( newExportUserClusesOutDto.getOpenSeaflag() );
        cluesDetailExportOutDtos.setFrequencyOpenSeas( newExportUserClusesOutDto.getFrequencyOpenSeas() );
        cluesDetailExportOutDtos.setFrontSalesman( newExportUserClusesOutDto.getFrontSalesman() );
        cluesDetailExportOutDtos.setAuditTime( newExportUserClusesOutDto.getAuditTime() );
        cluesDetailExportOutDtos.setFollowEfficiencyCategoryValue( newExportUserClusesOutDto.getFollowEfficiencyCategoryValue() );
        if ( newExportUserClusesOutDto.getFollowUpCount() != null ) {
            cluesDetailExportOutDtos.setFollowUpCount( String.valueOf( newExportUserClusesOutDto.getFollowUpCount() ) );
        }
        cluesDetailExportOutDtos.setArtificialFollowUpCount( newExportUserClusesOutDto.getArtificialFollowUpCount() );
        cluesDetailExportOutDtos.setArtificialFollowUpFlag( newExportUserClusesOutDto.getArtificialFollowUpFlag() );
        cluesDetailExportOutDtos.setRemark( newExportUserClusesOutDto.getRemark() );
        cluesDetailExportOutDtos.setFreehandSketchingFlagName( newExportUserClusesOutDto.getFreehandSketchingFlagName() );
        cluesDetailExportOutDtos.setKitchenDesignFlagName( newExportUserClusesOutDto.getKitchenDesignFlagName() );
        cluesDetailExportOutDtos.setCostumeChangeReportFlagName( newExportUserClusesOutDto.getCostumeChangeReportFlagName() );
        cluesDetailExportOutDtos.setStatus( newExportUserClusesOutDto.getStatus() );
        cluesDetailExportOutDtos.setKeyWords( newExportUserClusesOutDto.getKeyWords() );
        cluesDetailExportOutDtos.setRegularSubscriber( newExportUserClusesOutDto.getRegularSubscriber() );
        cluesDetailExportOutDtos.setWithSingleOldUserCode( newExportUserClusesOutDto.getWithSingleOldUserCode() );
        cluesDetailExportOutDtos.setWithSingleOldUserName( newExportUserClusesOutDto.getWithSingleOldUserName() );
        cluesDetailExportOutDtos.setBuryPipe( newExportUserClusesOutDto.getBuryPipe() );
        cluesDetailExportOutDtos.setIsFollowUp( newExportUserClusesOutDto.getIsFollowUp() );
        cluesDetailExportOutDtos.setIsIntoStores( newExportUserClusesOutDto.getIsIntoStores() );
        cluesDetailExportOutDtos.setIsDeal( newExportUserClusesOutDto.getIsDeal() );
        cluesDetailExportOutDtos.setCsmOrderCode( newExportUserClusesOutDto.getCsmOrderCode() );
        cluesDetailExportOutDtos.setServiceEngineerCsn( newExportUserClusesOutDto.getServiceEngineerCsn() );
        cluesDetailExportOutDtos.setIsLostOrder( newExportUserClusesOutDto.getIsLostOrder() );
        cluesDetailExportOutDtos.setLostOrderValue( newExportUserClusesOutDto.getLostOrderValue() );
        cluesDetailExportOutDtos.setLostOrderSubValue( newExportUserClusesOutDto.getLostOrderSubValue() );
        cluesDetailExportOutDtos.setSelfLackReviewStatusValue( newExportUserClusesOutDto.getSelfLackReviewStatusValue() );
        cluesDetailExportOutDtos.setIsHouserholdsVisit( newExportUserClusesOutDto.getIsHouserholdsVisit() );
        cluesDetailExportOutDtos.setIsComeDevise( newExportUserClusesOutDto.getIsComeDevise() );
        cluesDetailExportOutDtos.setIsComeDeviseTime( newExportUserClusesOutDto.getIsComeDeviseTime() );
        cluesDetailExportOutDtos.setAppointmentTime( newExportUserClusesOutDto.getAppointmentTime() );
        cluesDetailExportOutDtos.setIsSceneInteractionValue( newExportUserClusesOutDto.getIsSceneInteractionValue() );
        cluesDetailExportOutDtos.setIsSceneInteractionTime( newExportUserClusesOutDto.getIsSceneInteractionTime() );
        cluesDetailExportOutDtos.setFlagInstallation( newExportUserClusesOutDto.getFlagInstallation() );
        cluesDetailExportOutDtos.setFlagMoveIn( newExportUserClusesOutDto.getFlagMoveIn() );
        cluesDetailExportOutDtos.setOrderEarnestFlagName( newExportUserClusesOutDto.getOrderEarnestFlagName() );
        cluesDetailExportOutDtos.setCluesType( newExportUserClusesOutDto.getCluesType() );
        cluesDetailExportOutDtos.setCluesSource( newExportUserClusesOutDto.getCluesSource() );
        cluesDetailExportOutDtos.setCluesLevel( newExportUserClusesOutDto.getCluesLevel() );
        cluesDetailExportOutDtos.setUrgencyFollowUp( newExportUserClusesOutDto.getUrgencyFollowUp() );
        cluesDetailExportOutDtos.setUrgencyPurchase( newExportUserClusesOutDto.getUrgencyPurchase() );
        cluesDetailExportOutDtos.setReviewStatus( newExportUserClusesOutDto.getReviewStatus() );
        cluesDetailExportOutDtos.setFollowUpStatus( newExportUserClusesOutDto.getFollowUpStatus() );
        cluesDetailExportOutDtos.setAuditStatus( newExportUserClusesOutDto.getAuditStatus() );
        cluesDetailExportOutDtos.setIntentionProduct( newExportUserClusesOutDto.getIntentionProduct() );
        cluesDetailExportOutDtos.setDecorateProgres( newExportUserClusesOutDto.getDecorateProgres() );
        cluesDetailExportOutDtos.setTotalScore( newExportUserClusesOutDto.getTotalScore() );
        cluesDetailExportOutDtos.setDecorateAndDesignerTypeName( newExportUserClusesOutDto.getDecorateAndDesignerTypeName() );
        cluesDetailExportOutDtos.setDecorateAndDesignerCode( newExportUserClusesOutDto.getDecorateAndDesignerCode() );
        cluesDetailExportOutDtos.setDecorateAndDesignerName( newExportUserClusesOutDto.getDecorateAndDesignerName() );
        cluesDetailExportOutDtos.setDecorateAndDesignerPhone( newExportUserClusesOutDto.getDecorateAndDesignerPhone() );
        cluesDetailExportOutDtos.setDesignerClubMemberGradeName( newExportUserClusesOutDto.getDesignerClubMemberGradeName() );
        cluesDetailExportOutDtos.setDesignerClubMemberId( newExportUserClusesOutDto.getDesignerClubMemberId() );
        cluesDetailExportOutDtos.setCommunityMemberJson( newExportUserClusesOutDto.getCommunityMemberJson() );
        cluesDetailExportOutDtos.setIsMakeBargain( newExportUserClusesOutDto.getIsMakeBargain() );
        cluesDetailExportOutDtos.setPayStatus( newExportUserClusesOutDto.getPayStatus() );
        cluesDetailExportOutDtos.setMakeBargainProduct( newExportUserClusesOutDto.getMakeBargainProduct() );
        cluesDetailExportOutDtos.setMakeBargainSystem( newExportUserClusesOutDto.getMakeBargainSystem() );
        cluesDetailExportOutDtos.setPreferenceStatusName( newExportUserClusesOutDto.getPreferenceStatusName() );
        cluesDetailExportOutDtos.setPreferenceNote( newExportUserClusesOutDto.getPreferenceNote() );
        cluesDetailExportOutDtos.setDecorateType( newExportUserClusesOutDto.getDecorateType() );
        cluesDetailExportOutDtos.setMakeRoomTime( newExportUserClusesOutDto.getMakeRoomTime() );
        cluesDetailExportOutDtos.setInStoreTimeValue( newExportUserClusesOutDto.getInStoreTimeValue() );
        cluesDetailExportOutDtos.setInStoreTypeValue( newExportUserClusesOutDto.getInStoreTypeValue() );
        cluesDetailExportOutDtos.setRetentionMethodValue( newExportUserClusesOutDto.getRetentionMethodValue() );
        cluesDetailExportOutDtos.setCustomerBringTypeValue( newExportUserClusesOutDto.getCustomerBringTypeValue() );
        cluesDetailExportOutDtos.setIntroducerName( newExportUserClusesOutDto.getIntroducerName() );
        cluesDetailExportOutDtos.setIntroducerPhone( newExportUserClusesOutDto.getIntroducerPhone() );
        cluesDetailExportOutDtos.setCardNo( newExportUserClusesOutDto.getCardNo() );
        cluesDetailExportOutDtos.setQywxRelationFlag( newExportUserClusesOutDto.getQywxRelationFlag() );
        cluesDetailExportOutDtos.setFollowUpNoDate( newExportUserClusesOutDto.getFollowUpNoDate() );
        cluesDetailExportOutDtos.setUnFollowDate( newExportUserClusesOutDto.getUnFollowDate() );
        if ( newExportUserClusesOutDto.getTeleConnectionCount() != null ) {
            cluesDetailExportOutDtos.setTeleConnectionCount( String.valueOf( newExportUserClusesOutDto.getTeleConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getWeChatConnectionCount() != null ) {
            cluesDetailExportOutDtos.setWeChatConnectionCount( String.valueOf( newExportUserClusesOutDto.getWeChatConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getVisitConnectionCount() != null ) {
            cluesDetailExportOutDtos.setVisitConnectionCount( String.valueOf( newExportUserClusesOutDto.getVisitConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getIntoShopConnectionCount() != null ) {
            cluesDetailExportOutDtos.setIntoShopConnectionCount( String.valueOf( newExportUserClusesOutDto.getIntoShopConnectionCount() ) );
        }
        cluesDetailExportOutDtos.setUtmSource( newExportUserClusesOutDto.getUtmSource() );
        cluesDetailExportOutDtos.setHouseType( newExportUserClusesOutDto.getHouseType() );
        cluesDetailExportOutDtos.setHouseRenovationType( newExportUserClusesOutDto.getHouseRenovationType() );
        cluesDetailExportOutDtos.setVillage( newExportUserClusesOutDto.getVillage() );
        cluesDetailExportOutDtos.setCpc( newExportUserClusesOutDto.getCpc() );
        cluesDetailExportOutDtos.setAddress( newExportUserClusesOutDto.getAddress() );
        cluesDetailExportOutDtos.setBuilding( newExportUserClusesOutDto.getBuilding() );
        cluesDetailExportOutDtos.setUnit( newExportUserClusesOutDto.getUnit() );
        cluesDetailExportOutDtos.setHouseNumber( newExportUserClusesOutDto.getHouseNumber() );
        cluesDetailExportOutDtos.setHouseModel( newExportUserClusesOutDto.getHouseModel() );
        cluesDetailExportOutDtos.setHouseArea( newExportUserClusesOutDto.getHouseArea() );
        cluesDetailExportOutDtos.setKitchenType( newExportUserClusesOutDto.getKitchenType() );
        cluesDetailExportOutDtos.setKitchenArea( newExportUserClusesOutDto.getKitchenArea() );
        cluesDetailExportOutDtos.setBudgetAccounting( newExportUserClusesOutDto.getBudgetAccounting() );
        cluesDetailExportOutDtos.setInstallDateValue( newExportUserClusesOutDto.getInstallDateValue() );
        cluesDetailExportOutDtos.setMoveinDateValue( newExportUserClusesOutDto.getMoveinDateValue() );
        cluesDetailExportOutDtos.setMakeBargainCycle( newExportUserClusesOutDto.getMakeBargainCycle() );
        cluesDetailExportOutDtos.setDyAdvertiseNickName( newExportUserClusesOutDto.getDyAdvertiseNickName() );
        cluesDetailExportOutDtos.setDyCluesIds( newExportUserClusesOutDto.getDyCluesIds() );
        cluesDetailExportOutDtos.setDyOrderIds( newExportUserClusesOutDto.getDyOrderIds() );
        cluesDetailExportOutDtos.setUnionId( newExportUserClusesOutDto.getUnionId() );
        cluesDetailExportOutDtos.setWxName( newExportUserClusesOutDto.getWxName() );

        return cluesDetailExportOutDtos;
    }

    protected CluesDetailSensitiveExportOutDtos newExportUserClusesOutDtoToCluesDetailSensitiveExportOutDtos(NewExportUserClusesOutDto newExportUserClusesOutDto) {
        if ( newExportUserClusesOutDto == null ) {
            return null;
        }

        CluesDetailSensitiveExportOutDtos cluesDetailSensitiveExportOutDtos = new CluesDetailSensitiveExportOutDtos();

        cluesDetailSensitiveExportOutDtos.setIndex( newExportUserClusesOutDto.getIndex() );
        cluesDetailSensitiveExportOutDtos.setId( newExportUserClusesOutDto.getId() );
        cluesDetailSensitiveExportOutDtos.setChannelName( newExportUserClusesOutDto.getChannelName() );
        cluesDetailSensitiveExportOutDtos.setRadioName( newExportUserClusesOutDto.getRadioName() );
        cluesDetailSensitiveExportOutDtos.setCreateSourcePlatformValue( newExportUserClusesOutDto.getCreateSourcePlatformValue() );
        cluesDetailSensitiveExportOutDtos.setActivityId1( newExportUserClusesOutDto.getActivityId1() );
        cluesDetailSensitiveExportOutDtos.setCreatCluesMethod( newExportUserClusesOutDto.getCreatCluesMethod() );
        cluesDetailSensitiveExportOutDtos.setActivityType( newExportUserClusesOutDto.getActivityType() );
        cluesDetailSensitiveExportOutDtos.setActivityName( newExportUserClusesOutDto.getActivityName() );
        cluesDetailSensitiveExportOutDtos.setIntegrateCode( newExportUserClusesOutDto.getIntegrateCode() );
        cluesDetailSensitiveExportOutDtos.setTemplateCode( newExportUserClusesOutDto.getTemplateCode() );
        cluesDetailSensitiveExportOutDtos.setIntegrateTitle( newExportUserClusesOutDto.getIntegrateTitle() );
        cluesDetailSensitiveExportOutDtos.setCustomerName( newExportUserClusesOutDto.getCustomerName() );
        cluesDetailSensitiveExportOutDtos.setGender( newExportUserClusesOutDto.getGender() );
        cluesDetailSensitiveExportOutDtos.setCustomerPhone( newExportUserClusesOutDto.getCustomerPhone() );
        cluesDetailSensitiveExportOutDtos.setWechatno( newExportUserClusesOutDto.getWechatno() );
        cluesDetailSensitiveExportOutDtos.setFundingTime( newExportUserClusesOutDto.getFundingTime() );
        cluesDetailSensitiveExportOutDtos.setDistrictValue( newExportUserClusesOutDto.getDistrictValue() );
        cluesDetailSensitiveExportOutDtos.setCompanyName( newExportUserClusesOutDto.getCompanyName() );
        cluesDetailSensitiveExportOutDtos.setDistributorName( newExportUserClusesOutDto.getDistributorName() );
        cluesDetailSensitiveExportOutDtos.setStroeCode( newExportUserClusesOutDto.getStroeCode() );
        cluesDetailSensitiveExportOutDtos.setStroeName( newExportUserClusesOutDto.getStroeName() );
        cluesDetailSensitiveExportOutDtos.setAbbreviation( newExportUserClusesOutDto.getAbbreviation() );
        cluesDetailSensitiveExportOutDtos.setStoreTypeName( newExportUserClusesOutDto.getStoreTypeName() );
        cluesDetailSensitiveExportOutDtos.setStoreSubChannelName( newExportUserClusesOutDto.getStoreSubChannelName() );
        cluesDetailSensitiveExportOutDtos.setStoreCategoryName( newExportUserClusesOutDto.getStoreCategoryName() );
        cluesDetailSensitiveExportOutDtos.setFullPathName( newExportUserClusesOutDto.getFullPathName() );
        cluesDetailSensitiveExportOutDtos.setCreateUserName( newExportUserClusesOutDto.getCreateUserName() );
        cluesDetailSensitiveExportOutDtos.setChargeCode( newExportUserClusesOutDto.getChargeCode() );
        cluesDetailSensitiveExportOutDtos.setSalesman( newExportUserClusesOutDto.getSalesman() );
        cluesDetailSensitiveExportOutDtos.setAssistUserCode( newExportUserClusesOutDto.getAssistUserCode() );
        cluesDetailSensitiveExportOutDtos.setAssistUserName( newExportUserClusesOutDto.getAssistUserName() );
        cluesDetailSensitiveExportOutDtos.setModifiedChargeUserDate( newExportUserClusesOutDto.getModifiedChargeUserDate() );
        cluesDetailSensitiveExportOutDtos.setOthers( newExportUserClusesOutDto.getOthers() );
        cluesDetailSensitiveExportOutDtos.setVisitStatus( newExportUserClusesOutDto.getVisitStatus() );
        cluesDetailSensitiveExportOutDtos.setVisitTime( newExportUserClusesOutDto.getVisitTime() );
        cluesDetailSensitiveExportOutDtos.setReviseTime( newExportUserClusesOutDto.getReviseTime() );
        cluesDetailSensitiveExportOutDtos.setFirstFollowTime( newExportUserClusesOutDto.getFirstFollowTime() );
        cluesDetailSensitiveExportOutDtos.setLastFollowTime( newExportUserClusesOutDto.getLastFollowTime() );
        cluesDetailSensitiveExportOutDtos.setOpenSeaflag( newExportUserClusesOutDto.getOpenSeaflag() );
        cluesDetailSensitiveExportOutDtos.setFrequencyOpenSeas( newExportUserClusesOutDto.getFrequencyOpenSeas() );
        cluesDetailSensitiveExportOutDtos.setFrontSalesman( newExportUserClusesOutDto.getFrontSalesman() );
        cluesDetailSensitiveExportOutDtos.setAuditTime( newExportUserClusesOutDto.getAuditTime() );
        cluesDetailSensitiveExportOutDtos.setFollowEfficiencyCategoryValue( newExportUserClusesOutDto.getFollowEfficiencyCategoryValue() );
        if ( newExportUserClusesOutDto.getFollowUpCount() != null ) {
            cluesDetailSensitiveExportOutDtos.setFollowUpCount( String.valueOf( newExportUserClusesOutDto.getFollowUpCount() ) );
        }
        cluesDetailSensitiveExportOutDtos.setArtificialFollowUpCount( newExportUserClusesOutDto.getArtificialFollowUpCount() );
        cluesDetailSensitiveExportOutDtos.setArtificialFollowUpFlag( newExportUserClusesOutDto.getArtificialFollowUpFlag() );
        cluesDetailSensitiveExportOutDtos.setRemark( newExportUserClusesOutDto.getRemark() );
        cluesDetailSensitiveExportOutDtos.setFreehandSketchingFlagName( newExportUserClusesOutDto.getFreehandSketchingFlagName() );
        cluesDetailSensitiveExportOutDtos.setKitchenDesignFlagName( newExportUserClusesOutDto.getKitchenDesignFlagName() );
        cluesDetailSensitiveExportOutDtos.setCostumeChangeReportFlagName( newExportUserClusesOutDto.getCostumeChangeReportFlagName() );
        cluesDetailSensitiveExportOutDtos.setStatus( newExportUserClusesOutDto.getStatus() );
        cluesDetailSensitiveExportOutDtos.setKeyWords( newExportUserClusesOutDto.getKeyWords() );
        cluesDetailSensitiveExportOutDtos.setRegularSubscriber( newExportUserClusesOutDto.getRegularSubscriber() );
        cluesDetailSensitiveExportOutDtos.setWithSingleOldUserCode( newExportUserClusesOutDto.getWithSingleOldUserCode() );
        cluesDetailSensitiveExportOutDtos.setWithSingleOldUserName( newExportUserClusesOutDto.getWithSingleOldUserName() );
        cluesDetailSensitiveExportOutDtos.setBuryPipe( newExportUserClusesOutDto.getBuryPipe() );
        cluesDetailSensitiveExportOutDtos.setIsFollowUp( newExportUserClusesOutDto.getIsFollowUp() );
        cluesDetailSensitiveExportOutDtos.setIsIntoStores( newExportUserClusesOutDto.getIsIntoStores() );
        cluesDetailSensitiveExportOutDtos.setIsDeal( newExportUserClusesOutDto.getIsDeal() );
        cluesDetailSensitiveExportOutDtos.setCsmOrderCode( newExportUserClusesOutDto.getCsmOrderCode() );
        cluesDetailSensitiveExportOutDtos.setServiceEngineerCsn( newExportUserClusesOutDto.getServiceEngineerCsn() );
        cluesDetailSensitiveExportOutDtos.setIsLostOrder( newExportUserClusesOutDto.getIsLostOrder() );
        cluesDetailSensitiveExportOutDtos.setLostOrderValue( newExportUserClusesOutDto.getLostOrderValue() );
        cluesDetailSensitiveExportOutDtos.setLostOrderSubValue( newExportUserClusesOutDto.getLostOrderSubValue() );
        cluesDetailSensitiveExportOutDtos.setSelfLackReviewStatusValue( newExportUserClusesOutDto.getSelfLackReviewStatusValue() );
        cluesDetailSensitiveExportOutDtos.setIsHouserholdsVisit( newExportUserClusesOutDto.getIsHouserholdsVisit() );
        cluesDetailSensitiveExportOutDtos.setIsComeDevise( newExportUserClusesOutDto.getIsComeDevise() );
        cluesDetailSensitiveExportOutDtos.setIsComeDeviseTime( newExportUserClusesOutDto.getIsComeDeviseTime() );
        cluesDetailSensitiveExportOutDtos.setAppointmentTime( newExportUserClusesOutDto.getAppointmentTime() );
        cluesDetailSensitiveExportOutDtos.setIsSceneInteractionValue( newExportUserClusesOutDto.getIsSceneInteractionValue() );
        cluesDetailSensitiveExportOutDtos.setIsSceneInteractionTime( newExportUserClusesOutDto.getIsSceneInteractionTime() );
        cluesDetailSensitiveExportOutDtos.setFlagInstallation( newExportUserClusesOutDto.getFlagInstallation() );
        cluesDetailSensitiveExportOutDtos.setFlagMoveIn( newExportUserClusesOutDto.getFlagMoveIn() );
        cluesDetailSensitiveExportOutDtos.setOrderEarnestFlagName( newExportUserClusesOutDto.getOrderEarnestFlagName() );
        cluesDetailSensitiveExportOutDtos.setCluesType( newExportUserClusesOutDto.getCluesType() );
        cluesDetailSensitiveExportOutDtos.setCluesSource( newExportUserClusesOutDto.getCluesSource() );
        cluesDetailSensitiveExportOutDtos.setCluesLevel( newExportUserClusesOutDto.getCluesLevel() );
        cluesDetailSensitiveExportOutDtos.setUrgencyFollowUp( newExportUserClusesOutDto.getUrgencyFollowUp() );
        cluesDetailSensitiveExportOutDtos.setUrgencyPurchase( newExportUserClusesOutDto.getUrgencyPurchase() );
        cluesDetailSensitiveExportOutDtos.setReviewStatus( newExportUserClusesOutDto.getReviewStatus() );
        cluesDetailSensitiveExportOutDtos.setFollowUpStatus( newExportUserClusesOutDto.getFollowUpStatus() );
        cluesDetailSensitiveExportOutDtos.setAuditStatus( newExportUserClusesOutDto.getAuditStatus() );
        cluesDetailSensitiveExportOutDtos.setIntentionProduct( newExportUserClusesOutDto.getIntentionProduct() );
        cluesDetailSensitiveExportOutDtos.setDecorateProgres( newExportUserClusesOutDto.getDecorateProgres() );
        cluesDetailSensitiveExportOutDtos.setTotalScore( newExportUserClusesOutDto.getTotalScore() );
        cluesDetailSensitiveExportOutDtos.setDecorateAndDesignerTypeName( newExportUserClusesOutDto.getDecorateAndDesignerTypeName() );
        cluesDetailSensitiveExportOutDtos.setDecorateAndDesignerCode( newExportUserClusesOutDto.getDecorateAndDesignerCode() );
        cluesDetailSensitiveExportOutDtos.setDecorateAndDesignerName( newExportUserClusesOutDto.getDecorateAndDesignerName() );
        cluesDetailSensitiveExportOutDtos.setDecorateAndDesignerPhone( newExportUserClusesOutDto.getDecorateAndDesignerPhone() );
        cluesDetailSensitiveExportOutDtos.setDesignerClubMemberGradeName( newExportUserClusesOutDto.getDesignerClubMemberGradeName() );
        cluesDetailSensitiveExportOutDtos.setDesignerClubMemberId( newExportUserClusesOutDto.getDesignerClubMemberId() );
        cluesDetailSensitiveExportOutDtos.setCommunityMemberJson( newExportUserClusesOutDto.getCommunityMemberJson() );
        cluesDetailSensitiveExportOutDtos.setIsMakeBargain( newExportUserClusesOutDto.getIsMakeBargain() );
        cluesDetailSensitiveExportOutDtos.setPayStatus( newExportUserClusesOutDto.getPayStatus() );
        cluesDetailSensitiveExportOutDtos.setMakeBargainProduct( newExportUserClusesOutDto.getMakeBargainProduct() );
        cluesDetailSensitiveExportOutDtos.setMakeBargainSystem( newExportUserClusesOutDto.getMakeBargainSystem() );
        cluesDetailSensitiveExportOutDtos.setPreferenceStatusName( newExportUserClusesOutDto.getPreferenceStatusName() );
        cluesDetailSensitiveExportOutDtos.setPreferenceNote( newExportUserClusesOutDto.getPreferenceNote() );
        cluesDetailSensitiveExportOutDtos.setDecorateType( newExportUserClusesOutDto.getDecorateType() );
        cluesDetailSensitiveExportOutDtos.setMakeRoomTime( newExportUserClusesOutDto.getMakeRoomTime() );
        cluesDetailSensitiveExportOutDtos.setInStoreTimeValue( newExportUserClusesOutDto.getInStoreTimeValue() );
        cluesDetailSensitiveExportOutDtos.setInStoreTypeValue( newExportUserClusesOutDto.getInStoreTypeValue() );
        cluesDetailSensitiveExportOutDtos.setRetentionMethodValue( newExportUserClusesOutDto.getRetentionMethodValue() );
        cluesDetailSensitiveExportOutDtos.setCustomerBringTypeValue( newExportUserClusesOutDto.getCustomerBringTypeValue() );
        cluesDetailSensitiveExportOutDtos.setIntroducerName( newExportUserClusesOutDto.getIntroducerName() );
        cluesDetailSensitiveExportOutDtos.setIntroducerPhone( newExportUserClusesOutDto.getIntroducerPhone() );
        cluesDetailSensitiveExportOutDtos.setCardNo( newExportUserClusesOutDto.getCardNo() );
        cluesDetailSensitiveExportOutDtos.setQywxRelationFlag( newExportUserClusesOutDto.getQywxRelationFlag() );
        cluesDetailSensitiveExportOutDtos.setFollowUpNoDate( newExportUserClusesOutDto.getFollowUpNoDate() );
        cluesDetailSensitiveExportOutDtos.setUnFollowDate( newExportUserClusesOutDto.getUnFollowDate() );
        if ( newExportUserClusesOutDto.getTeleConnectionCount() != null ) {
            cluesDetailSensitiveExportOutDtos.setTeleConnectionCount( String.valueOf( newExportUserClusesOutDto.getTeleConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getWeChatConnectionCount() != null ) {
            cluesDetailSensitiveExportOutDtos.setWeChatConnectionCount( String.valueOf( newExportUserClusesOutDto.getWeChatConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getVisitConnectionCount() != null ) {
            cluesDetailSensitiveExportOutDtos.setVisitConnectionCount( String.valueOf( newExportUserClusesOutDto.getVisitConnectionCount() ) );
        }
        if ( newExportUserClusesOutDto.getIntoShopConnectionCount() != null ) {
            cluesDetailSensitiveExportOutDtos.setIntoShopConnectionCount( String.valueOf( newExportUserClusesOutDto.getIntoShopConnectionCount() ) );
        }
        cluesDetailSensitiveExportOutDtos.setUtmSource( newExportUserClusesOutDto.getUtmSource() );
        cluesDetailSensitiveExportOutDtos.setHouseType( newExportUserClusesOutDto.getHouseType() );
        cluesDetailSensitiveExportOutDtos.setHouseRenovationType( newExportUserClusesOutDto.getHouseRenovationType() );
        cluesDetailSensitiveExportOutDtos.setVillage( newExportUserClusesOutDto.getVillage() );
        cluesDetailSensitiveExportOutDtos.setCpc( newExportUserClusesOutDto.getCpc() );
        cluesDetailSensitiveExportOutDtos.setBuilding( newExportUserClusesOutDto.getBuilding() );
        cluesDetailSensitiveExportOutDtos.setUnit( newExportUserClusesOutDto.getUnit() );
        cluesDetailSensitiveExportOutDtos.setHouseNumber( newExportUserClusesOutDto.getHouseNumber() );
        cluesDetailSensitiveExportOutDtos.setHouseModel( newExportUserClusesOutDto.getHouseModel() );
        cluesDetailSensitiveExportOutDtos.setHouseArea( newExportUserClusesOutDto.getHouseArea() );
        cluesDetailSensitiveExportOutDtos.setKitchenType( newExportUserClusesOutDto.getKitchenType() );
        cluesDetailSensitiveExportOutDtos.setKitchenArea( newExportUserClusesOutDto.getKitchenArea() );
        cluesDetailSensitiveExportOutDtos.setBudgetAccounting( newExportUserClusesOutDto.getBudgetAccounting() );
        cluesDetailSensitiveExportOutDtos.setInstallDateValue( newExportUserClusesOutDto.getInstallDateValue() );
        cluesDetailSensitiveExportOutDtos.setMoveinDateValue( newExportUserClusesOutDto.getMoveinDateValue() );
        cluesDetailSensitiveExportOutDtos.setMakeBargainCycle( newExportUserClusesOutDto.getMakeBargainCycle() );
        cluesDetailSensitiveExportOutDtos.setDyAdvertiseNickName( newExportUserClusesOutDto.getDyAdvertiseNickName() );
        cluesDetailSensitiveExportOutDtos.setDyCluesIds( newExportUserClusesOutDto.getDyCluesIds() );
        cluesDetailSensitiveExportOutDtos.setDyOrderIds( newExportUserClusesOutDto.getDyOrderIds() );
        cluesDetailSensitiveExportOutDtos.setUnionId( newExportUserClusesOutDto.getUnionId() );
        cluesDetailSensitiveExportOutDtos.setWxName( newExportUserClusesOutDto.getWxName() );

        return cluesDetailSensitiveExportOutDtos;
    }

    protected NewCluesFollowServiceDto newCluesFollowServiceVoToNewCluesFollowServiceDto(NewCluesFollowServiceVo newCluesFollowServiceVo) {
        if ( newCluesFollowServiceVo == null ) {
            return null;
        }

        NewCluesFollowServiceDto newCluesFollowServiceDto = new NewCluesFollowServiceDto();

        newCluesFollowServiceDto.setIndex( newCluesFollowServiceVo.getIndex() );
        newCluesFollowServiceDto.setId( newCluesFollowServiceVo.getId() );
        newCluesFollowServiceDto.setCustomerName( newCluesFollowServiceVo.getCustomerName() );
        newCluesFollowServiceDto.setDistrictValue( newCluesFollowServiceVo.getDistrictValue() );
        newCluesFollowServiceDto.setCompanyName( newCluesFollowServiceVo.getCompanyName() );
        newCluesFollowServiceDto.setStoreTypeName( newCluesFollowServiceVo.getStoreTypeName() );
        newCluesFollowServiceDto.setDistributorName( newCluesFollowServiceVo.getDistributorName() );
        newCluesFollowServiceDto.setChannelCategoryName( newCluesFollowServiceVo.getChannelCategoryName() );
        newCluesFollowServiceDto.setStroeCode( newCluesFollowServiceVo.getStroeCode() );
        newCluesFollowServiceDto.setStroeName( newCluesFollowServiceVo.getStroeName() );
        newCluesFollowServiceDto.setChargeCode( newCluesFollowServiceVo.getChargeCode() );
        newCluesFollowServiceDto.setSalesman( newCluesFollowServiceVo.getSalesman() );
        newCluesFollowServiceDto.setType( newCluesFollowServiceVo.getType() );
        newCluesFollowServiceDto.setFreehandSketchingFlagName( newCluesFollowServiceVo.getFreehandSketchingFlagName() );
        newCluesFollowServiceDto.setKitchenDesignFlagName( newCluesFollowServiceVo.getKitchenDesignFlagName() );
        newCluesFollowServiceDto.setCostumeChangeReportFlagName( newCluesFollowServiceVo.getCostumeChangeReportFlagName() );
        newCluesFollowServiceDto.setServiceAction( newCluesFollowServiceVo.getServiceAction() );
        newCluesFollowServiceDto.setContentText( newCluesFollowServiceVo.getContentText() );
        newCluesFollowServiceDto.setSourceType( newCluesFollowServiceVo.getSourceType() );
        newCluesFollowServiceDto.setChargeUserName( newCluesFollowServiceVo.getChargeUserName() );
        newCluesFollowServiceDto.setFollowDate( newCluesFollowServiceVo.getFollowDate() );
        newCluesFollowServiceDto.setAddress( newCluesFollowServiceVo.getAddress() );
        newCluesFollowServiceDto.setScore( newCluesFollowServiceVo.getScore() );
        newCluesFollowServiceDto.setCoverUrl1( newCluesFollowServiceVo.getCoverUrl1() );
        newCluesFollowServiceDto.setCoverUrl2( newCluesFollowServiceVo.getCoverUrl2() );
        newCluesFollowServiceDto.setCoverUrl3( newCluesFollowServiceVo.getCoverUrl3() );
        newCluesFollowServiceDto.setCoverUrl4( newCluesFollowServiceVo.getCoverUrl4() );
        newCluesFollowServiceDto.setCoverUrl5( newCluesFollowServiceVo.getCoverUrl5() );
        newCluesFollowServiceDto.setCoverUrl6( newCluesFollowServiceVo.getCoverUrl6() );
        newCluesFollowServiceDto.setCoverUrl7( newCluesFollowServiceVo.getCoverUrl7() );
        newCluesFollowServiceDto.setCoverUrl8( newCluesFollowServiceVo.getCoverUrl8() );
        newCluesFollowServiceDto.setCoverUrl9( newCluesFollowServiceVo.getCoverUrl9() );
        newCluesFollowServiceDto.setComeUrls( newCluesFollowServiceVo.getComeUrls() );
        newCluesFollowServiceDto.setFollowUpCount( newCluesFollowServiceVo.getFollowUpCount() );
        newCluesFollowServiceDto.setNextFollowUpTime( newCluesFollowServiceVo.getNextFollowUpTime() );
        newCluesFollowServiceDto.setIsComeDeviseReport( newCluesFollowServiceVo.getIsComeDeviseReport() );
        newCluesFollowServiceDto.setEvaluateStatus( newCluesFollowServiceVo.getEvaluateStatus() );
        newCluesFollowServiceDto.setEvaluateResultDesc( newCluesFollowServiceVo.getEvaluateResultDesc() );
        newCluesFollowServiceDto.setEvaluateUserName( newCluesFollowServiceVo.getEvaluateUserName() );
        newCluesFollowServiceDto.setEvaluateTime( newCluesFollowServiceVo.getEvaluateTime() );

        return newCluesFollowServiceDto;
    }
}
