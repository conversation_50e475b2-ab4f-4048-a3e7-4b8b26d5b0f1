package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesPortionExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.NewExportUserClusesOutDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:16+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CluesPortionMapperImpl implements CluesPortionMapper {

    @Override
    public List<CluesPortionExportOutDto> newExportToCluesPortionOutDto(List<NewExportUserClusesOutDto> newExportUserClusesOutDtos) {
        if ( newExportUserClusesOutDtos == null ) {
            return null;
        }

        List<CluesPortionExportOutDto> list = new ArrayList<CluesPortionExportOutDto>( newExportUserClusesOutDtos.size() );
        for ( NewExportUserClusesOutDto newExportUserClusesOutDto : newExportUserClusesOutDtos ) {
            list.add( newExportUserClusesOutDtoToCluesPortionExportOutDto( newExportUserClusesOutDto ) );
        }

        return list;
    }

    protected CluesPortionExportOutDto newExportUserClusesOutDtoToCluesPortionExportOutDto(NewExportUserClusesOutDto newExportUserClusesOutDto) {
        if ( newExportUserClusesOutDto == null ) {
            return null;
        }

        CluesPortionExportOutDto cluesPortionExportOutDto = new CluesPortionExportOutDto();

        cluesPortionExportOutDto.setIndex( newExportUserClusesOutDto.getIndex() );
        cluesPortionExportOutDto.setId( newExportUserClusesOutDto.getId() );
        cluesPortionExportOutDto.setChannelName( newExportUserClusesOutDto.getChannelName() );
        cluesPortionExportOutDto.setRadioName( newExportUserClusesOutDto.getRadioName() );
        cluesPortionExportOutDto.setActivityId1( newExportUserClusesOutDto.getActivityId1() );
        cluesPortionExportOutDto.setActivityType( newExportUserClusesOutDto.getActivityType() );
        cluesPortionExportOutDto.setActivityName( newExportUserClusesOutDto.getActivityName() );
        cluesPortionExportOutDto.setCustomerName( newExportUserClusesOutDto.getCustomerName() );
        cluesPortionExportOutDto.setGender( newExportUserClusesOutDto.getGender() );
        cluesPortionExportOutDto.setCustomerPhone( newExportUserClusesOutDto.getCustomerPhone() );
        cluesPortionExportOutDto.setWechatno( newExportUserClusesOutDto.getWechatno() );
        cluesPortionExportOutDto.setFundingTime( newExportUserClusesOutDto.getFundingTime() );
        cluesPortionExportOutDto.setDistrictValue( newExportUserClusesOutDto.getDistrictValue() );
        cluesPortionExportOutDto.setCompanyName( newExportUserClusesOutDto.getCompanyName() );
        cluesPortionExportOutDto.setStroeCode( newExportUserClusesOutDto.getStroeCode() );
        cluesPortionExportOutDto.setStroeName( newExportUserClusesOutDto.getStroeName() );
        cluesPortionExportOutDto.setStoreTypeName( newExportUserClusesOutDto.getStoreTypeName() );
        cluesPortionExportOutDto.setCreateUserName( newExportUserClusesOutDto.getCreateUserName() );
        cluesPortionExportOutDto.setSalesman( newExportUserClusesOutDto.getSalesman() );
        cluesPortionExportOutDto.setOthers( newExportUserClusesOutDto.getOthers() );
        cluesPortionExportOutDto.setVisitStatus( newExportUserClusesOutDto.getVisitStatus() );
        cluesPortionExportOutDto.setVisitTime( newExportUserClusesOutDto.getVisitTime() );
        cluesPortionExportOutDto.setReviseTime( newExportUserClusesOutDto.getReviseTime() );
        cluesPortionExportOutDto.setRemark( newExportUserClusesOutDto.getRemark() );
        cluesPortionExportOutDto.setStatus( newExportUserClusesOutDto.getStatus() );
        cluesPortionExportOutDto.setCluesType( newExportUserClusesOutDto.getCluesType() );
        cluesPortionExportOutDto.setCluesSource( newExportUserClusesOutDto.getCluesSource() );
        cluesPortionExportOutDto.setCluesLevel( newExportUserClusesOutDto.getCluesLevel() );
        cluesPortionExportOutDto.setReviewStatus( newExportUserClusesOutDto.getReviewStatus() );
        cluesPortionExportOutDto.setFollowUpStatus( newExportUserClusesOutDto.getFollowUpStatus() );
        cluesPortionExportOutDto.setLostOrderValue( newExportUserClusesOutDto.getLostOrderValue() );
        cluesPortionExportOutDto.setLostOrderSubValue( newExportUserClusesOutDto.getLostOrderSubValue() );
        cluesPortionExportOutDto.setSelfLackReviewStatusValue( newExportUserClusesOutDto.getSelfLackReviewStatusValue() );
        cluesPortionExportOutDto.setUtmSource( newExportUserClusesOutDto.getUtmSource() );

        return cluesPortionExportOutDto;
    }
}
