package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesActivityExportOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.CluesActivityOutDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:16+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CluesActivityMapperImpl implements CluesActivityMapper {

    @Override
    public List<CluesActivityExportOutDto> cluesActivityToExport(List<CluesActivityOutDto> cluesActivityOutDtos) {
        if ( cluesActivityOutDtos == null ) {
            return null;
        }

        List<CluesActivityExportOutDto> list = new ArrayList<CluesActivityExportOutDto>( cluesActivityOutDtos.size() );
        for ( CluesActivityOutDto cluesActivityOutDto : cluesActivityOutDtos ) {
            list.add( cluesActivityOutDtoToCluesActivityExportOutDto( cluesActivityOutDto ) );
        }

        return list;
    }

    protected CluesActivityExportOutDto cluesActivityOutDtoToCluesActivityExportOutDto(CluesActivityOutDto cluesActivityOutDto) {
        if ( cluesActivityOutDto == null ) {
            return null;
        }

        CluesActivityExportOutDto cluesActivityExportOutDto = new CluesActivityExportOutDto();

        cluesActivityExportOutDto.setIndex( cluesActivityOutDto.getIndex() );
        cluesActivityExportOutDto.setId( cluesActivityOutDto.getId() );
        cluesActivityExportOutDto.setCustomerName( cluesActivityOutDto.getCustomerName() );
        cluesActivityExportOutDto.setCustomerPhone( cluesActivityOutDto.getCustomerPhone() );
        cluesActivityExportOutDto.setAreaName( cluesActivityOutDto.getAreaName() );
        cluesActivityExportOutDto.setCompanyName( cluesActivityOutDto.getCompanyName() );
        cluesActivityExportOutDto.setDistributorName( cluesActivityOutDto.getDistributorName() );
        cluesActivityExportOutDto.setStroeCode( cluesActivityOutDto.getStroeCode() );
        cluesActivityExportOutDto.setStroeName( cluesActivityOutDto.getStroeName() );
        cluesActivityExportOutDto.setAbbreviation( cluesActivityOutDto.getAbbreviation() );
        cluesActivityExportOutDto.setFullPathName( cluesActivityOutDto.getFullPathName() );
        cluesActivityExportOutDto.setCreateUserName( cluesActivityOutDto.getCreateUserName() );
        cluesActivityExportOutDto.setChargeCode( cluesActivityOutDto.getChargeCode() );
        cluesActivityExportOutDto.setChargeUserName( cluesActivityOutDto.getChargeUserName() );
        cluesActivityExportOutDto.setActivityId( cluesActivityOutDto.getActivityId() );
        cluesActivityExportOutDto.setRecordType( cluesActivityOutDto.getRecordType() );
        cluesActivityExportOutDto.setActivityName( cluesActivityOutDto.getActivityName() );
        cluesActivityExportOutDto.setFundingTime( cluesActivityOutDto.getFundingTime() );

        return cluesActivityExportOutDto;
    }
}
