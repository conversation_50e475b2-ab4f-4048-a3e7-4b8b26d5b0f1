package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.entity.TAttributeLevelConfig;
import com.fotile.exportcenter.marketing.pojo.vo.SelectAllByFiledIdVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:16+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class AttributeLevelConfigMapperImpl implements AttributeLevelConfigMapper {

    @Override
    public SelectAllByFiledIdVO from(TAttributeLevelConfig tAttributeLevelConfig) {
        if ( tAttributeLevelConfig == null ) {
            return null;
        }

        SelectAllByFiledIdVO selectAllByFiledIdVO = new SelectAllByFiledIdVO();

        selectAllByFiledIdVO.setFieldId( tAttributeLevelConfig.getFieldId() );
        selectAllByFiledIdVO.setAttributeId( tAttributeLevelConfig.getAttributeId() );
        selectAllByFiledIdVO.setAttributeValue( tAttributeLevelConfig.getAttributeValue() );
        selectAllByFiledIdVO.setAttributeSort( tAttributeLevelConfig.getAttributeSort() );
        selectAllByFiledIdVO.setLevel( tAttributeLevelConfig.getLevel() );
        selectAllByFiledIdVO.setParentAttributeId( tAttributeLevelConfig.getParentAttributeId() );
        selectAllByFiledIdVO.setCategoryCode( tAttributeLevelConfig.getCategoryCode() );

        return selectAllByFiledIdVO;
    }

    @Override
    public List<SelectAllByFiledIdVO> from(List<TAttributeLevelConfig> tAttributeLevelConfigs) {
        if ( tAttributeLevelConfigs == null ) {
            return null;
        }

        List<SelectAllByFiledIdVO> list = new ArrayList<SelectAllByFiledIdVO>( tAttributeLevelConfigs.size() );
        for ( TAttributeLevelConfig tAttributeLevelConfig : tAttributeLevelConfigs ) {
            list.add( from( tAttributeLevelConfig ) );
        }

        return list;
    }
}
