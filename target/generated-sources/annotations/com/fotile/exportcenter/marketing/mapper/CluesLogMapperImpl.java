package com.fotile.exportcenter.marketing.mapper;

import com.fotile.exportcenter.marketing.pojo.dto.CluesPhoneLogListOutDTO;
import com.fotile.exportcenter.marketing.pojo.dto.OperatorLogListOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.SelectOperatorLogListOutDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T12:02:16+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CluesLogMapperImpl implements CluesLogMapper {

    @Override
    public List<OperatorLogListOutDto> operatorLogToCluesLogOutDto(List<SelectOperatorLogListOutDto> operatorLogListOutDtos) {
        if ( operatorLogListOutDtos == null ) {
            return null;
        }

        List<OperatorLogListOutDto> list = new ArrayList<OperatorLogListOutDto>( operatorLogListOutDtos.size() );
        for ( SelectOperatorLogListOutDto selectOperatorLogListOutDto : operatorLogListOutDtos ) {
            list.add( selectOperatorLogListOutDtoToOperatorLogListOutDto( selectOperatorLogListOutDto ) );
        }

        return list;
    }

    @Override
    public List<CluesPhoneLogListOutDTO> cluesPhoneLogListOutDTO(List<SelectOperatorLogListOutDto> operatorLogListOutDtos) {
        if ( operatorLogListOutDtos == null ) {
            return null;
        }

        List<CluesPhoneLogListOutDTO> list = new ArrayList<CluesPhoneLogListOutDTO>( operatorLogListOutDtos.size() );
        for ( SelectOperatorLogListOutDto selectOperatorLogListOutDto : operatorLogListOutDtos ) {
            list.add( selectOperatorLogListOutDtoToCluesPhoneLogListOutDTO( selectOperatorLogListOutDto ) );
        }

        return list;
    }

    protected OperatorLogListOutDto selectOperatorLogListOutDtoToOperatorLogListOutDto(SelectOperatorLogListOutDto selectOperatorLogListOutDto) {
        if ( selectOperatorLogListOutDto == null ) {
            return null;
        }

        OperatorLogListOutDto operatorLogListOutDto = new OperatorLogListOutDto();

        operatorLogListOutDto.setIndex( selectOperatorLogListOutDto.getIndex() );
        operatorLogListOutDto.setSourceId( selectOperatorLogListOutDto.getSourceId() );
        operatorLogListOutDto.setCustomerName( selectOperatorLogListOutDto.getCustomerName() );
        operatorLogListOutDto.setCustomerPhone( selectOperatorLogListOutDto.getCustomerPhone() );
        operatorLogListOutDto.setWechatno( selectOperatorLogListOutDto.getWechatno() );
        operatorLogListOutDto.setDistrictValue( selectOperatorLogListOutDto.getDistrictValue() );
        operatorLogListOutDto.setCompanyName( selectOperatorLogListOutDto.getCompanyName() );
        operatorLogListOutDto.setDistributorName( selectOperatorLogListOutDto.getDistributorName() );
        operatorLogListOutDto.setStroeCode( selectOperatorLogListOutDto.getStroeCode() );
        operatorLogListOutDto.setStoreName( selectOperatorLogListOutDto.getStoreName() );
        operatorLogListOutDto.setAbbreviation( selectOperatorLogListOutDto.getAbbreviation() );
        operatorLogListOutDto.setFullPathName( selectOperatorLogListOutDto.getFullPathName() );
        operatorLogListOutDto.setCreateUserName( selectOperatorLogListOutDto.getCreateUserName() );
        operatorLogListOutDto.setChargeCode( selectOperatorLogListOutDto.getChargeCode() );
        operatorLogListOutDto.setSalesMan( selectOperatorLogListOutDto.getSalesMan() );
        operatorLogListOutDto.setHouseType( selectOperatorLogListOutDto.getHouseType() );
        operatorLogListOutDto.setVillage( selectOperatorLogListOutDto.getVillage() );
        operatorLogListOutDto.setChargeUserName( selectOperatorLogListOutDto.getChargeUserName() );
        operatorLogListOutDto.setType( selectOperatorLogListOutDto.getType() );
        operatorLogListOutDto.setFollowDate( selectOperatorLogListOutDto.getFollowDate() );
        operatorLogListOutDto.setDescription( selectOperatorLogListOutDto.getDescription() );
        operatorLogListOutDto.setAppType( selectOperatorLogListOutDto.getAppType() );
        operatorLogListOutDto.setLengthOfStay( selectOperatorLogListOutDto.getLengthOfStay() );
        operatorLogListOutDto.setServiceAction( selectOperatorLogListOutDto.getServiceAction() );
        operatorLogListOutDto.setDecorateProgres( selectOperatorLogListOutDto.getDecorateProgres() );
        operatorLogListOutDto.setContentText( selectOperatorLogListOutDto.getContentText() );
        operatorLogListOutDto.setAddress( selectOperatorLogListOutDto.getAddress() );
        operatorLogListOutDto.setScore( selectOperatorLogListOutDto.getScore() );
        operatorLogListOutDto.setCoverUrl1( selectOperatorLogListOutDto.getCoverUrl1() );
        operatorLogListOutDto.setCoverUrl2( selectOperatorLogListOutDto.getCoverUrl2() );
        operatorLogListOutDto.setCoverUrl3( selectOperatorLogListOutDto.getCoverUrl3() );
        operatorLogListOutDto.setCoverUrl4( selectOperatorLogListOutDto.getCoverUrl4() );
        operatorLogListOutDto.setCoverUrl5( selectOperatorLogListOutDto.getCoverUrl5() );
        operatorLogListOutDto.setCoverUrl6( selectOperatorLogListOutDto.getCoverUrl6() );
        operatorLogListOutDto.setCoverUrl7( selectOperatorLogListOutDto.getCoverUrl7() );
        operatorLogListOutDto.setCoverUrl8( selectOperatorLogListOutDto.getCoverUrl8() );
        operatorLogListOutDto.setCoverUrl9( selectOperatorLogListOutDto.getCoverUrl9() );
        operatorLogListOutDto.setFollowUpStatus( selectOperatorLogListOutDto.getFollowUpStatus() );
        operatorLogListOutDto.setIsComeDevise( selectOperatorLogListOutDto.getIsComeDevise() );

        return operatorLogListOutDto;
    }

    protected CluesPhoneLogListOutDTO selectOperatorLogListOutDtoToCluesPhoneLogListOutDTO(SelectOperatorLogListOutDto selectOperatorLogListOutDto) {
        if ( selectOperatorLogListOutDto == null ) {
            return null;
        }

        CluesPhoneLogListOutDTO cluesPhoneLogListOutDTO = new CluesPhoneLogListOutDTO();

        cluesPhoneLogListOutDTO.setIndex( selectOperatorLogListOutDto.getIndex() );
        cluesPhoneLogListOutDTO.setSourceId( selectOperatorLogListOutDto.getSourceId() );
        cluesPhoneLogListOutDTO.setCustomerName( selectOperatorLogListOutDto.getCustomerName() );
        cluesPhoneLogListOutDTO.setDistrictValue( selectOperatorLogListOutDto.getDistrictValue() );
        cluesPhoneLogListOutDTO.setCompanyName( selectOperatorLogListOutDto.getCompanyName() );
        cluesPhoneLogListOutDTO.setStroeCode( selectOperatorLogListOutDto.getStroeCode() );
        cluesPhoneLogListOutDTO.setStoreName( selectOperatorLogListOutDto.getStoreName() );
        cluesPhoneLogListOutDTO.setChargeCode( selectOperatorLogListOutDto.getChargeCode() );
        cluesPhoneLogListOutDTO.setSalesMan( selectOperatorLogListOutDto.getSalesMan() );
        cluesPhoneLogListOutDTO.setDialchargeCode( selectOperatorLogListOutDto.getDialchargeCode() );
        cluesPhoneLogListOutDTO.setDialChargeUserName( selectOperatorLogListOutDto.getDialChargeUserName() );
        cluesPhoneLogListOutDTO.setCreatedDate( selectOperatorLogListOutDto.getCreatedDate() );
        cluesPhoneLogListOutDTO.setDialSource( selectOperatorLogListOutDto.getDialSource() );

        return cluesPhoneLogListOutDTO;
    }
}
